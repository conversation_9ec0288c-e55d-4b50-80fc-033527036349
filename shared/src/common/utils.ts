import { sha256 as sha256Text } from "js-sha256";
import stableObjectHash from 'stable-hash';

// This is sha256('flakiness.io').substring(0, 10)
const ID_SEPARATOR = `388c03a1c6`;

export function sha256(data: string | string[]): string {
  if (Array.isArray(data))
    return sha256Text(data.join(ID_SEPARATOR));
  return sha256Text(data);
}

export function sha256Object(obj: any): string {
  return sha256(stableObjectHash(obj));
}

/**
 * Brands a type by intersecting it with a type with a brand property based on
 * the provided brand string.
 */
export type Brand<T, Brand extends string> = T & {
  readonly [B in Brand as `__${B}_brand`]: never;
};

export function randomUUID(): string {
  return crypto.randomUUID();
}

export async function retryUntilDeadline<T>(operation: () => T, deadline: number): Promise<T> {
  const timeouts = [100, 200, 500, 500];
  const defaultTimeout = 1000;
  let lastError;
  while (Date.now() < deadline) {
    try {
      return await operation();
    } catch (e) {
      lastError = e;
      await new Promise(x => setTimeout(x, timeouts.shift() ?? defaultTimeout));
    }
  }
  throw lastError;
}

interface Measure {
  (title: string): void; // Just an example
  reset(): void;
  disableLog(): void;
  timestamp(): number;
  duration(): number;
}

export function measure(prefix?: string) {
  let timestamp = Date.now();
  if (prefix)
    prefix = prefix.trim() + ' ';
  else 
    prefix = '';

  let logEnabled = true;
  const m: Measure = (title: string) => {
    if (!logEnabled)
      return;
    const suffix = ': ' + (Date.now() - timestamp) + 'ms';
    console.log(prefix + title + suffix);
    timestamp = Date.now();
  }
  m.reset = () => {
    timestamp = Date.now();
  }
  m.disableLog = () => {
    logEnabled = false;
  }
  m.timestamp = () => timestamp;
  m.duration = () => Date.now() - timestamp;

  return m;
}


