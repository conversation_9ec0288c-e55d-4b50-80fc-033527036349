# Prod

As of Oct, 2024, the production deployment uses the following:
- Cloudflare R2 as S3-storage
- Supabase as Postgres
- Hetzner main server in US
- Backoffice Hetzner server in Germany (cheaper)

Flakiness.io service is distributed as a docker container.

Our deployment runs multiple processes of Flakiness.io service, each running in
its own mode and configured using the [systemd service files](./systemd). The
processes are:
- a single ["serving" process](./systemd/flakiness.service) which only "serves"
  requests, and doesn't do any compute.
- multiple ["builder" processes](./systemd/fbuilder@.service) which do not
  serve anything and are only concerned with processing job queues.

We use [spot](https://spot.umputun.dev/) to drive deployments.
The list of machines and all commands are defined in [spot.yml](./spot.yml).

As of Apr, 2025, the deployment is fully automated via github actions.
It should never be run manually.

## Scripts

- [`pgcli.sh`](./pgcli.sh): use PGCLI to connect to  the production postgres.
  However:
    * make sure to configure `prod` or `prodlocal` environments for it to work.
    * do not run **ANY** destroying operations against prod database.

