[Unit]
Description=Flakiness Builder %i
After=network.target

[Service]
User=root
Group=root
ExecStart=/usr/bin/docker run \
    --rm \
    --name fbuilder-%i \
    --user www-data \
    --network=host \
    -v /etc/flakiness/env:/etc/flakiness/env \
    --env NODE_ENV=production \
    --env TELEMETRY_PORT=300%i \
    --env FK_BUILDER_INDEX=%i \
    cr.flakiness.io/app:last-deploy \
    node --env-file=/etc/flakiness/env --enable-source-maps ./server/lib/units/builder_process.js
Restart=always
ExecStop=/usr/bin/docker stop fbuilder-%i
ExecStopPost=/usr/bin/docker rm fbuilder-%i
EnvironmentFile=/etc/flakiness/env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=fbuilder

[Install]
WantedBy=multi-user.target

