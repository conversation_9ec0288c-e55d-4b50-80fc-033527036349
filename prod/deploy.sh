#!/usr/bin/env bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

if ! command -v spot > /dev/null; then
  echo "ERROR: please install spot - https://spot.umputun.dev"
  exit 1
fi

if ! command -v op > /dev/null; then
  echo "ERROR: please install & authenticate 1Password CLI to fetch secrets"
  exit 1
fi

if [[ ! -e "./prodsshkey" ]]; then
  op read "op://prod/ssh/private key"?ssh-format=openssh > ./prodsshkey
fi

spot "$@"
