user: root
ssh_shell: /bin/bash
ssh_key: ./prodsshkey
local_shell: /bin/bash

targets:
  prod:
    hosts:
      - { host: "usa1.flakiness.io", user: "root" }
      - { host: "ger1.flakiness.io", user: "root" }
  backoffice:
    hosts:
      - { host: "backoffice.flakiness.io", user: "root" }

# list of tasks, i.e. commands to execute
tasks:

  - name: install-caddy
    targets: ['prod', 'backoffice']
    commands:
      - name: install caddy
        script: |
          set -euo pipefail
          sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https curl
          curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor --batch --yes -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
          curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
          sudo apt update
          sudo apt install caddy
        cond: "! command -v caddy"

  - name: install-docker
    targets: ['prod', 'backoffice']
    commands:
      - name: install docker
        script: |
          set -euo pipefail
          # Technically this is not the safest, but oh well.
          curl -fsSL https://get.docker.com | sh
        cond: "! command -v docker"

  - name: flakiness.io
    targets: ["prod"]
    commands:
      - name: fetch environment from 1Password
        script: |
          ../config/config prod
          ../config/config deploymentsecrets
          source ../.env.prod
          source ../.env.deploymentsecrets
          export FLAKINESS_LICENSE="${FLAKINESS_LICENSE}"
          export TELEMETRY_USER="${TELEMETRY_USER}"
          export TELEMETRY_PASSWORD="${TELEMETRY_PASSWORD}"
          export CF_ACCOUNT_ID="${CF_ACCOUNT_ID}"
          export CF_ZONE_ID="${CF_ZONE_ID}"
          export CF_API_TOKEN="${CF_API_TOKEN}"
        options: { local: true }

      - name: disable machine on Cloudflare
        script: |
          node ./cf.mjs api.$SPOT_REMOTE_ADDR disable
        options: { local: true }

      - name: configure caddy
        script: |
          set -euo pipefail

          CADDYFILE_PATH="/etc/caddy/Caddyfile"

          TELEMETRY_PWD_BCRYPT=$(caddy hash-password --plaintext "${TELEMETRY_PASSWORD}")
          # Write the Caddyfile to TLS-terminate to docker container
          cat > "$CADDYFILE_PATH" <<EOF
          {
            metrics {
              per_host
            }
          }
          api.$SPOT_REMOTE_ADDR {
              handle {
                @paths {
                  path /api/*
                  path /stripewebhook
                  path /login/*
                  path /logout
                  path /metrics
                }
                encode
                reverse_proxy @paths localhost:3000
              }

              handle_path /docs/* {
                root * /var/www/flakiness/web/dist/docs
                encode
                file_server
              }

              handle_path /docs {
                root * /var/www/flakiness/web/dist/docs
                encode
                file_server
              }

              handle {
                root * /var/www/flakiness/web/dist

                route {
                  try_files {path} /index.html
                  header /index.html Cache-Control "public, max-age=0, must-revalidate"
                }
                encode
                file_server
              }
          }

          b1.$SPOT_REMOTE_ADDR {
              basic_auth /metrics {
                $TELEMETRY_USER "${TELEMETRY_PWD_BCRYPT}"
              }
              reverse_proxy localhost:3001
          }

          b2.$SPOT_REMOTE_ADDR {
              basic_auth /metrics {
                $TELEMETRY_USER "${TELEMETRY_PWD_BCRYPT}"
              }
              reverse_proxy localhost:3002
          }

          m.$SPOT_REMOTE_ADDR {
            handle_path /metrics/http {
              basic_auth {
                $TELEMETRY_USER "${TELEMETRY_PWD_BCRYPT}"
              }
              metrics
            }

            handle_path /metrics/machine {
              basic_auth {
                $TELEMETRY_USER "${TELEMETRY_PWD_BCRYPT}"
              }
              rewrite * /metrics
              reverse_proxy localhost:9100
            }
          }
          EOF

          caddy validate --config "${CADDYFILE_PATH}"

          if systemctl is-active --quiet caddy; then
            systemctl reload caddy
          else
            systemctl start caddy
          fi

      - name: login docker & pull app container
        script: |
          echo $FLAKINESS_LICENSE | docker login -u degulabs --password-stdin cr.flakiness.io
          docker pull cr.flakiness.io/app:"${APP_VERSION:-latest}"
          docker tag cr.flakiness.io/app:"${APP_VERSION:-latest}" cr.flakiness.io/app:last-deploy

          # Extract static files folder from the container. We will serve them
          # separately.

          docker rm -f temp-container # cleanup
          docker create --name temp-container cr.flakiness.io/app:"${APP_VERSION:-latest}"
          FLAKINESS_WEB_PATH=/var/www/flakiness/web
          rm -rf "${FLAKINESS_WEB_PATH}/dist"
          mkdir -p "${FLAKINESS_WEB_PATH}"
          docker cp temp-container:"${FLAKINESS_WEB_PATH}/dist" "${FLAKINESS_WEB_PATH}"
          docker rm temp-container

      - name: stop all services and cleanup unit files
        script: |
          if [[ -f /etc/systemd/system/flakiness.service ]]; then
            systemctl stop flakiness 'fbuilder@*'
            systemctl disable flakiness 'fbuilder@*'
          fi
          rm -rf /etc/systemd/system/flakiness.service
          rm -rf /etc/systemd/system/fbuilder@.service
          rm -rf /etc/systemd/system/xnotify.service
          rm -rf /etc/flakiness/env

      - name: copy unit files and env file
        copy:
          - { "src": "systemd/flakiness.service", "dst": "/etc/systemd/system/flakiness.service" }
          - { "src": "systemd/fbuilder@.service", "dst": "/etc/systemd/system/fbuilder@.service" }
          - { "src": "../.env.prod", "dst": "/etc/flakiness/env", "mkdir": true }

      - name: change ownership of /etc/flakiness/env
        script: |
          chown www-data:www-data /etc/flakiness/env
          chmod 0400 /etc/flakiness/env

      - name: run database migration
        script: |
          /usr/bin/docker run \
            --rm \
            --name flakiness \
            --user www-data \
            --network=host \
            -v /etc/flakiness/env:/etc/flakiness/env \
            --env NODE_ENV=production \
            cr.flakiness.io/app:"${APP_VERSION:-latest}" \
            node --env-file=/etc/flakiness/env --enable-source-maps \
            ./server/lib/cli.js migrate-postgres-to-latest
      - name: Restart services
        script: |
          systemctl daemon-reload
          systemctl enable flakiness fbuilder@1 fbuilder@2
          systemctl start flakiness fbuilder@1 fbuilder@2

      - name: Install prometheus-node-exporter
        script: |
          apt update
          apt install prometheus-node-exporter
          systemctl enable prometheus-node-exporter
          systemctl start prometheus-node-exporter
          systemctl status prometheus-node-exporter
        cond: "! systemctl is-enabled prometheus-node-exporter"

      - name: wait for API server to start
        wait: {"cmd": "curl -s --fail localhost:3000", "timeout": "30s", "interval": "1s"}

      - name: enable machine on Cloudflare
        script: |
          node ./cf.mjs api.$SPOT_REMOTE_ADDR enable
        options: { local: true }

      - name: Purge Cloudflare caches
        script: |
          # Make the API call with the 'purge_everything' payload
          curl -X POST "https://api.cloudflare.com/client/v4/zones/${CF_ZONE_ID}/purge_cache" \
               -H "Authorization: Bearer ${CF_API_TOKEN}" \
               -H "Content-Type: application/json" \
               --data "{\"purge_everything\":true}"
        options: { local: true }

  - name: telemetry
    targets: ["backoffice"]
    commands:
      - name: fetch environment from 1Password
        script: |
          ../config/config prod
          ../config/config deploymentsecrets
          source ../.env.prod
          source ../.env.deploymentsecrets
          export TELEMETRY_USER="${TELEMETRY_USER}"
          export TELEMETRY_PASSWORD="${TELEMETRY_PASSWORD}"
        options: { local: true }

      - name: configure caddy
        script: |
          set -euo pipefail

          CADDYFILE_PATH="/etc/caddy/Caddyfile"
          TELEMETRY_PWD_BCRYPT=$(caddy hash-password --plaintext "${TELEMETRY_PASSWORD}")

          # Write the Caddyfile to TLS-terminate to docker container
          cat > "$CADDYFILE_PATH" <<EOF
          metrics.flakiness.io {
            reverse_proxy localhost:3000
          }
          prometheus.flakiness.io {
            basic_auth {
              $TELEMETRY_USER "${TELEMETRY_PWD_BCRYPT}"
            }
            reverse_proxy localhost:9090
          }
          EOF
          caddy validate --config "${CADDYFILE_PATH}"

          if systemctl is-active --quiet caddy; then
            systemctl reload caddy
          else
            systemctl start caddy
          fi

      - name: install prometheus & grafana
        script: |
          set -euo pipefail

          # Update package list and install necessary tools
          echo "Updating package list and installing necessary tools..."
          apt update
          apt install -y software-properties-common

          # Add Prometheus repository if not already added
          if ! grep -q "packages.grafana.com" /etc/apt/sources.list.d/*; then
            echo "Adding Prometheus APT repository..."
            curl https://packages.grafana.com/gpg.key | apt-key add -
            add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
            apt update
          else
            echo "Prometheus APT repository already exists, skipping..."
          fi

          # Install Prometheus
          if ! dpkg -l | grep -q prometheus; then
            echo "Installing Prometheus..."
            apt install -y prometheus
          else
            echo "Prometheus is already installed, skipping installation..."
          fi

          if ! dpkg -l | grep -q grafana; then
            echo "Installing Grafana..."
            apt-get install -y grafana
          else
            echo "Grafana is already installed, skipping installation..."
          fi

          # Start and enable the Prometheus service
          echo "Starting and enabling Prometheus service..."
          systemctl start prometheus grafana-server
          systemctl enable prometheus grafana-server

          # Confirm installation and service status
          echo "Prometheus installation complete. Checking status..."
          systemctl status prometheus
          systemctl status grafana-server

      - name: configure prometheus
        script: |
          set -euo pipefail

          PROMETHEUS_CONFIG="/etc/prometheus/prometheus.yml"
          cat > "$PROMETHEUS_CONFIG" <<EOF
          global:
            scrape_interval: 10s
            evaluation_interval: 1m

          scrape_configs:
            - job_name: api
              metrics_path: /metrics
              scheme: https
              basic_auth:
                username: $TELEMETRY_USER
                password: $TELEMETRY_PASSWORD
              static_configs:
                - targets:
                  - api.usa1.flakiness.io
                  - api.ger1.flakiness.io
            - job_name: builders
              metrics_path: /metrics
              scheme: https
              basic_auth:
                username: $TELEMETRY_USER
                password: $TELEMETRY_PASSWORD
              static_configs:
                - targets:
                  - b1.usa1.flakiness.io
                  - b2.usa1.flakiness.io
                  - b1.ger1.flakiness.io
                  - b2.ger1.flakiness.io
            - job_name: machines
              metrics_path: /metrics/machine
              scheme: https
              basic_auth:
                username: $TELEMETRY_USER
                password: $TELEMETRY_PASSWORD
              static_configs:
                - targets:
                  - m.usa1.flakiness.io
                  - m.ger1.flakiness.io
            - job_name: http
              metrics_path: /metrics/http
              scheme: https
              basic_auth:
                username: $TELEMETRY_USER
                password: $TELEMETRY_PASSWORD
              static_configs:
                - targets:
                  - m.usa1.flakiness.io
                  - m.ger1.flakiness.io
          EOF

          echo "Prometheus configuration updated successfully."

          # Restart Prometheus to apply the changes
          echo "Restarting Prometheus service..."
          sudo systemctl restart prometheus

  - name: backoffice
    targets: ["backoffice"]
    commands:
      - name: copy unit files and env file
        copy:
          - { "src": "systemd/pw-uploader.service", "dst": "/etc/systemd/system/pw-uploader.service" }
          - { "src": "systemd/pw-uploader.timer", "dst": "/etc/systemd/system/pw-uploader.timer" }
          - { "src": "../.env.prod", "dst": "/etc/flakiness/env", "mkdir": true }

      - name: install Node.js
        script: |
          apt-get update && apt-get install -y curl
          curl -fsSL https://deb.nodesource.com/setup_23.x -o nodesource_setup.sh
          bash nodesource_setup.sh
          apt-get install -y nodejs
          rm nodesource_setup.sh
        cond: "! command -v node"

      - name: install git
        script: |
          apt-get update && apt-get install -y git
        cond: "! command -v git"

      - name: clone or update repository
        script: |
          # Check if the target directory exists
          if [[ -d "/flakiness/.git" ]]; then
            git -C "/flakiness" pull
          else
            git clone "**************:flakiness/flakiness.git" "/flakiness"
          fi

      - name: install deps and build
        script: |
          cd /flakiness
          npm ci
          npx kubik --env-file /etc/flakiness/env ./experimental/build.mts

      - name: install & restart systemd service
        script: |
          systemctl daemon-reload
          systemctl enable pw-uploader.timer
          systemctl restart pw-uploader.timer
