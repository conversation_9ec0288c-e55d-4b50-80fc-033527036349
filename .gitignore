server/test-results
server/test-uploader-data-dir/
node_modules/
/test-results/
/tests/coverage-report
.local-browsers/
/.dev_profile*
.DS_Store
.env*
*.swp
*.pyc
.vscode
.idea
yarn.lock
/packages/playwright-core/src/generated/*
packages/*/lib/
drivers/
.android-sdk/
.gradle/
nohup.out
.trace
.tmp
allure*
blob-report
playwright-report
test-results.json
/demo/
/packages/*/LICENSE
/packages/*/NOTICE
/packages/playwright/README.md
/packages/playwright-test/README.md
/packages/playwright-core/api.json
.env
/tests/installation/output/
/tests/installation/.registry.json
.cache/
.eslintcache
prod/.env
prod/.localenv
