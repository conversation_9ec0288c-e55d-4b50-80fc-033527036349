#!/usr/bin/env npx kubik

import { Task } from 'kubik';
import esbuild from 'esbuild';
import fs from 'fs';
import path from 'path';

const { __dirname, $ } = Task.init(import.meta, {
  name: 'experimental',
  watch: [ './src', './package.json', 'tsconfig.json' ],
  deps: [
    '../server/build.mts',
  ],
});

const outDir = path.join(__dirname, 'lib');
const srcDir = path.join(__dirname, 'src');
const typesDir = path.join(__dirname, 'types');
await fs.promises.rm(outDir, { recursive: true, force: true });
await fs.promises.rm(typesDir, { recursive: true, force: true });

const { errors } = await esbuild.build({
  color: true,
  entryPoints: [
    path.join(srcDir, '**/*'),
  ],
  packages: 'external',
  outdir: outDir,
  format: 'esm',
  platform: 'node',
  target: ['node22'],
  sourcemap: true,
  bundle: false,
  minify: false,
});

if (!errors.length)
  await $`tsc --pretty -p .`;
