import { StatsBuilder } from '@flakiness/server/common/stats/statsBuilder.js';
import { TestIndex } from '@flakiness/server/common/stats/testIndex.js';
import { asyncBrotliCompress, gunzipAsync } from '@flakiness/server/node/nodeutils.js';
import fs from 'fs';
import path from 'path';
import url from 'url';
import { parseMicrosoftPlaywrightReport } from './utils.js';

// Convert the module's URL to a file path
const __filename = url.fileURLToPath(import.meta.url);
// Get the directory name of the current module
const __dirname = path.dirname(__filename);
const DATA_PATH = path.join(__dirname, '..', 'cached-commits-data');


async function addReports(stats: StatsBuilder, aPath: string) {
  const gzipped = await fs.promises.readFile(aPath);
  const data = await gunzipAsync(gzipped);
  const pwReports = JSON.parse(data.toString('utf-8'));

  for (let i = 0; i < pwReports.length; ++i) {
    const pwReport = pwReports[i];
    const report = await parseMicrosoftPlaywrightReport(pwReports[i]);
    stats.addReport(pwReport.metadata.uuid, report);
  }
  return { compressed: gzipped.length, uncompressed: data.length, reportsCount: pwReports.length };
}


const files = await fs.promises.readdir(DATA_PATH);
const reportPaths = files.filter(file => file.endsWith('.json.gz')).map(file => path.join(DATA_PATH, file));

const builder = StatsBuilder.create(new TestIndex());

let totalReports = 0;
for (let i = 0; i < 10; ++i) {
  const { compressed, uncompressed, reportsCount } = await addReports(builder, reportPaths[i]);
  const rawText = JSON.stringify(builder.jsonStats());
  const br = await asyncBrotliCompress(rawText);
  totalReports += reportsCount;
  console.log(`
#${i + 1} Commit: ${bytesToHuman(uncompressed)} (gzip: ${bytesToHuman(compressed)}) - ${reportsCount} reports
#${i + 1}  Stats: ${bytesToHuman(rawText.length)} (br: ${bytesToHuman(br.length)}) - ${totalReports} reports (br: ${bytesToHuman(br.length / totalReports)} / report)`);
}

await fs.promises.writeFile('/Users/<USER>/flakiness/server/a.json', JSON.stringify(builder.jsonStats()));

function bytesToHuman(bytes: number) {
  if (bytes < 1024)
    return bytes + ' B';
  bytes = bytes / 1024;
  if (bytes < 1024)
    return bytes.toFixed(2) + ' KB'
  bytes = bytes / 1024;
  return bytes.toFixed(2) + ' MB';
}



