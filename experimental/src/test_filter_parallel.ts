import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsFilter } from '@flakiness/server/common/stats/statsFilter.js';
import { StatsFilterResultMerger } from '@flakiness/server/common/stats/statsFilterMerger.js';
import { StatsReport } from '@flakiness/server/common/stats/statsReport.js';
import { measure } from '@flakiness/shared/common/utils.js';
import fs from 'fs';
import path from 'path';
import { setTimeout } from 'timers/promises';
import url from 'url';
import { Worker } from 'worker_threads';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const file = 
  '/Users/<USER>/flakiness/nov.json.br'
;

const commitIds = (await fs.promises.readFile('/Users/<USER>/flakiness/novcommits.txt', 'utf-8')).trim().split('\n').map(l => l.trim()) as Stats.CommitId[];


const workers: Worker[] = [];
for (let i = 0; i < 4; ++i) {
  const worker = new Worker(path.join(__dirname, './filter_worker.js'));
  // Handle worker errors
  worker.on('error', (error) => {
    console.error(`Worker ${i} error:`, error);
  });

  // Restart the worker if it stops unexpectedly
  worker.on('exit', (code) => {
    console.log(`worker ${i + 1} exit, code: ${code}`);
  });
  workers.push(worker);
  await new Promise(x => worker.once('message', x));
}


async function filterInWorker(worker: Worker, commitIds: string[], query: string): Promise<StatsFilter.TestsReport> {
  worker.postMessage({
    action: 'filter',
    commitIds,
    query,
    ts: Date.now(),
  });
  // return await new Promise<StatsFilter.MergeableResult>(resolve => {
  //   worker.once('message', ({ result, ts }) => {
  //     console.log(`worker -> main: ${Date.now() - ts}ms`);
  //     resolve(result);
  //   });
  // });
  return await new Promise<StatsFilter.TestsReport>(resolve => {
    worker.once('message', async ({ compressed, text, result, ts }) => {
      await setTimeout(0);
      const m = measure(`[main]`);
      console.log(`worker -> main: ${Date.now() - ts}ms`);
      // if (compressed) {
      //   text = gunzipSync(compressed).toString("utf-8");
      //   m(`gunzip`)
      // }
      // if (text) {
      //   m.reset();
      //   result = JSON.parse(text);
      //   m(`json.parse`);
      // }
      resolve(result);
    });
  });
}

async function parallelFilter(commitIds: Stats.CommitId[], query: string) {
  const K = Math.ceil(commitIds.length / workers.length);
  const results = await Promise.all(workers.map(async (worker, index) => {
    const result = await filterInWorker(worker, commitIds.slice(index * K, index * K + K), query);
    return result;
  }));
  const merger = new StatsFilterResultMerger();
  merger.mergeAll(results);
  return new StatsReport(merger.total(), []);
  // return new StatsPager(StatsFilter.mergeResults(results));
}

console.log(`Running against file: ${file}`);

// for (const query of ['click @os.name=ubuntu', '', '']) {
for (const query of ['']) {
  console.log(`---------`); 
  const m = measure();
  const result = await parallelFilter(commitIds, query);
  m(`filtered parallely: ${query}`);
}
console.log("exiting");
for (const worker of workers)
  worker.terminate();