/**
 * Regenerate new results with the following command:
 *   npm run perf -- --md > experimental/perf_filter_results.md
 */
import { Query } from '@flakiness/server/common/fql/query.js';
import { StatsFilter } from '@flakiness/server/common/stats/statsFilter.js';
import { StatsFilterResultMerger } from '@flakiness/server/common/stats/statsFilterMerger.js';
import { StatsReport } from '@flakiness/server/common/stats/statsReport.js';
import assert from 'assert';
import * as mitata from 'mitata';
import path from 'path';
import { Worker } from 'worker_threads';
import { downloadArtifactIsMissing, ensureStatsAndReportIndex, loadJSON } from './utils.js';

assert(process.env.TESTDATA_JSON_REPORTS_PER_COMMIT_URL);
const commitsPath = await downloadArtifactIsMissing(process.env.TESTDATA_JSON_REPORTS_PER_COMMIT_URL);
const commits = await loadJSON(commitsPath) as {
  commitId: string,
  url: string,
}[];

const { commitIds, stats } = await ensureStatsAndReportIndex(commits.slice(0, 10));
const filter = new StatsFilter.StatsFilter(stats);
const envs = filter.computeRunEnvironments(commitIds, [''], Query.parse(''));
const timelines = envs.map(env => Query.createForEnv(env).serialize());

let jsonRunCount = 0;
for (const commit of stats.commits) {
  if (!commitIds.includes(commit.commitId))
    continue;
  for (const report of commit.reports)
    jsonRunCount += report.runs.length;
}

const filterQueries = ['', 'page-click', '@webkit', '$expect', 'page-click @firefox'];

mitata.group(`statsFilter, cold, ${jsonRunCount} runs, ${commitIds.length} commits`, () => {
  mitata.bench(`q=$q`, function* (state: mitata.k_state) {
    const q = Query.parse(state.get('q'));

    yield () => {
      const filter = new StatsFilter.StatsFilter(stats);
      const result = filter.computeTestsReport(commitIds, timelines, q);
      mitata.do_not_optimize(result);
    }
  }).args({
    'q': filterQueries
  });
});

mitata.bench(`regressions, cold`, function* (state: mitata.k_state) {
  const q = Query.parse('');
  yield () => {
    const filter = new StatsFilter.StatsFilter(stats);
    const result = filter.computeRegressions(commitIds, timelines, q);
    mitata.do_not_optimize(result);
  }
});

mitata.bench(`chronologicalOutcomes, cold`, function* (state: mitata.k_state) {
  const q = Query.parse('');
  yield () => {
    const filter = new StatsFilter.StatsFilter(stats);
    const result = filter.computeChronologicalOutcomes(commitIds, timelines, q);
    mitata.do_not_optimize(result);
  }
});

mitata.group(`statsFilter, warm, ${jsonRunCount} runs, ${commitIds.length} commits`, () => {
  mitata.bench(`q=$q`, function* (state: mitata.k_state) {
    const filter = new StatsFilter.StatsFilter(stats);
    filter.computeTestsReport(commitIds, timelines, Query.parse(''));
    const q = Query.parse(state.get('q'));
    yield () => {
      const result = filter.computeTestsReport(commitIds, timelines, q);
      mitata.do_not_optimize(result);
    }
  }).args({
    'q': filterQueries
  });
});

mitata.bench(`statsMerger`, function* () {
  const filter = new StatsFilter.StatsFilter(stats);
  const result1 = filter.computeTestsReport(commitIds.slice(0, commitIds.length / 2), timelines, Query.parse(''));
  const result2 = filter.computeTestsReport(commitIds.slice(commitIds.length / 2), timelines, Query.parse(''));
  yield () => {
    const merger = new StatsFilterResultMerger();
    merger.mergeAll([result1, result2]);
    mitata.do_not_optimize(merger);
  }
});

mitata.bench(`StatsReport`, function* () {
  const filter = new StatsFilter.StatsFilter(stats);
  const result = filter.computeTestsReport(commitIds, timelines, Query.parse(''));
  yield () => {
    mitata.do_not_optimize(new StatsReport(result, []));
  }
});

mitata.bench(`statsFilter.transfer`, async function* () {
  const worker = new Worker(path.join(import.meta.dirname, 'echo_worker.js'));
  const filter = new StatsFilter.StatsFilter(stats);
  let result = filter.computeTestsReport(commitIds, timelines, Query.parse(''));
  await new Promise(x => worker.once('message', x));
  yield async () => {
    worker.postMessage(result, StatsFilter.transferList(result));
    result = await new Promise(x => worker.once('message', x));
  }
  worker.terminate();
});

const isMarkdown = process.argv.includes('md') || process.argv.includes('--md') || process.argv.includes('--markdown');

await mitata.run({
  // filter: /chronological/,
  format: isMarkdown ? 'markdown' : 'mitata',
});