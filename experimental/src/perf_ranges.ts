/**
 * Regenerate new results with the following command:
 *   node experimental/lib/perf_ranges.js --md > experimental/perf_ranges_results.md
 */
import { Ranges } from '@flakiness/server/common/ranges.js';
import * as mitata from 'mitata';

// mulberry32 
export function createRandom(seed: number) {
  return function() {
    let t = seed += 0x6D2B79F5;
    t = Math.imul(t ^ t >>> 15, t | 1);
    t ^= t + Math.imul(t ^ t >>> 7, t | 61);
    return ((t ^ t >>> 14) >>> 0);
  };
}

function randomList(rng: () => number, domain: number, count = rng() % (domain/4) + domain/2) {
  const numbers: number[] = [];
  for (let i = 0; i < count; ++i)
    numbers.push(rng() % domain);
  return numbers;
}

function randomRanges(rng: () => number, domain: number): Ranges.Ranges<number> {
  let i = 0;
  const ranges = [] as any as Ranges.Ranges<number>;
  while (i < domain) {
    const gap = rng() % 10;
    const length = rng() % 100;
    ranges.push(Math.min(i + gap, domain));
    ranges.push(Math.min(i + gap + length, domain));
    i += gap + length;
  }
  return ranges;
}

function shiftRanges(rng: () => number, domain: number, ranges: Ranges.Ranges<number>): Ranges.Ranges<number> {
  const result = [] as any as Ranges.Ranges<number>;
  for (let i = 0; i < ranges.length; i += 2) {
    const shift = rng() % (ranges[i + 1] - ranges[i]);

    result.push(Math.min(ranges[i] + shift, domain));
    result.push(Math.min(ranges[i + 1] + shift, domain));
  }
  return result;
}


// const rng = createRandom(777);
// const R1 = randomRanges(rng, 50000000);
// const R2 = shiftRanges(rng, 50000000, R1);
// console.log(Ranges.intersect(R1, R2).length, Ranges.union(R1, R2).length);

mitata.group(`Ranges.WeightedSum`, () => {
  mitata.bench(`seed=$seed, |$domain|`, function* (state: mitata.k_state) {
    const domain = state.get('domain');
    const rng = createRandom(state.get('seed'));
    // Create a set of 100 different ranges; each 
    const ranges = Array(100).fill(0).map(() => randomRanges(rng, domain));
    yield () => {
      const w = new Ranges.WeightedSum();
      for (const range of ranges)
        w.addRange(range, rng());
      mitata.do_not_optimize(w.list(Ranges.FULL));
    }
  }).args({
    seed: [777, 261239, 42],
    domain: [250000],
  });
});


mitata.group(`Ranges.fromList`, () => {
  mitata.bench(`seed=$seed, |$domain|`, function* (state: mitata.k_state) {
    const rng = createRandom(state.get('seed'));
    const list = randomList(rng, state.get('domain'));
    yield () => {
      mitata.do_not_optimize(Ranges.fromList(list));
    }
  }).args({
    seed: [777, 261239, 42],
    domain: [250000],
  });
});

mitata.group(`Ranges.intersect`, () => {
  mitata.bench(`seed=$seed, |$domain|`, function* (state: mitata.k_state) {
    const rng = createRandom(state.get('seed'));
    const domain = state.get('domain');
    const r1 = randomRanges(rng, domain);
    const r2 = shiftRanges(rng, domain, r1);

    yield () => {
      mitata.do_not_optimize(Ranges.intersect(r1, r2));
    }
  }).args({
    seed: [777, 261239, 42],
    domain: [50000000],
  });
})


mitata.group(`Ranges.union`, () => {
  mitata.bench(`seed=$seed, |$domain|`, function* (state: mitata.k_state) {
    const rng = createRandom(state.get('seed'));
    const domain = state.get('domain');
    const r1 = randomRanges(rng, domain);
    const r2 = shiftRanges(rng, domain, r1);
    yield () => {
      mitata.do_not_optimize(Ranges.union(r1, r2));
    }
  }).args({
    seed: [777, 261239, 42],
    domain: [500000],
  });
})

const isMarkdown = process.argv.includes('md') || process.argv.includes('--md') || process.argv.includes('--markdown');

await mitata.run({
  // filter: /^statsFilter/,
  // filter: /^statsMerger/,
  format: isMarkdown ? 'markdown' : 'mitata',
});