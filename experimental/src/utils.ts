import { FlakinessReport } from '@flakiness/report';
import { <PERSON>wrightJSONReport } from '@flakiness/report/playwright';
import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsBuilder } from '@flakiness/server/common/stats/statsBuilder.js';
import { TestIndex } from '@flakiness/server/common/stats/testIndex.js';
import { sha256 } from '@flakiness/shared/common/utils.js';
import { JSONReport } from '@playwright/test/reporter';
import assert from 'assert';
import fs from 'fs';
import path from 'path';
import { Readable } from 'stream';
import { finished } from 'stream/promises';
import { ReadableStream } from 'stream/web';
import { brotliDecompressSync, gunzipSync } from 'zlib';

const TEST_ARTIFACTS = path.join(import.meta.dirname, '..', '.flakiness-perf-artifacts');
console.log(`Using ${TEST_ARTIFACTS} to store test artifacts.`);

export async function downloadArtifactIsMissing(url: string) {
  const parsed = new URL(url);
  const filepath = path.join(TEST_ARTIFACTS, `__downloaded__`, parsed.pathname);
  if (fs.existsSync(filepath))
    return filepath;
  await fs.promises.mkdir(path.dirname(filepath), { recursive: true }).catch(e => {});
  const response = await fetch(url);
  if (!response.ok)
    throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
  const body = response.body as ReadableStream;
  if (!body)
    throw new Error(`response has no body`);
  await fs.promises.mkdir(path.dirname(filepath), { recursive: true });
  const fileStream = fs.createWriteStream(filepath);
  await finished(Readable.fromWeb(body).pipe(fileStream));
  return filepath;
}

export function artifactPath(name: string) {
  return path.join(TEST_ARTIFACTS, name);
}

const IS_ALMOST_POSIX_PATH = new RegExp("^[a-zA-Z]:/", "gi");

function findGitRoot(gitRoot: string) {
  const aPath = gitRoot.startsWith('/') || IS_ALMOST_POSIX_PATH.test(gitRoot) ? path.posix : path.win32;

  while (aPath.basename(gitRoot) !== 'tests')
    gitRoot = aPath.dirname(gitRoot);
  return aPath.dirname(gitRoot);
}

export type EnhancedJSONReport = JSONReport & {
  metadata: {
    gitRoot: string,
    uuid: string,
    osName: string,
    arch: string,
    osVersion: string,
    runURL?: string,
    commitTimestamp: string,
    commitSHA: string,
  }
};

export async function parseMicrosoftPlaywrightReport(pwReport: EnhancedJSONReport): Promise<FlakinessReport.Report> {
  const result = await PlaywrightJSONReport.parse(
    {
      arch: pwReport.metadata.arch,
      runURL: pwReport.metadata.runURL,
      commitId: pwReport.metadata.commitSHA,
      gitRoot: findGitRoot(pwReport.config.rootDir),
      osName: pwReport.metadata.osName,
      osVersion: pwReport.metadata.osVersion,
    }, pwReport, {
      extractAttachments: false,
    }
  );
  return result.report;
}

export async function loadJSON(filepath: string) {
  let bytes = await fs.promises.readFile(filepath);
  if (filepath.endsWith('.gz'))
    bytes = gunzipSync(bytes);
  else if (filepath.endsWith('.br'))
    bytes = brotliDecompressSync(bytes);
  return JSON.parse(bytes.toString('utf-8'));
}

export async function ensureStatsAndReportIndex(commits: {
  commitId: string,
  url: string,
}[]): Promise<{
  stats: Stats.JSONData,
  commitIds: Stats.CommitId[],
}> {
  let reportId = 0 as Stats.ReportId;
  const commitIds = commits.map(commit => commit.commitId as Stats.CommitId);
  const statsPath = artifactPath(`stats-${sha256(commitIds)}.json`);
  try {
    const [stats] = await Promise.all([
      await fs.promises.readFile(statsPath, 'utf-8').then(text => JSON.parse(text) as Stats.JSONData),
    ]);
    assert(stats.version === Stats.STATS_VERSION);
    return { stats, commitIds };
  } catch (e) {
    // if versions mismatch or if files are missing, then re-build everything.
    console.log(`stats and project info are missing - rebuilding for ${commitIds.length} commits`);
    const testIndex = new TestIndex();
    const statsBuilder = StatsBuilder.create(testIndex);

    for (let i = 0; i < commits.length; ++i) {
      const commitId = commits[i].commitId;
      const url = commits[i].url;

      console.log(`commit ${i + 1}/${commitIds.length} ${commitId.substring(0, 7)}`);
      console.time(`- downloaded`);
      const filepath = await downloadArtifactIsMissing(url);
      console.timeEnd(`- downloaded`);
      console.time(`- parsed`);
      const pwReports = await loadJSON(filepath).catch(e => {
        console.error(`error while loading json: ${filepath}`);
        console.error(e);
        return [];
      }) as EnhancedJSONReport[];
      console.timeEnd(`- parsed`);
      console.time(`- processed ${pwReports.length} reports`);
      for (const pwReport of pwReports) {
        const fkReport = await parseMicrosoftPlaywrightReport(pwReport);
        testIndex.addReport(fkReport);
        statsBuilder.addReport(++reportId as Stats.ReportId, fkReport);
      }
      console.timeEnd(`- processed ${pwReports.length} reports`);
    }

    const stats = statsBuilder.jsonStats();
    await Promise.all([
      fs.promises.writeFile(statsPath, JSON.stringify(stats)),
    ]);
    return { stats, commitIds };
  }
}