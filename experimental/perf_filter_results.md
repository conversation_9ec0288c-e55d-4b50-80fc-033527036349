
> flakiness@0.13.0 perf
> node --enable-source-maps --env-file=.env.dev experimental/lib/perf_filter.js --md

Using /tmp/flakiness-test-artifacts to store test artifacts.
clk: ~3.01 GHz
cpu: Apple M1 Max
runtime: node 22.11.0 (arm64-darwin)

| • statsFilter, cold, 5786 runs, 10 commits |              avg |         min |         p75 |         p99 |         max |
| --------------------- | ---------------- | ----------- | ----------- | ----------- | ----------- |
| q=                    | `501.21 ms/iter` | `458.68 ms` | `514.28 ms` | `515.87 ms` | `563.13 ms` |
| q=page-click          | `106.92 ms/iter` | ` 98.85 ms` | `108.34 ms` | `114.68 ms` | `130.83 ms` |
| q=@webkit             | ` 92.11 ms/iter` | ` 87.97 ms` | ` 92.57 ms` | ` 96.55 ms` | `103.93 ms` |
| q=$expect             | `220.39 ms/iter` | `210.14 ms` | `225.74 ms` | `228.91 ms` | `233.47 ms` |
| q=page-click @firefox | ` 32.50 ms/iter` | ` 31.39 ms` | ` 33.07 ms` | ` 33.49 ms` | ` 34.86 ms` |

|                             |              avg |         min |         p75 |         p99 |         max |
| --------------------------- | ---------------- | ----------- | ----------- | ----------- | ----------- |
| regressions, cold           | ` 33.87 ms/iter` | ` 32.48 ms` | ` 33.88 ms` | ` 36.44 ms` | ` 36.89 ms` |
| chronologicalOutcomes, cold | ` 28.92 ms/iter` | ` 27.44 ms` | ` 29.95 ms` | ` 31.11 ms` | ` 31.24 ms` |

| • statsFilter, warm, 5786 runs, 10 commits |              avg |         min |         p75 |         p99 |         max |
| --------------------- | ---------------- | ----------- | ----------- | ----------- | ----------- |
| q=                    | `380.44 ms/iter` | `357.90 ms` | `388.70 ms` | `392.55 ms` | `410.97 ms` |
| q=page-click          | ` 57.62 ms/iter` | ` 52.52 ms` | ` 56.99 ms` | ` 65.27 ms` | ` 67.82 ms` |
| q=@webkit             | ` 68.69 ms/iter` | ` 65.12 ms` | ` 68.56 ms` | ` 76.90 ms` | ` 77.52 ms` |
| q=$expect             | `163.10 ms/iter` | `155.38 ms` | `168.58 ms` | `171.82 ms` | `173.34 ms` |
| q=page-click @firefox | ` 15.25 ms/iter` | ` 13.82 ms` | ` 15.08 ms` | ` 19.64 ms` | ` 22.48 ms` |

|                      |              avg |         min |         p75 |         p99 |         max |
| -------------------- | ---------------- | ----------- | ----------- | ----------- | ----------- |
| statsMerger          | ` 14.98 ms/iter` | ` 12.77 ms` | ` 15.33 ms` | ` 22.81 ms` | ` 22.96 ms` |
| StatsReport          | `  7.90 ms/iter` | `  6.40 ms` | `  8.24 ms` | `  9.42 ms` | `  9.89 ms` |
| statsFilter.transfer | ` 64.06 ms/iter` | ` 60.46 ms` | ` 65.98 ms` | ` 66.71 ms` | ` 68.35 ms` |
