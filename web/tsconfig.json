{"exclude": ["dist"], "include": ["src"], "compilerOptions": {"experimentalDecorators": true, "useDefineForClassFields": false, "noEmit": true, "allowJs": true, "checkJs": true, "target": "ESNext", "strict": true, "allowSyntheticDefaultImports": true, "module": "NodeNext", "moduleResolution": "nodenext", "resolveJsonModule": true, "lib": ["esnext", "DOM", "DOM.Iterable"], "incremental": true, "tsBuildInfoFile": "./.tsconfig.tsbuildinfo", "types": []}, "references": [{"path": "../server"}, {"path": "../shared"}, {"path": "../report"}]}