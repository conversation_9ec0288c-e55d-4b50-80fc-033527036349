import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { ContextType } from '@lit/context';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';

@customElement('page-organization-billing')
export class PageOrganizationBilling extends LitElement {

  static url(options: { orgSlug: string }): string {
    return new URL(['', options.orgSlug, 'billing'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/billing',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
      }, html`
        <page-organization-billing
          .orgSlug=${groups?.org}
        ></page-organization-billing>
      `),
    }];
  }

  @consume({ context: contexts.org, subscribe: true }) private _org?: ContextType<typeof contexts.org>;
  @property({ attribute: false }) orgSlug?: string;

  render() {
    if (this._org && !this._org.access)
      return html`<page-http-error code=403></page-http-error>`;

    return html`
      <org-header .orgSlug=${this.orgSlug} submenu=billing></org-header>
      <app-body user-role=${this._org?.access}>
        <org-alerts .org=${this._org}></org-alerts>
        <h1>Billing & Subscription</h1>
        <product-plans
          .orgSlug=${this.orgSlug}
        ></product-plans>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`

  `];
}
