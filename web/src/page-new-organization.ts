import { css, html, LitElement } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { api } from './api.js';

import { ContextType } from '@lit/context';
import slugify from 'slugify';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { OrgNamePicked } from './components/pick-org-name.js';
import { consume, contexts, TaskEvent } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { PageOrganizationBilling } from './page-organization-billing.js';
import { PageOrganization } from './page-organization.js';
import { RouteConfig } from './router.js';

@customElement('page-new-organization')
export class PageNewOrganization extends LitElement {

  static url(): string {
    return new URL('/new-organization', window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [ '/new-organization', ],
      render: () => html`<page-new-organization></page-new-organization>`,
    }];
  }

  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  @state() private _projectNameResult?: TaskEvent<OrgNamePicked>;

  render() {
    if (!this._user)
      return;

    return html`
      <app-header .customTitle=${"New Organization"}></app-header>
      <app-body>
        <form @submit=${this._submitForm}>
          <h1>Create a new organization</h1>
          <div class=org-details>Organization is a group of projects.</div>
          
          <sl-divider></sl-divider>
          <i class=required-fields-note>Required fields are marked with an asterisk (*).</i>
          
          <!-- Org name container -->

          <pick-org-name
            @org-name=${(event: TaskEvent<OrgNamePicked>) => this._projectNameResult = event }
          ></pick-org-name>

          <sl-divider></sl-divider>

          <footer>
            <sl-button ?disabled=${!this._formData()} variant=success type=submit>Create organization</sl-button>
          </footer>

        </form>
      </app-body>
      <app-footer></app-footer>
    `
  }

  private _formData() {
    const orgName = this._projectNameResult?.data?.orgName;
    return orgName ? { orgName } : undefined;
  }

  private async _submitForm(event: SubmitEvent) {
    event.preventDefault();

    const formData = this._formData();

    if (!formData)
      return;

    const orgSlug = slugify.default(formData.orgName);
    await api.user.createOrganization.query({
      orgName: formData.orgName,
      orgSlug,
    });

    if (this._serverInfo?.enabledBilling)
      this._router?.navigate(PageOrganizationBilling.url({ orgSlug }));
    else
      this._router?.navigate(PageOrganization.url({ orgSlug }));
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    footer {
      display: grid;
      grid-template-columns: 1fr auto;

      & sl-button {
        grid-column: 2;
      }
    }

    h1 { margin-bottom: 0; }

    .project-details {
      color: var(--sl-color-neutral-400);
    }

    sl-tab-panel::part(base) {
      padding: 0;
    }
  `];
}
