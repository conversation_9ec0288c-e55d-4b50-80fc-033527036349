import { Query } from '@flakiness/server/common/fql/query.js';
import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Context, ContextProvider, createContext } from '@lit/context';
import { Task, TaskConfig } from '@lit/task';
import { ReactiveElement } from 'lit';

import { Router } from './router.js';
export { consume, provide } from '@lit/context';

export const contexts = {
  serverInfo: createContext<WireTypes.ServerInfo>(Symbol('serverInfo')),
  user: createContext<WireTypes.User>(Symbol('user')),
  org: createContext<WireTypes.Organization>(Symbol('org')),
  orgMembers: createContext<{ owner: WireTypes.User, members: WireTypes.OrgMember[]}>(Symbol('orgMembers')),
  project: createContext<WireTypes.Project>(Symbol('project')),
  projectCollaborators: createContext<WireTypes.ProjectCollaborator[]>(Symbol('projectCollaborator')),
  projectReferences: createContext<{
    branches: WireTypes.Ref[],
    defaultBranch: WireTypes.Ref,
  }>(Symbol('projectReferences')),
  router: createContext<Router>(Symbol('router')),  
  fql: createContext<Query>(Symbol('fql')),

  linkFQL: createContext<LinkRenderer<string>>(Symbol('link-fql')),
  linkTimeline: createContext<LinkRenderer<string>>(Symbol('link-timeline')),
  linkTest: createContext<LinkRenderer<WireTypes.TestStats>>(Symbol('link-test')),
}

export interface LinkRenderer<Element> {
  render(element: Element): string | undefined,
}

export class ContextTask<T extends ReadonlyArray<unknown> = ReadonlyArray<unknown>, Element = unknown, R = LinkRenderer<Element>> extends Task<T, R> {
  private _setContextTask: Task<any, any>;
  constructor(host: ReactiveElement, context: Context<unknown, R|undefined>, task: TaskConfig<T, R>) {
    super(host, task);

    const contextProvider = new ContextProvider(host, { context });
    this._setContextTask = new Task(host, {
      args: () => [this.value],
      task: async ([value], { signal }) => {
        contextProvider.setValue(value);
      },
    })
  }
}

export class TaskEvent<T> extends Event {
  constructor(eventName: string, public status: 'complete'|'error'|'pending', public data?: T) {
    super(eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
  }
}

export class EventTask<T extends ReadonlyArray<unknown> = ReadonlyArray<unknown>, R = unknown, > extends Task<T, R> {
  constructor(host: ReactiveElement, eventName: string, taskConfig: TaskConfig<T, R>) {
    super(host, {
      ...taskConfig,
      task: async (...args) => {
        host.dispatchEvent(new TaskEvent(eventName, 'pending'));
        try {
          const result = await taskConfig.task.apply(null, args);
          host.dispatchEvent(new TaskEvent(eventName, 'complete', result));
          return result;
        } catch (e) {
          host.dispatchEvent(new TaskEvent(eventName, 'error'));
          throw e; 
        }
      }
    });
  }
}

export class FKProjectChanged extends Event {
  static readonly eventName = 'fk-project-changed';
  readonly projectSlug: string;
  readonly orgSlug: string;

  constructor(options: { orgSlug: string, projectSlug: string }) {
    super(FKProjectChanged.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.orgSlug =options.orgSlug;
    this.projectSlug = options.projectSlug;
  }
}

export class FKOrgChanged extends Event {
  static readonly eventName = 'fk-org-changed';
  readonly orgSlug: string;

  constructor(options: { orgSlug: string }) {
    super(FKOrgChanged.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.orgSlug =options.orgSlug;
  }
}
