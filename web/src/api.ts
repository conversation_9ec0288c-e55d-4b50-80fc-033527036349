import type { AppRouter } from '@flakiness/server/node/api.js';
import { createTRPCProxyClient, httpBatchLink, httpLink } from '@trpc/client';

export const api = createTRPCProxyClient<AppRouter>({
  links: [
    httpBatchLink({
      url: window.location.origin + '/api',
    }),
  ],
});

(window as any)['api'] = api;

export const apiNoBatch = createTRPCProxyClient<AppRouter>({
  links: [
    httpLink({
      url: window.location.origin + '/api',
    }),
  ],
});
