import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { ContextType } from '@lit/context';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';
import { tasks } from './tasks.js';


@customElement('page-organization-usage')
export class PageOrganizationUsage extends LitElement {

  static url(options: { orgSlug: string, tab?: 'billing'|'general'|'members' }): string {
    return ['', options.orgSlug, 'usage'].join('/');
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/usage',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
      }, html`
        <page-organization-usage
          .orgSlug=${groups?.org}
        ></page-organization-usage>
      `),
    }];
  }

  @consume({ context: contexts.org, subscribe: true }) private _org?: ContextType<typeof contexts.org>;

  @property({ attribute: false }) orgSlug?: string;

  private _billingTask = tasks.billing(this, {
    orgSlug: () => this.orgSlug,
  });

  render() {
    if (this._org && !this._org.access)
      return html`<page-http-error code=403></page-http-error>`;

    return html`
      <org-header .orgSlug=${this.orgSlug} submenu=usage></org-header>
      <app-body user-role=${this._org?.access}>
        <org-alerts .org=${this._org}></org-alerts>
        <h1>Organization Usage</h1>
        <usage-metrics .orgSlug=${this.orgSlug} .days=${this._billingTask.value?.subscription?.maxDataRetentionDays}></usage-metrics>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
  `];
}
