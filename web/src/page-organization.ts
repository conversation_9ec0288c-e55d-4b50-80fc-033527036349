import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { ContextType } from '@lit/context';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { PageNewProject } from './page-new-project.js';
import { RouteConfig } from './router.js';
import { tasks } from './tasks.js';

@customElement('page-organization')
export class PageOrganization extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [ '/:org', '/:org/' ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
      }, html`
        <page-organization .orgSlug=${groups?.org}></page-organization>
      `),
    }];
  }

  static url(options: { orgSlug: string }): string {
    return new URL(`/${options.orgSlug}`, window.location.href).href;
  }

  @property({ attribute: false }) orgSlug?: string;

  @consume({ context: contexts.org, subscribe: true })
  private _org?: ContextType<typeof contexts.org>;

  private _projectsTask = tasks.orgProjects(this, {
    orgSlug: () => this._org?.orgSlug,
  });

  render() {
    if (!this.orgSlug)
      return nothing;
    return html`
      <org-header .orgSlug=${this.orgSlug} submenu=projects></org-header>
      <app-body>
        <org-alerts .org=${this._org}></org-alerts>
        <h-box>
          <div class=title>Projects</div>
          <x-filler></x-filler>
          ${this._org?.access && ['owner', 'admin'].includes(this._org.access) ? html`
            <sl-button
              size=small
              ?disabled=${!!this._org?.restrictedProjectAccess}
              href=${PageNewProject.url({ orgSlug: this.orgSlug })}
              type=submit
              class=new-project-button
              variant=success
            >
              <sl-icon slot="prefix" name="plus-circle"></sl-icon>
              New Project
            </sl-button>
          ` : nothing}
        </h-box>

        ${this._projectsTask.render({
          pending: () => html`<sl-spinner></sl-spinner>`,
          complete: projects => projects.length ? html`
            <list-projects .projects=${projects}></list-projects>
          `
          : this._org?.access ? html`
            <div class=no-items>This organization has no projects.</div>
          ` : html`
            <div class=no-items>This organization has no public projects.</div>
          `
        })}
      </app-body>
      <app-footer></app-footer>
    `;
  }

  static styles = [linkStyles, pageStyles, css`
    .new-project-button {
      width: fit-content;
      grid-column: 2;
    }

    a {
      color: var(--sl-color-neutral-900);
      text-decoration: none;
    }

    .title {
      font-size: var(--sl-font-size-2x-large);
    }

    .no-items {
      color: var(--sl-color-neutral-400);
      font-size: var(--sl-font-size-2x-large);
      display: flex;
      justify-content: center;
      margin-top: var(--sl-spacing-4x-large);
    }
  `];
}
