import { css, html, LitElement } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { api } from './api.js';

import { ContextType } from '@lit/context';
import slugify from 'slugify';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { GithubInstallation } from './components/pick-github-installation.js';
import { GithubPat } from './components/pick-github-pat.js';
import { ProjectNamePicked } from './components/pick-project-name.js';
import { PickProjectVisibility } from './components/pick-project-visibility.js';
import { consume, contexts, TaskEvent } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { PageReport } from './page-report.js';
import { RouteConfig } from './router.js';

function allOrNothing<T extends Record<string, any>>(obj: Partial<T>): Required<T> | undefined {
  for (const [key, value] of Object.entries(obj)) {
    if (!value)
      return undefined;
  }
  return obj as Required<T>;
}

@customElement('page-new-project')
export class PageNewProject extends LitElement {

  static url(options: { orgSlug: string }): string {
    return new URL(`/${options.orgSlug}/new-project`, window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [ '/:org/new-project', ],
      render: (groups) => html`<page-new-project .orgSlug=${groups?.org}></page-new-project>`,
    }];
  }

  @property({ attribute: false }) orgSlug?: string;
  @query('pick-project-visibility') private _visibility?: PickProjectVisibility;

  @consume({ context: contexts.user }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router }) private _router?: ContextType<typeof contexts.router>;

  render() {
    if (!this._user)
      return;

    return html`
      <app-header .customTitle=${"New Project"}></app-header>
      <app-body>
        <form @submit=${this._submitForm}>
          <h1>Create a new project</h1>
          <div class=project-details>Project is connected to a source code and collects all test reports.</div>
          
          <sl-divider></sl-divider>
          
          <!-- Project name container -->

          <form-section label="Project name">
            <pick-project-name
              .orgSlug=${this.orgSlug}
              @project-name=${this._onProjectNamePicked}
            ></pick-project-name>
          </form-section>

          <form-section label="Project visibility">
            <pick-project-visibility>
            </pick-project-visibility>
          </form-section>

          <!-- Project source  container -->
          <form-section label="Source code integration">
            <sl-tab-group>
              <sl-tab slot="nav" panel="app">Github App</sl-tab>
              <sl-tab slot="nav" panel="pat">Github Personal Access Token</sl-tab>

              <sl-tab-panel name="app">
                <pick-github-installation @github-installation=${this._onSourceGithubInstallation}></pick-github-installation>
              </sl-tab-panel>
              <sl-tab-panel name="pat">
                <pick-github-pat @github-pat=${this._onSourceGithubPat}></pick-github-pat>
              </sl-tab-panel>
            </sl-tab-group>
          </form-section>

          <sl-divider></sl-divider>

          <footer>
            <sl-button ?disabled=${!this._formData()} variant=success type=submit>Create project</sl-button>
          </footer>

        </form>
      </app-body>
      <app-footer></app-footer>
    `
  }

  private _formData() {
    const githubPat = allOrNothing({
      accessToken: this._githubPatResult?.data?.accessToken,
      repo: this._githubPatResult?.data?.repo,
      owner: this._githubPatResult?.data?.owner,
    });

    const githubApp = allOrNothing({
      repo: this._githubInstallationResult?.data?.repo,
      owner: this._githubInstallationResult?.data?.owner,
    });

    const project = allOrNothing({
      orgSlug: this._projectNameResult?.data?.orgSlug,
      projectName: this._projectNameResult?.data?.projectName,
    });
    if (project && (githubApp || githubPat))
      return { project, githubApp, githubPat };
    return undefined;
  }

  @state() private _githubPatResult?: TaskEvent<GithubPat>;

  private _onSourceGithubPat(event: TaskEvent<GithubPat>) {
    this._githubPatResult = event;
  }

  @state() private _githubInstallationResult?: TaskEvent<GithubInstallation>;

  private _onSourceGithubInstallation(event: TaskEvent<GithubInstallation>) {
    this._githubInstallationResult = event;
  }

  @state() private _projectNameResult?: TaskEvent<ProjectNamePicked>;

  private _onProjectNamePicked(event: TaskEvent<ProjectNamePicked>) {
    this._projectNameResult = event;
  }

  private async _submitForm(event: SubmitEvent) {
    event.preventDefault();

    const formData = this._formData();

    if (!formData)
      return;

    const projectSlug = slugify.default(formData.project.projectName);
    if (formData.githubPat) {
      await api.organization.createGithubPATProject.mutate({
        orgSlug: formData.project.orgSlug,
        projectName: formData.project.projectName,
        visibility: this._visibility?.visibility(),
        projectSlug,
        github: {
          repo: formData.githubPat.repo,
          owner: formData.githubPat.owner,
          accessToken: formData.githubPat.accessToken,
        }
      });
    } else if (formData.githubApp) {
      await api.organization.createGithubAppProject.mutate({
        orgSlug: formData.project.orgSlug,
        projectName: formData.project.projectName,
        visibility: this._visibility?.visibility(),
        projectSlug,
        github: {
          repo: formData.githubApp.repo,
          owner: formData.githubApp.owner,
        }
      });
    } else {
      throw new Error('INTERNAL ERROR: weird form data');
    }

    this._router?.navigate(PageReport.url({ orgSlug: this.orgSlug!, projectSlug }));
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    footer {
      display: grid;
      grid-template-columns: 1fr auto;
      margin-bottom: 100px;

      & sl-button {
        grid-column: 2;
      }
    }

    h1 { margin-bottom: 0; }

    .project-details {
      color: var(--sl-color-neutral-400);
    }

    sl-tab-panel::part(base) {
      padding: 0;
    }
  `];
}
