import { FlakinessReport } from "@flakiness/report";
import { WireTypes } from "@flakiness/server/common/wireTypes.js";
import { Task } from "@lit/task";
import { ReactiveControllerHost } from "lit";
import { api } from "./api.js";
import { assert, commitLastXDays } from "./utils.js";


export type HEADCommitOptions = WireTypes.ListCommitOptions & {
  since?: Date,
  until?: Date,
}

// Rule of thumb: if some task accepts Dates as arguments,
// this task MUST implement custom argsEqual().
function isEqualDates(date1: Date|undefined, date2: Date|undefined) {
  if (date1 === undefined && date2 === undefined)
    return true;
  if (date1 === undefined || date2 === undefined)
    return false;
  return +date1 === +date2;
}

export namespace tasks {
  export function head(host: ReactiveControllerHost, options: {
    defaultBranchName: () => string|undefined,
    head: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [
        options.defaultBranchName(), options.head(),
      ] as const,
      task: async ([defaultBranchName, head], { signal }) => {
        if (head)
          return head;
        assert(defaultBranchName);
        return defaultBranchName;
      }
    });
  }

  export function headCommit(host: ReactiveControllerHost, options: {
    project: () => WireTypes.Project|undefined,
    head: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [
        options.project(), options.head(),
      ] as const,
      task: async ([project, head], { signal }) => {
        assert(project && head);
        const response = await api.project.listCommits.query({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          commitOptions: {
            head,
            maxCount: 1,
          },
          pageOptions: {
            number: 0,
            size: 1,
          }
        }, { signal });
        return response.elements[0];
      }
    });
  }

  export function listCommitOptions(host: ReactiveControllerHost, options: {
    head: () => string|undefined,
    headCommit: () => WireTypes.Commit|undefined,
    since: () => Date|undefined,
    until: () => Date|undefined,
    maxCount: () => number|undefined,
    daysFromHEAD: () => number|undefined,
  }) {
    return new Task(host, {
      args: () => [
        options.head(), options.headCommit(), options.since(), options.until(), options.maxCount(), options.daysFromHEAD()
      ] as const,
      argsEqual: (
        [head1, headCommit1, since1, until1, maxCount1, daysFromHEAD1],
        [head2, headCommit2, since2, until2, maxCount2, daysFromHEAD2]
      ) => {
        return head1 === head2 && 
               headCommit1?.commitId === headCommit2?.commitId &&
               isEqualDates(since1, since2) &&
               isEqualDates(until1, until2) &&
               maxCount1 === maxCount2 &&
               daysFromHEAD1 === daysFromHEAD2;
      },
      task: async ([head, headCommit, since, until, maxCount, daysFromHEAD], { signal }) => {
        assert(headCommit && head && daysFromHEAD);
        // If there's a commit count, then easy.
        if (maxCount)
          return { head, maxCount } satisfies HEADCommitOptions;

        if (!since || !until) {
          const interval = commitLastXDays(headCommit, daysFromHEAD);
          since = interval.since;
          until = interval.until;
        }

        return {
          head,
          since,
          until,
          sinceTimestamp: +since as FlakinessReport.UnixTimestampMS,
          untilTimestamp: +until as FlakinessReport.UnixTimestampMS,
        } satisfies HEADCommitOptions;
      }
    });
  }

  export function flakinessReport(host: ReactiveControllerHost, options: {
    project: () => WireTypes.Project|undefined,
    reportId: () => number|undefined,
  }) {
    return new Task(host, {
      args: () => [
        options.project(), options.reportId(),
      ] as const,
      task: async ([project, reportId], { signal }) => {
        assert(project && reportId !== undefined);
        // First, fetch report.
        const reportResult = await api.run.rawURL.query({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          reportId: reportId,
          attachmentIds: [],
        }, { signal });
        const response = await fetch(reportResult.reportURL, { signal });
        if (!response.ok)
          throw new Error(`failed to fetch report: ${response.status} ${response.statusText}`);
        const json = await response.json() as FlakinessReport.Report;
        return FlakinessReport.dedupeSuitesTestsEnvironments(json);
      }
    });
  }

  export function aggregatedMetadata(host: ReactiveControllerHost, options: {
    project: () => WireTypes.Project|undefined,
    commitOptions: () => WireTypes.ListCommitOptions|undefined,
    fql: () => string|undefined,
  }) {
    return new Task(host, {
        args: () => [
          options.project(), options.commitOptions(), options.fql()
        ] as const,
        argsEqual: ([
          project1, commitOptions1, fql1
        ], [
          project2, commitOptions2, fql2
        ]) => {
          return project1 === project2 && fql1 === fql2 && isEqualCommitOptions(commitOptions1, commitOptions2);
        },
        task: async ([project, commitOptions, fql], { signal }) => {
          assert(project && commitOptions);
          return await api.report.aggregatedMetadata.query({
            orgSlug: project.org.orgSlug,
            projectSlug: project.projectSlug,
            fql,
            commitOptions,
          }, { signal });
        }
      }
    );
  }

  export function billing(host: ReactiveControllerHost, options: {
    orgSlug: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [options.orgSlug()] as const,
      task: async ([orgSlug], { signal }) => {
        assert(orgSlug);
        return await api.billing.status.query({ orgSlug }, { signal });
      }
    });
  }

  export function orgMembers(host: ReactiveControllerHost, options: {
    orgSlug: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [options.orgSlug()] as const,
      task: async ([orgSlug], { signal }) => {
        assert(orgSlug);
        const result = await api.organization.listMembers.query({ orgSlug }, { signal });
        return result.members;
      }
    });
  }

  export function orgProjects(host: ReactiveControllerHost, options: {
    orgSlug: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [options.orgSlug()] as const,
      task: async ([orgSlug], { signal }) => {
        assert(orgSlug);
        return await api.organization.listProjects.query({ orgSlug }, { signal }) ?? [];
      }
    });
  }

  export function allEnvironments(host: ReactiveControllerHost, options: {
    project: () => WireTypes.Project|undefined,
    commitOptions: () => WireTypes.ListCommitOptions|undefined,
    fql: () => string|undefined,
  }) {
    return new Task(host, {
        args: () => [
          options.project(), options.commitOptions(), options.fql()
        ] as const,
        argsEqual: ([
          project1, commitOptions1, fql1
        ], [
          project2, commitOptions2, fql2
        ]) => {
          return project1 === project2 && fql1 === fql2 && isEqualCommitOptions(commitOptions1, commitOptions2);
        },
        task: async ([project, commitOptions, fql], { signal }) => {
          assert(project && commitOptions);
          return await api.report.allEnvironments.query({
            orgSlug: project.org.orgSlug,
            projectSlug: project.projectSlug,
            fql,
            commitOptions,
          }, { signal });
        }
      }
    );
  }
}

function isEqualCommitOptions(c1: WireTypes.ListCommitOptions|undefined, c2: WireTypes.ListCommitOptions|undefined): boolean {
  // Both undefined are equal
  if (!c1 && !c2)
    return true;
  // Either undefined is not equal
  if (!c1 || !c2)
    return false;
  return c1.head === c2.head &&
         c1.headOffset === c2.headOffset &&
         c1.maxCount === c2.maxCount &&
         c1.sinceTimestamp === c2.sinceTimestamp &&
         c1.untilTimestamp === c2.untilTimestamp;
}
