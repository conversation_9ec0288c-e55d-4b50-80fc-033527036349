import { sha256Object } from '@flakiness/shared/common/utils.js';
import { LitElement, PropertyValues, ReactiveController, ReactiveControllerHost, TemplateResult, css, html, nothing } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import type { Disposable } from './utils.js';
import { deepQuerySelectorAll, disposeAll, onDOMEvent } from './utils.js';

// @ts-ignore: Property 'UrlPattern' does not exist 
if (!globalThis.URLPattern) { 
  await import("urlpattern-polyfill");
}

function findLink(event: MouseEvent): HTMLAnchorElement | undefined {
  for (const element of event.composedPath()) {
    if (!(element instanceof Element))
      continue;
    const link = element.closest("a[href]:not([target^=_]):not([download])");
    if (link)
      return link as HTMLAnchorElement;
  }
  return undefined;
}

type PathnameParams = {
  [key: string]: string | undefined;
};
type RouteRenderer = (params?: PathnameParams) => TemplateResult;
type Middleware = () => TemplateResult|undefined;

type Route = {
  pattern: URLPattern,
  render: RouteRenderer,
  middelwares?: Middleware[],
};

export type RouteConfig = {
  path: string | string[],
  render: RouteRenderer,
  middelwares?: Middleware[],
};

@customElement('app-router')
export class Router extends LitElement {
  @state() private _currentRoute?: Route;
  @state() private _currentRouteParseResult?: URLPatternResult;

  private _eventListeners: Disposable[] = [];
  
  private _routes: Route[] = [];
  private _ignoreClientSideNavigation: RegExp[] = [];

  private _hashRevealTimer?: NodeJS.Timeout;

  _states = new Set<URLStateRouter>();
  private _currentParams?: URLSearchParams;
  private _currentParamsHash?: string;

  private _notFoundRoute: Route = {
    pattern: new URLPattern({ pathname: '/*' }),
    render: () => html`Not Found.`,
  }

  ignoreClientSideNavigation(regex: string) {
    this._ignoreClientSideNavigation.push(new RegExp(regex, 'i'));
    this.requestUpdate();
  }

  withMiddleware(middleware: Middleware) {
    const addRoute = (config: RouteConfig) => this.route({
      ...config,
      middelwares: [middleware, ...(config.middelwares ?? [])],
    });
    return {
      route: addRoute,
      allRoutes: (configs: RouteConfig[]) => configs.forEach(addRoute),
    };
  }

  render() {
    for (const middleware of this._currentRoute?.middelwares ?? []) {
      const result = middleware.call(null);
      if (result)
        return result;
    }
    return this._currentRoute?.render.call(null, this._currentRouteParseResult?.pathname?.groups) ?? nothing;
  }

  route(config: RouteConfig) {
    const paths = Array.isArray(config.path) ? config.path : [config.path];
    for (const aPath of paths) {
      this._routes.push({
        pattern: new URLPattern({ pathname: aPath }),
        render: config.render, 
        middelwares: config.middelwares,
      });  
    }
    this.requestUpdate();
  }

  setNotFoundRenderer(renderer: RouteRenderer) {
    this._notFoundRoute.render = renderer;
  }

  connectedCallback(): void {
    super.connectedCallback();
    this._eventListeners = [
      onDOMEvent(window, 'popstate', () => this._goto(window.location.pathname)),
      onDOMEvent(document, 'click', event => this._onDocumentClick(event as MouseEvent)),
      onDOMEvent(this, URLStateConnectedEvent.eventName, this._onStateConnected.bind(this)),
    ];
    this._goto(window.location.pathname);
  }

  _onStateConnected(event: URLStateConnectedEvent) {
    event.stopImmediatePropagation();
    this._states.add(event.urlstate);
    event.urlstate._router = this;
    if (this._currentParams)
      event.urlstate._checkSearchChanged(this._currentParams);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    disposeAll(this._eventListeners);
  }

  navigate(url: string) {
    const u = new URL(url);
    window.history.pushState({}, '', url);
    this._goto(u.pathname);
  }

  _encodeParams(encoder: (params: URLSearchParams) => void) {
    const params = new URLSearchParams(window.location.search);
    encoder(params);
    if (window.location.search !== params.toString())
      window.history.pushState({}, '', window.location.pathname + '?' + params.toString());
    this._goto(window.location.pathname);
  }

  private _revealHash(attempt: number = 0) {
    const REVEAL_ATTEMPTS = [0, 50, 100, 250, 500, 500, 1000];
    if (!window.location.hash)
      return;
    const elements = deepQuerySelectorAll(window.location.hash).filter(element => {
      const rect = element.getBoundingClientRect();
      return rect && (rect.width || rect.height);
    });
    if (elements.length)
      elements[0].scrollIntoView();
    else if (attempt < REVEAL_ATTEMPTS.length)
      this._hashRevealTimer = setTimeout(this._revealHash.bind(this, attempt + 1), REVEAL_ATTEMPTS[attempt])
  }

  private _goto(pathname: string) {
    this._currentRoute = this._routes.find(route => route.pattern.test({ pathname })) ?? this._notFoundRoute;
    this._currentRouteParseResult = this._currentRoute.pattern.exec({ pathname }) ?? undefined;
  }

  protected updated(_changedProperties: PropertyValues): void {
    const params = new URLSearchParams(window.location.search);
    const paramsHash = sha256Object([window.location.pathname, ...params]);
    if (this._currentParamsHash !== paramsHash) {
      this._currentParamsHash = paramsHash;
      this._currentParams = params;
      for (const state of this._states)
        state._checkSearchChanged(params);
    }

    clearTimeout(this._hashRevealTimer);
    this._revealHash();
    super.updated(_changedProperties);
  }

  private _onDocumentClick(event: MouseEvent) {
    const hasModifiers = event.altKey || event.ctrlKey || event.metaKey || event.shiftKey;
    if (hasModifiers || event.defaultPrevented || event.button !== 0)
      return;

    const link = findLink(event);
    if (!link)
      return;
    if (link.origin !== window.location.origin)
      return;
    if (this._ignoreClientSideNavigation.some(route => route.test(link.pathname)))
      return;

    event.preventDefault();
    event.stopImmediatePropagation();
    event.stopPropagation();
    if (link.href !== window.location.href)
      window.history.pushState({}, '', link.href);
    this._goto(link.pathname);
  }

  static styles = css`
    :host {
      height: 100%;
    }
  `
}

export class URLStateRouter implements ReactiveController {
  _router?: Router;
  private _params: URLSearchParams = new URLSearchParams();

  constructor(private _host: ReactiveControllerHost & HTMLElement, private _onParamsChanged: (params: URLSearchParams) => void) {
    this._host.addController(this);
  }

  _checkSearchChanged(params: URLSearchParams) {
    this._params = params;
    this._onParamsChanged.call(null, params);
    this._host.requestUpdate();
  }

  params() {
    return this._params;
  }

  encodeParams(encoder: (params: URLSearchParams) => void) {
    this._router?._encodeParams(encoder);
  }

  hostConnected(): void {
    const event = new URLStateConnectedEvent(this);
    this._host.dispatchEvent(event);
    if (!this._router)
      console.error('ERROR: URLState was not attached to a tree with active router!');
  }

  hostDisconnected(): void {
    this._router?._states.delete(this);
    this._router = undefined;
  }
}

export class URLStateConnectedEvent extends Event {
  static readonly eventName = 'flakiness-urlstate-connected';
  readonly urlstate: URLStateRouter;

  constructor(state: URLStateRouter) {
    super(URLStateConnectedEvent.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.urlstate = state;
  }
}

declare global {
  interface HTMLElementEventMap {
    [URLStateConnectedEvent.eventName]: URLStateConnectedEvent;
  }
}

type StateOption<T> = {
  name?: string,
  default: T,
  // NOTE: the encoder MUST BE STABLE, meaning that the same conceptual data
  // will encode to the EQUAL string.
  encode: (value: T) => string | undefined,
  decode: (s: string) => T | undefined,
}

type Schema = Record<string, StateOption<any>>;
export type Materialized<S extends Schema> = { [K in keyof S]: S[K]['default'] }
export type SomeMaterialized<S extends Schema> = { [K in keyof S]?: S[K]['default'] }

export class URLState<S extends Schema> {
  static option<T>(options: StateOption<T>) {
    return options;
  }

  static dateOption(options: Omit<StateOption<Date|undefined>, 'encode'|'decode'>): StateOption<Date|undefined> {
    return {
      ...options,
      encode: date => date ? String(((+date) / 1000)|0) : undefined,
      decode: text => text ? new Date(parseInt(text, 10) * 1000) : undefined,
    };
  }

  constructor(private _fullSchema: S) {}

  default() {
    const result = {} as Materialized<S>;
    const encodedKeys = new Set<string>;
    for (const [key, schema] of Object.entries(this._fullSchema))
      (result as any)[key] = schema.default;
    return result;
  }

  toSearchParams(someMaterialized: SomeMaterialized<S>, inplace = new URLSearchParams()): URLSearchParams {
    const encodedKeys = new Set<string>;
    // 1. Set all new values
    for (const [key, schema] of Object.entries(this._fullSchema)) {
      let value = someMaterialized[key];
      if (value === undefined)
        value = schema.default;
      const encoded = schema.encode(value);
      const name = schema.name ?? key;
      if (encoded && encoded !== schema.encode(schema.default)) {
        encodedKeys.add(name);
        inplace.set(name, encoded);
      }
    }
    // 2. Remove all keys that are not defined
    for (const key of Array.from(inplace.keys())) {
      if (!encodedKeys.has(key))
        inplace.delete(key);
    }
    return inplace;
  }

  fromSearchParams(params: URLSearchParams, inplace?: Materialized<S>): Materialized<S> {
    if (!inplace)
      inplace = this.default();
    for (const [key, schema] of Object.entries(this._fullSchema)) {
      const name = schema.name ?? key;
      const paramsValue = params.get(name);
      // IMPORTANT: only re-create objects if they actually changed!
      if (paramsValue !== (schema.encode(inplace[key]) ?? null))
        (inplace as any)[key] = paramsValue === null ? schema.default : (schema.decode(paramsValue) ?? schema.default);
    }
    return inplace as Materialized<S>;
  }

  bind(host: ReactiveControllerHost & HTMLElement): Materialized<S> {
    const materialized = this.default();
    const changeApi = {} as Materialized<S>;
    for (const [name, options] of Object.entries(this._fullSchema)) {
      Object.defineProperty(changeApi, name, {
        get: () => {
          return materialized[name];
        },
        set: (aValue: any) => {
          if (aValue === materialized[name])
            return;
          (materialized as any)[name] = aValue;
          urlStateRouter.encodeParams(params => this.toSearchParams(materialized, params));
        },
        enumerable: true,
      });
    }
    const urlStateRouter = new URLStateRouter(host, params => {
      this.fromSearchParams(params, materialized);
      host.requestUpdate();
    });
    return changeApi;
  }

  createLinkRenderer(options?: {
    base?: SomeMaterialized<S>,
    pathname?: string
  }) {
    const url = new URL(window.location.href);
    const base = options?.base ?? {};
    if (options?.pathname)
      url.pathname = options.pathname;
    url.hash = '';
    return (someMaterialized: SomeMaterialized<S>) => {
      this.toSearchParams({ ...base, ...someMaterialized }, url.searchParams);
      return url.toString();
    }
  }
}

