import { WireTypes } from "@flakiness/server/common/wireTypes.js";
import { html } from "lit";
import { githubLinks } from "../githubLinks.js";
import { markdown } from "../markdown.js";

const ghaSecretInstructions = (project: WireTypes.Project) => markdown`
  Navigate to [project settings](${githubLinks.newProjectSecret(project)}) and
  add a new secret with the name \`FLAKINESS_ACCESS_TOKEN\` and value ${accessToken(project)}
`;

const accessToken = (project: WireTypes.Project) => html`<sl-tag data-testid=flakiness-access-token>${project.readWriteAccessToken}</sl-tag><sl-copy-button value="${project.readWriteAccessToken}"></sl-copy-button>`;

export const welcome_new_project = (project: WireTypes.Project) => markdown`

### Getting Started

> Flakiness Access Token: ${accessToken(project)}

Welcome to your new project!

To upload reports:

1. Install the flakiness report package: this package provides the \`npx flakiness\` cli command to upload Playwright Test JSON reports to the service.

    ${markdown.bash(`
      npm i @flakiness/report@latest
    `)}

1. Add Flakiness.io Playwright Reporter to the \`playwright.config.ts\` file:

    ${markdown.ts(`
      import { defineConfig } from '@playwright/test';

      export default defineConfig({
        reporter: [
          ['list'],
          ['@flakiness/report/playwright-test', { endpoint: '${window.location.origin}' }],
        ],
        retries: 1,
      });
    `, `playwright.config.ts`)}

1. Add secret to your CI
    ${html`
      <sl-tab-group>
        <sl-tab slot="nav" panel="github-actions">Github Actions</sl-tab>
        <sl-tab-panel name="github-actions">            
          ${ghaSecretInstructions(project)}
        </sl-tab-panel>
      </sl-tab-group>
    `}
`;