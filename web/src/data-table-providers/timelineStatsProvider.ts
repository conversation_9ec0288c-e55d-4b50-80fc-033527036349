import { StatsReport } from '@flakiness/server/common/stats/statsReport.js';
import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { parseTimeline } from '@flakiness/server/common/timelineUtils.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';
import { contexts } from '../contexts.js';

@customElement('timeline-stats-view')
class TimelineStatsView extends LitElement {
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkTimeline, subscribe: true }) private _linkTimeline?: ContextType<typeof contexts.linkTimeline>;

  @property({ attribute: false }) timeline?: string;

  render() {
    if (this.timeline === undefined)
      return nothing;
    const timeline = parseTimeline(this.timeline ?? '');
    let newFQL = this._fql?.clearEnvFilters();
    for (const filter of timeline.filters)
      newFQL = newFQL?.toggleFilter(filter);
    const link = newFQL ? this._linkTimeline?.render(newFQL?.serialize()) : undefined;
    return html`
      <timeline-name .timeline=${this.timeline ?? ''} .href=${link}></timeline-name>
    `;
  }

  static styles = [linkStyles, css`
  `];
}


export const timelineStatsColumns = {
  name: {
    sortAxis: undefined,
    width: '1fr',
    renderHeader() { return html`Name`; },
    renderElement(timelineStats): TemplateResult {
      return html`<timeline-stats-view style="margin: var(--sl-spacing-small) 0;" .timeline=${timelineStats.timeline}></timeline-stats-view>`;
    },
  },
  duration: {
    sortAxis: 'total_time',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Total CPU Time`; },
    renderElement({ durationMs }): TemplateResult {
      return html`<time-interval ms=${durationMs.sum}></time-interval>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'failures' as WireTypes.TimelineStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createTimelineStatsLoaderRemote(options: {
  orgSlug: string,
  projectSlug: string,
  timelineSplit: TimelineSplit,
  fql: string,
  commitOptions: WireTypes.ListCommitOptions,
  historyDepthDays: number,
}): DataLoader<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  return (pageOptions, sortOptions, signal) => {
    return api.report.timelineStats.mutate({
      orgSlug: options.orgSlug,
      projectSlug: options.projectSlug,
      commitOptions: options.commitOptions,
      fql: options.fql,
      timelineSplit: options.timelineSplit.serialize(),
      historyDepthDays: options.historyDepthDays,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
  }
}

export function createTimelineStatsLoaderLocal(statsReport: StatsReport): DataLoader<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.pageTimelineStats(pageOptions, sortOptions.axis, sortOptions.direction);
  }
}

export function createTimelineStatsDataProvider(options: {
  columns?: DataColumn<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>[],
  loader: DataLoader<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>,  
}): DataTableProvider<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  return {
    columns: options.columns ?? [timelineStatsColumns.name, timelineStatsColumns.duration],
    defaultSortColumn: timelineStatsColumns.duration,
    noDataMessage: 'No Timelines',
    loader: options.loader,
  };
}
