import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsReport } from '@flakiness/server/common/stats/statsReport.js';
import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { ContextType } from '@lit/context';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';
import { consume, contexts } from '../contexts.js';
import { githubLinks } from '../githubLinks.js';

// Tests Provider ---------------------------

@customElement('data-table-test-stats-cell')
class DataTableTestCell extends LitElement {
  @property({ attribute: false }) testStats?: WireTypes.TestStats;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.linkTest, subscribe: true }) private _linkTest?: ContextType<typeof contexts.linkTest>;
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;

  render() {
    if (!this.testStats)
      return nothing;
    const test = this.testStats.test;
    const elements: TemplateResult[] = [];
    //TODO: can we hide this highlight inside the test-title?
    const hl = this._fql?.highlightTest(test);
    for (const title of test.titles) {
      elements.push(html`<hl-span .text=${title} .hl=${hl?.get(title)}></hl-span>`);
      elements.push(html`<sl-icon name=chevron-right></sl-icon>`)
    }
    elements.pop();
    
    const annotationTexts = new Multimap<string, string|undefined>();
    for (const annotation of this.testStats?.annotations ?? [])
      annotationTexts.set(annotation.type, annotation.description);
    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <h-box style="gap: var(--sl-spacing-2x-small);">
          <a class=test-name href=${this._linkTest?.render(this.testStats) ?? nothing}>${elements}</a>
          ${(test.tags ?? []).map(tag => html`<test-tag .tag=${tag} .hl=${hl?.get(tag)}></test-tag>`)}
          ${[...annotationTexts].map(([type, descriptions]) => html`<test-annotation .annotation=${{
            type,
            description: [...descriptions].filter(d => !!d).join('\n'),
            annotationId: 'dummy' as Stats.AnnotationId,
          } satisfies WireTypes.TestAnnotation}></test-annotation>`)}
        </h-box>
        <h-box>
          <div class=filepath>
            <a-ext class=configpath href=${this._project ? githubLinks.fileUrl(this._project, this.testStats.test.lastExecutionCommitId, test.filePath, test.lineNumber) : nothing}>
              <hl-span .text=${test.filePath} .hl=${hl?.get(test.filePath)}></hl-span>:${test.lineNumber}
            </a-ext>
          </div>
          <h-box class=artifact-icons>
            ${this.testStats.hasImage ? html`
              <a href=${this._linkTest ? this._linkTest.render(this.testStats) + '#section-images' : nothing}>
                <artifact-icon .type=${"image"}></artifact-icon>
              </a>
            ` : nothing}
            ${this.testStats.hasVideo ? html`
              <a href=${this._linkTest ? this._linkTest.render(this.testStats) + '#section-videos' : nothing}>
                <artifact-icon .type=${"video"}></artifact-icon>
              </a>
            ` : nothing}
            ${this.testStats.hasTrace ? html`
              <a href=${this._linkTest ? this._linkTest.render(this.testStats) + '#section-traces' : nothing}>
                <artifact-icon .type=${"pw-trace"}></artifact-icon>
              </a>
            ` : nothing}
          </h-box>
        </h-box>
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      overflow: hidden;
    }

    sl-tooltip {
      --max-width: 50em;
    }
    v-box {
      padding: var(--sl-spacing-small) 0;
    }
    .test-name {
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: var(--sl-font-weight-semibold);
    }
    .filepath {
      color: var(--sl-color-neutral-500);
      font-size: var(--sl-font-size-small);
    }
    .artifact-icons {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
    sl-icon { font-size: var(--sl-font-size-2x-small); }
  `];
}

export const testStatsColumns = {
  name: {
    sortAxis: 'name',
    width: '1fr',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Test name`; },
    renderElement(stats: WireTypes.TestStats): TemplateResult {
      return html`<data-table-test-stats-cell .testStats=${stats}></data-table-test-stats-cell>`;
    },
  },
  status: {
    sortAxis: 'outcome',
    width: 'auto',
    renderHeader() { return html`S`; },
    renderElement(stats: WireTypes.TestStats): TemplateResult {
      return html`<fk-outcome .outcome=${stats.outcome}></fk-outcome>`;
    },
  },
  timeline: {
    sortAxis: undefined,
    width: 'auto',
    renderHeader() { return html`Timeline`; },
    renderElement(stats: WireTypes.TestStats): TemplateResult {
      return html`<timeline-stats-view style="padding: var(--sl-spacing-small) 0;" .timeline=${stats.timeline}></timeline-stats-view>`;
    },
  },
  duration: {
    sortAxis: 'avg_duration',
    renderHeader() { return html`Avg. Duration`; },
    renderElement({ durationMs }: WireTypes.TestStats): TemplateResult {
      const ms = Math.round(durationMs.sum / durationMs.count);
      return html`<time-interval ms=${ms}></time-interval>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.TestStats, WireTypes.TestStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'name' as WireTypes.TestStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createTestStatsLoaderLocal(statsReport: StatsReport): DataLoader<WireTypes.TestStats, WireTypes.TestStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.pageTestStats(pageOptions, sortOptions.axis, sortOptions.direction);    
  }
}

export function createTestStatsLoaderRemote(options: {
  orgSlug: string,
  projectSlug: string,
  timelineSplit: TimelineSplit,
  fql: string,
  commitOptions: WireTypes.ListCommitOptions,
  historyDepthDays: number,
}): DataLoader<WireTypes.TestStats, WireTypes.TestStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    const result = await api.report.testStats.mutate({
      orgSlug: options.orgSlug,
      projectSlug: options.projectSlug,
      commitOptions: options.commitOptions,
      fql: options.fql,
      timelineSplit: options.timelineSplit.serialize(),
      historyDepthDays: options.historyDepthDays,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
    return result;
  }
}

export function createTestStatsDataProvider(options: {
  columns?: DataColumn<WireTypes.TestStats, WireTypes.TestStatsSortAxis>[],
  loader: DataLoader<WireTypes.TestStats, WireTypes.TestStatsSortAxis>,
}): DataTableProvider<WireTypes.TestStats, WireTypes.TestStatsSortAxis> {
  return {
    columns: options.columns ?? [testStatsColumns.status, testStatsColumns.name, testStatsColumns.timeline, testStatsColumns.duration],
    loader: options.loader,
    defaultSortColumn: testStatsColumns.status,
    noDataMessage: 'No Tests',
  };
}
