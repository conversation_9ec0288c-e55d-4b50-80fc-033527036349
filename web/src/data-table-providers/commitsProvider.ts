import { Field, outcomeToStatusFilter } from '@flakiness/server/common/fql/fields.js';
import { Filter } from '@flakiness/server/common/fql/filter.js';
import { Operator } from '@flakiness/server/common/fql/operators.js';
import { Query } from '@flakiness/server/common/fql/query.js';
import { Stats } from '@flakiness/server/common/stats/stats.js';
import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { html, nothing, TemplateResult } from 'lit';
import { api } from '../api.js';
import { DataColumn, DataTableProvider } from '../components/data-table.js';
import { PageReport } from '../page-report.js';
import { PageRun } from '../page-run.js';
import { wireOutcomesToOutcome } from '../utils.js';

function createOutcomeColumn(outcome: WireTypes.Outcome, options: {
  orgSlug: string,
  projectSlug: string,
  fql: string,
}): DataColumn<WireTypes.CommitStats, WireTypes.CommitStatsSortAxis> {
  const filter = new Filter('status', Field.statusToString.STATUS, Operator.stringOperators.EQ, [outcomeToStatusFilter(outcome)], false);
  const q = Query.parse(options.fql).clearStatusFilters().toggleFilter(filter);

  return {
    sortAxis: outcome,
    renderHeader() {
      if (outcome === 'expected')
        return html`Passed`;
      if (outcome === 'unexpected')
        return html`Failed`;
      if (outcome === 'flaked')
        return html`Flaked`;
      if (outcome === 'regressed')
        return html`New Failures`;
      if (outcome === 'skipped')
        return html`Skipped`;
      throw new Error(`Unknown outcome "${outcome}"`);
    },
    renderElement({ testStats, commit }): TemplateResult {
      const href = PageReport.url({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        commitsCount: 1,
        head: commit.commitId,
        fql: q.serialize(),
      });
      return html`
        <a href=${href}>
          <int-num .x=${testStats[outcome]}></int-num>
        </a>
      `;
    },
  }
}

function createReportsColumn(options: {
  orgSlug: string,
  projectSlug: string,
  testId: Stats.TestId,
  testTimeline: string,
  timelineSplit: TimelineSplit,
  fql: string,
}): DataColumn<WireTypes.CommitStats, WireTypes.CommitStatsSortAxis> {
  return {
    sortAxis: undefined,
    width: '1fr',
    renderHeader() { return html`` },
    renderElement(stats): TemplateResult {
      return html`
        <h-box style="flex-wrap: wrap; justify-content: end; flex: auto; gap: var(--sl-spacing-2x-small); margin: var(--sl-spacing-2x-small) 0;">
        ${stats.runs.map(reportStats => html`
          <sl-button href=${PageRun.url({
            orgSlug: options.orgSlug,
            projectSlug: options.projectSlug,
            testId: options.testId,
            testTimeline: options.testTimeline,
            reportId: reportStats.run.reportId,
            timelineSplit: options.timelineSplit,
          })}>
            <h-box>
              <fk-outcome .outcome=${wireOutcomesToOutcome(reportStats.testStatsOutcomes)}></fk-outcome>
              <span>Run #${reportStats.run.reportId}</span>
              <h-box slot=suffix style="gap: var(--sl-spacing-2x-small); font-size: var(--sl-font-size-small); color: var(--sl-color-neutral-500);">
                ${reportStats.hasImages ? html`<artifact-icon .type=${'image'}></artifact-icon>` : nothing}
                ${reportStats.hasVideos ? html`<artifact-icon .type=${'video'}></artifact-icon>` : nothing}
                ${reportStats.hasTraces ? html`<artifact-icon .type=${'pw-trace'}></artifact-icon>` : nothing}
              <h-box>
            </h-box>
          </sl-button>
        `)}
        </h-box>
      `;
    },
  };
}

function createNameColumn(options: {
  orgSlug: string,
  projectSlug: string,
  fql: string,
  timelineSplit: TimelineSplit,
}): DataColumn<WireTypes.CommitStats, WireTypes.CommitStatsSortAxis> {
  return {
    sortAxis: 'chrono',
    width: '1fr',
    renderHeader() { return html`Commits`; },
    renderElement(stats): TemplateResult {
      return html`
        <h-box style="gap: var(--sl-spacing-small); padding: var(--sl-spacing-small) 0;">
          <fk-outcome outcome=${wireOutcomesToOutcome(stats.testStats)}></fk-outcome>
          <fk-commit .commit=${stats.commit} href=${
            PageReport.url({
              orgSlug: options.orgSlug,
              projectSlug: options.projectSlug,
              fql: options.fql,
              commitsCount: 1,
              timelineSplit: options.timelineSplit,
              head: stats.commit.commitId,
            })
          }></fk-commit>
        </h-box>
      `;
    },
  }
}

export function createCommitsDataProvider(options: {
  orgSlug: string,
  projectSlug: string,
  timelineSplit: TimelineSplit,
  fql: string,
  commitOptions: WireTypes.ListCommitOptions,
  historyDepthDays: number,
  testId?: Stats.TestId,
  testTimeline?: string,
}): DataTableProvider<WireTypes.CommitStats, WireTypes.CommitStatsSortAxis> {
  const nameColumn = createNameColumn(options);
  return {
    columns: options.testId && options.testTimeline ?
      [
        nameColumn,
        createReportsColumn({ ...options, testId: options.testId, testTimeline: options.testTimeline })
      ] :
      [
        nameColumn,
        createOutcomeColumn('regressed', options),
        createOutcomeColumn('unexpected', options),
        createOutcomeColumn('flaked', options),
        createOutcomeColumn('skipped', options),
        createOutcomeColumn('expected', options),
      ],
    noDataMessage: 'No Commits',
    defaultSortColumn: nameColumn,
    loader: async (pageOptions, sortOptions, signal) => {
      const result = await api.report.commitStats.mutate({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        historyDepthDays: options.historyDepthDays,
        commitOptions: options.commitOptions,
        fql: options.fql,
        timelineSplit: options.timelineSplit.serialize(),
        pageOptions: pageOptions,
        sortOptions: sortOptions ?? {
          axis: 'chrono' as WireTypes.CommitStatsSortAxis,
          direction: 'desc' as WireTypes.SortDirection,
        },
      }, { signal });
      return result;
    }
  }
}
