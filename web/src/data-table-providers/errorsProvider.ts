import { Field } from '@flakiness/server/common/fql/fields.js';
import { Filter } from '@flakiness/server/common/fql/filter.js';
import { Operator } from '@flakiness/server/common/fql/operators.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';

import { StatsReport } from '@flakiness/server/common/stats/statsReport.js';
import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { contexts } from '../contexts.js';

// Tests Provider ---------------------------

@customElement('data-table-error-cell')
class DataTableErrorCell extends LitElement {
  @property({ attribute: false }) error?: WireTypes.TestError;
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  render() {
    const error = this.error;
    const text = error?.message ?? error?.value;
    if (!text || !error)
      return nothing;
    const hl = this._fql?.highlightError(error);
    const url = text && this._fql && this._link && this._link.render(this._fql.toggleFilter(new Filter('error', Field.errorToString.ERR_TEXT, Operator.stringOperators.EQ, [text], false)).serialize());
    return html`
      <section>
        <a href=${url ?? nothing}><hl-span .text=${text} .hl=${hl?.get(text)}></hl-span></a>
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      overflow: hidden;
    }
    section {
      padding: var(--sl-spacing-small) 0;
    }
    a {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  `]
}


@customElement('data-table-error-stats-cell')
class DataTableErrorStatsCell extends LitElement {
  @property({ attribute: false }) errorStats?: WireTypes.ErrorStats;
  @property({ attribute: false }) type?: 'timelines'|'tests';
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  render() {
    const error = this.errorStats?.error;
    if (!this.errorStats || !error || !this.type)
      return nothing;

    let value: number;
    if (this.type === 'timelines') {
      value = this.errorStats.impactedTimelines;
    } else if (this.type === 'tests') {
      value = this.errorStats.impactedTests;
    } else {
      // unknown type
      return nothing;
    }
    const text = error.message ?? error.value;
    let url: string | undefined;
    if (text && this._fql && this._link) {
      const errorFilter = new Filter('error', Field.errorToString.ERR_TEXT, Operator.stringOperators.EQ, [text], false);
      const query = this._fql.isMatchingFilter(errorFilter) ? this._fql.serialize() : this._fql.toggleFilter(errorFilter).serialize();
      url = this._link.render(query);
    }
    return html`
      <section>
        <a href=${url ?? nothing}>${value}</a>
      </section>
    `;
  }

  static styles = [linkStyles, css`
    section {
      padding: var(--sl-spacing-small) 0;
    }
  `]
}

export const errorColumns = {
  name: {
    sortAxis: 'name',
    width: '1fr',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Errors`; },
    renderElement({ error }): TemplateResult {
      return html`<data-table-error-cell .error=${error}></data-table-error-cell>`;
    },
  },
  impactedTimelines: {
    sortAxis: 'timelines',
    renderHeader() { return html`Impacted Timelines`; },
    renderElement(stats): TemplateResult {
      return html`<data-table-error-stats-cell .type=${'timelines'} .errorStats=${stats}></data-table-error-stats-cell>`;
    },
  },
  impactedTests: {
    sortAxis: 'tests',
    renderHeader() { return html`Impacted Tests`; },
    renderElement(stats): TemplateResult {
      return html`<data-table-error-stats-cell .type=${'tests'} .errorStats=${stats}></data-table-error-stats-cell>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'tests' as WireTypes.ErrorStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createErrorStatsLoaderRemote(options: {
  orgSlug: string,
  projectSlug: string,
  timelineSplit: TimelineSplit,
  fql: string,
  commitOptions: WireTypes.ListCommitOptions,
  historyDepthDays: number,
}): DataLoader<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis> {
  return (pageOptions, sortOptions, signal) => {
    return api.report.errorStats.mutate({
      orgSlug: options.orgSlug,
      projectSlug: options.projectSlug,
      commitOptions: options.commitOptions,
      fql: options.fql,
      timelineSplit: options.timelineSplit.serialize(),
      historyDepthDays: options.historyDepthDays,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
  }
}

export function createErrorStatsLoaderLocal(statsReport: StatsReport): DataLoader<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.pageErrorStats(pageOptions, sortOptions.axis, sortOptions.direction);
  }
}

export function createErrorStatsDataProvider(options: {
  columns?: DataColumn<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis>[],
  loader: DataLoader<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis>,  
}): DataTableProvider<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis> {
  return {
    loader: options.loader,
    columns: options.columns ?? [errorColumns.name, errorColumns.impactedTimelines, errorColumns.impactedTests],
    defaultSortColumn: errorColumns.impactedTests,
    noDataMessage: 'No Errors',
  };
}
