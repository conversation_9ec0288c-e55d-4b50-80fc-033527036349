import { Field, outcomeToStatusFilter } from '@flakiness/server/common/fql/fields.js';
import { Filter } from '@flakiness/server/common/fql/filter.js';
import { Operator } from '@flakiness/server/common/fql/operators.js';
import { Query } from '@flakiness/server/common/fql/query.js';
import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { html, nothing, TemplateResult } from 'lit';
import { api } from '../api.js';
import { DataColumn, DataTableProvider } from '../components/data-table.js';
import { PageRun } from '../page-run.js';
import { wireOutcomesToOutcome } from '../utils.js';

const COLUMN_RUN_ID: DataColumn<WireTypes.RunStats, WireTypes.ReportStatsSortAxis> = {
  sortAxis: undefined,
  width: 'auto',
  renderHeader() { return html`Run #`; },
  renderElement(stats): TemplateResult {
    return html`${stats.run.reportId}`;
  },
};

const COLUMN_RUN_STATUS: DataColumn<WireTypes.RunStats, WireTypes.ReportStatsSortAxis> = {
  sortAxis: undefined,
  width: 'auto',
  renderHeader() { return html`S`; },
  renderElement(stats): TemplateResult {
    return html`<fk-outcome outcome=${wireOutcomesToOutcome(stats.testStatsOutcomes)}></fk-outcome>`;
  },
};

function createRunNameColumn(options: {
  orgSlug: string,
  projectSlug: string,
  fql: string,
  timelineSplit: TimelineSplit,
}): DataColumn<WireTypes.RunStats, WireTypes.ReportStatsSortAxis> {
  return {
    sortAxis: 'chrono',
    width: '1fr',
    renderHeader() { return html`Date`; },
    renderElement(runStats): TemplateResult|typeof nothing {
      if (!runStats)
        return nothing;
      const { run } = runStats;

      const href = PageRun.url({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        reportId: run.reportId,
        query: options.fql,
        timelineSplit: options.timelineSplit,
      });
      return html`
        <h-box style="padding: var(--sl-spacing-small) 0;">
          <a href=${href}><sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(run.startTimestamp ?? Date.now()).toISOString()}></sl-format-date></a>
          <h-box style="font-size: var(--sl-font-size-small); color: var(--sl-color-neutral-500);">
            ${runStats.hasImages ? html`<artifact-icon .type=${'image'}></artifact-icon>` : nothing}
            ${runStats.hasVideos ? html`<artifact-icon .type=${'video'}></artifact-icon>` : nothing}
            ${runStats.hasTraces ? html`<artifact-icon .type=${'pw-trace'}></artifact-icon>` : nothing}
          </h-box>
        </h-box>
      `;
    },
  }
}

function createOutcomeColumn(outcome: WireTypes.Outcome, options: {
  orgSlug: string,
  projectSlug: string,
  fql: string,
  timelineSplit: TimelineSplit,
}): DataColumn<WireTypes.RunStats, WireTypes.ReportStatsSortAxis> {
  const filter = new Filter('status', Field.statusToString.STATUS, Operator.stringOperators.EQ, [outcomeToStatusFilter(outcome)], false);
  const q = Query.parse(options.fql).clearStatusFilters().toggleFilter(filter);
  return {
    sortAxis: outcome,
    renderHeader() {
      if (outcome === 'expected')
        return html`Passed`;
      if (outcome === 'unexpected')
        return html`Failed`;
      if (outcome === 'flaked')
        return html`Flaked`;
      if (outcome === 'regressed')
        return html`New Failures`;
      if (outcome === 'skipped')
        return html`Skipped`;
      throw new Error(`Unknown outcome "${outcome}"`);
    },
    renderElement({ testStatsOutcomes, run }): TemplateResult {
      const href = PageRun.url({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        reportId: run.reportId,
        query: q.serialize(),
        timelineSplit: options.timelineSplit,
      });
      return html`
        <a href=${href}>
          <int-num .x=${testStatsOutcomes[outcome]}></int-num>
        </a>
      `;
    },
  }
}

const COLUMN_DURATION: DataColumn<WireTypes.RunStats, WireTypes.ReportStatsSortAxis> = {
  sortAxis: 'factual_duration',
  renderHeader() { return html`Factual Duration`; },
  renderElement({ run: report }): TemplateResult {
    return html`<time-interval ms=${report.duration ?? 0}></time-interval>`;
  },
};

export function createReportsDataProvider(options: {
  orgSlug: string,
  projectSlug: string,
  timelineSplit: TimelineSplit,
  fql: string,
  commitOptions: WireTypes.ListCommitOptions,
  historyDepthDays: number
}): DataTableProvider<WireTypes.RunStats, WireTypes.ReportStatsSortAxis> {
  const nameColumn = createRunNameColumn(options);
  return {
    columns: [
      COLUMN_RUN_ID,
      COLUMN_RUN_STATUS,
      nameColumn,
      createOutcomeColumn('regressed', options),
      createOutcomeColumn('unexpected', options),
      createOutcomeColumn('flaked', options),
      createOutcomeColumn('skipped', options),
      createOutcomeColumn('expected', options),
      COLUMN_DURATION,
    ],
    noDataMessage: 'No Runs',
    defaultSortColumn: nameColumn,
    loader: (pageOptions, sortOptions, signal) => {
      return api.report.runStats.mutate({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        historyDepthDays: options.historyDepthDays,
        commitOptions: options.commitOptions,
        fql: options.fql,
        timelineSplit: options.timelineSplit.serialize(),
        pageOptions: pageOptions,
        sortOptions: sortOptions ?? {
          axis: 'chronological' as WireTypes.ReportStatsSortAxis,
          direction: 'desc' as WireTypes.SortDirection,
        },
      }, { signal });
    }
  }
}
