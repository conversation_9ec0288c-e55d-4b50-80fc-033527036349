import { consume, ContextType } from '@lit/context';
import { css, html, LitElement } from 'lit';
import { customElement, query } from 'lit/decorators.js';
import { api } from '../api.js';
import { contexts, EventTask } from '../contexts.js';
import { link_source_with_github_app } from '../docs/link_source_with_github_app.js';
import { githubMarkdownCSS } from '../githubMarkdownCSS.js';
import { githubUtils } from '../githubUtils.js';
import { assert } from '../utils.js';
import { docLinkStyles } from './cssstyles.js';
import { InputWithStatus } from './input-with-status.js';

export type GithubInstallation = {
  owner: string,
  repo: string,
}

@customElement('pick-github-installation')
class PickGithubInstallation extends LitElement {
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  @query('input-with-status') private _input?: InputWithStatus;

  private _validateGithubAccess = new EventTask(this, 'github-installation', {
    autoRun: false,
    args: () => [this._input?.value()],
    task: async ([inputSourceURL], { signal }): Promise<GithubInstallation> => {
      assert(inputSourceURL);

      // 1. Make sure source URL is parsable.
      const result = githubUtils.parseGithubURL(inputSourceURL);
      let installation: GithubInstallation|undefined;
      if ('error' in result) {
        this._input?.setStatus(false, result.error);
      } else {
        installation = result;
        const response = await api.github.checkAppAccess.query(installation, { signal });
        if (response)
          this._input?.setStatus(true, 'Access to repository');
        else
          this._input?.setStatus(false, 'Failed to access repository');
      }
      assert(installation && this._input?.success());
      return installation;
    }
  });

  override render() {
    return html`
      ${this._serverInfo ? link_source_with_github_app(this._serverInfo) : undefined}
      <input-with-status
        type=text
        required
        name=source_url
        placeholder="https://github.com/owner/repo"
        @sl-input=${() => this._validateGithubAccess.run()}
      ></input-with-status>   
    `;
  }

  static styles = [docLinkStyles, githubMarkdownCSS, css`
  `];
}