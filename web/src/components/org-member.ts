import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { SlSelectEvent } from '@shoelace-style/shoelace';
import { api } from '../api.js';
import { contexts } from '../contexts.js';
import { PageOrganization } from '../page-organization.js';

@customElement('org-member')
export class OrgMember extends LitElement {

  @property({ attribute: false }) org?: WireTypes.Organization;
  @property({ attribute: false }) user?: WireTypes.User;
  @property({ attribute: false }) orgRole?: WireTypes.OrgRole;
  @consume({ context: contexts.router, subscribe: true, }) private _router?: ContextType<typeof contexts.router>;

  override render() {
    const user = this.user;
    const org = this.org;
    const orgRole = this.orgRole;

    if (!user || !org)
      return nothing;

    return html`
      <fk-user .user=${user}>
        <sl-dropdown>
          <sl-button ?disabled=${orgRole === 'owner' || !this.org || this.org.access === 'member'} slot="trigger" caret>${this.orgRole ?? 'No Access'}</sl-button>
          <sl-menu @sl-select=${async (event: SlSelectEvent) => {
            let newRole: 'member'|'admin' | undefined;
            if (event.detail.item.value === 'make-owner') {
              const really = confirm('Are you sure you want to transfer organization ownership?');
              if (really) {
                await api.organization.transfer.mutate({
                  orgSlug: org.orgSlug,
                  newOwner: user.userId,
                });
                await this._router?.navigate(PageOrganization.url({ orgSlug: org.orgSlug }));
              }
              return;
            }
            if (event.detail.item.value === 'remove-access')
              newRole = undefined;
            else if (event.detail.item.value === 'make-member')
              newRole = 'member';
            else if (event.detail.item.value === 'make-admin')
              newRole = 'admin';

            await api.organization.setMembership.mutate({
              orgSlug: org.orgSlug,
              userId: user.userId,
              role: newRole,
            });
            this.orgRole = newRole;
          }}>
            <sl-menu-item value='make-member' type=checkbox ?checked=${orgRole === 'member'}>Member</sl-menu-item>
            <sl-menu-item value='make-admin' type=checkbox ?checked=${orgRole === 'admin'}>Admin</sl-menu-item>
            <sl-divider></sl-divider>
            <sl-menu-item ?disabled=${this.org?.access !== 'owner'} value='make-owner'>Transfer ownership</sl-menu-item>  
            <sl-divider></sl-divider>
            <sl-menu-item value='remove-access'>Remove access</sl-menu-item>  
        </sl-dropdown>
      </fk-user>
    `;
  }

  static styles = [css`
  `]
}
