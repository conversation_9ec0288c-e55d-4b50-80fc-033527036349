import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { SlSelectEvent } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from '../components/cssstyles.js';
import { contexts } from '../contexts.js';

export class FKHeadSelectEvent extends Event {
  static readonly eventName = 'fk-select';
  readonly head: WireTypes.Ref;

  constructor(head: WireTypes.Ref) {
    super(FKHeadSelectEvent.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.head = head;
  }
}

@customElement('head-selector')
class HeadSelector extends LitElement {

  @property({ attribute: false }) head?: string;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _refs?: ContextType<typeof contexts.projectReferences>;

  render() {
    const defaultBranch = this._refs?.defaultBranch;
    if (!defaultBranch)
      return nothing;
    const branches = (this._refs?.branches ?? []).filter(branch => branch.name !== defaultBranch.name);

    branches.sort((b1, b2) => b2.commit.timestamp - b1.commit.timestamp);
    const recentBranches = [defaultBranch, ...branches.slice(0, 5)];
    const otherBranches = branches.slice(5);

    const head = this.head ?? defaultBranch?.name;
    const isCommit = !this._refs?.branches.some(ref => ref.name === head);
    return html`
      <sl-dropdown @sl-select=${this._onSelect}>
        <sl-button slot="trigger" caret>
          <sl-icon slot=prefix library=boxicons name=${isCommit ? 'bx-git-commit' : 'bx-git-branch'}></sl-icon>
          ${head?.length === 40 ? head.slice(0, 7) : head ?? nothing}
        </sl-button>
        <sl-menu>
          ${recentBranches.map(branch => html`
            <sl-menu-item
              type=checkbox
              ?checked=${branch.name.toLowerCase() === head?.toLowerCase()}
              value=${branch.name}
            >
              ${branch.name}
              <sl-icon slot=prefix library=boxicons name=bx-git-branch></sl-icon>
            </sl-menu-item>
          `)}
          ${otherBranches.length ? html`
            <sl-divider></sl-divider>
            <sl-menu-item>
              Older branches
              <sl-menu slot=submenu>
                ${otherBranches.map(branch => html`
                  <sl-menu-item
                    type=checkbox
                    ?checked=${branch.name.toLowerCase() === this.head?.toLowerCase()}
                    value=${branch.name}
                  >
                    ${branch.name}
                    <sl-icon slot=prefix library=boxicons name=bx-git-branch></sl-icon>
                  </sl-menu-item>
                `)}
              </sl-menu>
            </sl-menu-item>
          ` : nothing}
        </sl-menu>
      </sl-dropdown>
    `;
  }

  private _onSelect(event: SlSelectEvent) {
    const refName = event.detail.item.value;
    const ref = this._refs?.branches.find(branch => branch.name === refName);
    this.head = ref?.name;
    if (ref)
      this.dispatchEvent(new FKHeadSelectEvent(ref));
  }

  static styles = [linkStyles, css`
  `];
}