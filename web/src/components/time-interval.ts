import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { humanReadableMs } from '../utils.js';

@customElement('time-interval')
class TimeInterval extends LitElement {
  @property({ }) ms?: string;
  @property({ type: Boolean }) icon?: boolean;

  private _renderInterval() {
    if (!this.ms)
      return nothing;
    const ms = parseInt(this.ms, 10);
    return html`${humanReadableMs(ms)}`;
  }

  override render() {
    const icon = this.icon ? html`<sl-icon name=clock-history></sl-icon>` : nothing;
    return html`${icon}${this._renderInterval()}`;
  }


  static styles = [css`
    :host {
      display: inline-flex;
      align-items: center;
    }

    sl-icon {
      margin-right: var(--sl-spacing-2x-small);
    }
  `];
}