import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { PageOrganizationBilling } from '../page-organization-billing.js';
import { linkStyles } from './cssstyles.js';

@customElement('org-alerts')
class OrgAlerts extends LitElement {
  @property({ attribute: false }) org?: WireTypes.Organization;

  override render() {
    let hasRestrictions = this.org?.restrictedCIUploads || this.org?.restrictedProjectAccess;
    if (!this.org || !hasRestrictions)
      return nothing;
    return html`
      <fk-callout variant="danger">
        <strong>Organization has access restrictions</strong><br />
        <p>Due to billing issues, access to organization projects is restricted:
          <ul>
            ${this.org.restrictedCIUploads ? html`
              <li>New reports <strong>cannot</strong> be uploaded to any projects</li>
            ` : nothing}
            ${this.org.restrictedProjectAccess ? html`
              <li>Projects <strong>cannot</strong> be accessed</li>
            ` : nothing}
          </ul>
        </p>
        ${this.org.access === 'owner' ? html`
          <p>Please update organization's <a href=${PageOrganizationBilling.url({ orgSlug: this.org.orgSlug })}>billing information.</a></p>
        ` : html`
          <p>Please notify your organization owner to update billing information.</p>
        `}
      </fk-callout>
    `;
  }

  static styles = [linkStyles, css`
    fk-callout {
      margin-top: var(--sl-spacing-medium);
    }

    a {
      text-decoration: underline;
    }
  `];
}