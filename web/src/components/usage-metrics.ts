import { Task } from '@lit/task';
import * as d3 from 'd3';
import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { assert, humanReadableBytes } from '../utils.js';
import { linkStyles } from './cssstyles.js';

@customElement('usage-metrics')
export class UsageMetrics extends LitElement {
  
  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) days: number = 180;

  private _rangeTask = new Task(this, {
    args: () => [this.days] as const,
    task: async([days]) => {
      const now = new Date();
      return {
        since: d3.utcDay.offset(d3.utcDay.floor(now), -days),
        until: d3.utcDay.ceil(now),
      }
    }
  })

  private _usageMetrics = new Task(this, {
    args: () => [this.orgSlug, this.projectSlug, this._rangeTask.value] as const,
    task: async ([orgSlug, projectSlug, range], { signal }) => {
      assert(orgSlug && range);
      const metrics = projectSlug ?
        await api.project.metrics.query({ orgSlug, projectSlug, }, { signal }) :
        await api.organization.metrics.query({ orgSlug, }, { signal });
      const since = +range.since;
      const until = +range.until;
      return metrics.filter(metric => since <= metric.dayTimestampMs && metric.dayTimestampMs < until);
    },
  });

  override render() {
    const metrics = this._usageMetrics.value ?? [];
    const totalBytes = metrics.reduce((acc, metric) => acc + metric.totalBytes, 0);
    const totalTestRuns = metrics.reduce((acc, metric) => acc + metric.testsCount, 0);
    const totalReports = metrics.reduce((acc, metric) => acc + metric.reportsCount, 0);
    return html`
      <v-box>
        <div class=header>Uploaded ${humanReadableBytes(totalBytes)} of data in the last ${this.days} days</div class=header>
        <usage-chart
          .unit=${'bytes'}
          .since=${this._rangeTask.value?.since}
          .until=${this._rangeTask.value?.until}
          .metrics=${this._usageMetrics.value?.map(a => ({
            timestamp: a.dayTimestampMs,
            value: a.totalBytes,
          }))}
        ></usage-chart>
        <div class=header>Uploaded ${totalReports.toLocaleString()} reports in the last ${this.days} days</div class=header>
        <usage-chart
          .unit=${'runs'}
          .since=${this._rangeTask.value?.since}
          .until=${this._rangeTask.value?.until}
          .metrics=${this._usageMetrics.value?.map(a => ({
            timestamp: a.dayTimestampMs,
            value: a.reportsCount,
          }))}
        ></usage-chart>
        <div class=header>Uploaded ${totalTestRuns.toLocaleString()} test runs in the last ${this.days} days</div class=header>
        <usage-chart
          .unit=${'tests'}
          .since=${this._rangeTask.value?.since}
          .until=${this._rangeTask.value?.until}
          .metrics=${this._usageMetrics.value?.map(a => ({
            timestamp: a.dayTimestampMs,
            value: a.testsCount,
          }))}
        ></usage-chart>
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      flex: auto;
    }

    .header {
      font-weight: var(--sl-font-weight-bold);
    }

    usage-chart {
      height: 200px;
    }
  `];
}
