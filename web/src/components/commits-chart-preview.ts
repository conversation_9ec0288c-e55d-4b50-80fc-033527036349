import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import * as d3 from 'd3';
import { css, html, LitElement, PropertyValues } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { createRef, ref } from 'lit/directives/ref.js';
import { humanReadableMs } from '../utils.js';
import { linkStyles } from './cssstyles.js';

interface Metric {
  value(stats: WireTypes.CommitStats): number;
  format(): ReturnType<typeof d3.format>;
  color?: string,
}

@customElement('commits-chart-preview')
export class CommitsChartPreview extends LitElement {
  static TEST_FLAKINESS_RATIO: Metric = {
    value: ({ testStats: testStatsOutcomes }: WireTypes.CommitStats) => {
      return testStatsOutcomes.flaked / (testStatsOutcomes.expected + testStatsOutcomes.unexpected + testStatsOutcomes.flaked + testStatsOutcomes.skipped);
    },
    format: () => d3.format('.0%'),
    color: '#ffc107',
  };

  static TEST_FAILURES_RATIO: Metric = {
    value: ({ testStats: testStatsOutcomes }: WireTypes.CommitStats) => {
      return testStatsOutcomes.unexpected / (testStatsOutcomes.expected + testStatsOutcomes.unexpected + testStatsOutcomes.flaked + testStatsOutcomes.skipped);
    },
    format: () => d3.format('.0%'),
    color: '#e53935',
  };

  static PASSED_TESTS: Metric = {
    value: ({ testStats }: WireTypes.CommitStats) => {
      return testStats.expected;
    },
    format: () => d3.format('.0f'),
    color: '#43a047',
  };

  static FAILED_TESTS: Metric = {
    value: ({ testStats }: WireTypes.CommitStats) => {
      return testStats.unexpected;
    },
    format: () => d3.format('.0f'),
    color: '#e53935',
  };

  static FLAKED_TESTS: Metric = {
    value: ({ testStats: testStatsOutcomes }: WireTypes.CommitStats) => {
      return testStatsOutcomes.flaked;
    },
    format: () => d3.format('.0f'),
    color: '#ffc107',
  };

  static SKIPPED_TESTS: Metric = {
    value: ({ testStats: testStatsOutcomes }: WireTypes.CommitStats) => {
      return testStatsOutcomes.skipped;
    },
    format: () => d3.format('.0f'),
    color: '#9e9e9e',
  };

  static TEST_AVG_DURATION: Metric = {
    value: (stats: WireTypes.CommitStats) => {
      return Math.round(stats.durationMs.sum / stats.durationMs.count);
    },
    format: () => humanReadableMs,
  }

  static TEST_TOTAL_CPU_TIME: Metric = {
    value: (stats: WireTypes.CommitStats) => {
      return stats.durationMs.sum;
    },
    format: () => humanReadableMs,
  }

  static ERROR_IMPACTED_TESTS: Metric = {
    value: ({ impactedTests }: WireTypes.CommitStats) => {
      return impactedTests;
    },
    format: () => String,
  };

  static ERROR_IMPACTED_ENVS: Metric = {
    value: ({ impactedTimelines: impactedEnvs }: WireTypes.CommitStats) => {
      return impactedEnvs;
    },
    format: () => String,
  };
  
  @property({ attribute: false }) commitStats?: WireTypes.CommitStats[];
  @property({ attribute: false }) metric?: Metric;
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;

  private _chart = createRef<SVGElement>();

  // private _xScale = d3.scaleTime();
  private _xScale = d3.scalePoint<string>();
  private _yScale = d3.scaleLinear();
  private _xAxis = d3.axisBottom(this._xScale);
  private _yAxis = d3.axisLeft(this._yScale).tickFormat(d3.format('.0%'));

  override render() {
    return html`
      <slot></slot>
      <sl-resize-observer @sl-resize=${() => this._updateChart() }>
        <svg id=chart ${ref(this._chart)}>
          <g id=xaxis></g>
          <g id=yaxis></g>

          <g id=timeseries>
            <path id=edge></path>
          </g>
          <g id=selection>
          </g>
        </svg>
      </sl-resize-observer>
    `;
  }

  protected override updated(_changedProperties: PropertyValues): void {
    this._updateChart();
  }

  private _updateChart() {
    const data = this.commitStats;
    const boundingBox = this._chart.value?.getBoundingClientRect();
    const chart = this._chart.value;
    const metric = this.metric;

    if (!data || !boundingBox || !chart || !metric || !this.since || !this.until)
      return;

    const WIDTH = boundingBox.width;
    const HEIGHT = boundingBox.height;

    const PADDING_TOP = 20;
    const PADDING_RIGHT = 20;
    const YAXIS_SIZE = 50;
    const XAXIS_SIZE = 20;

    this._xScale
      .domain(data.map(d => d.commit.commitId))
      .range([YAXIS_SIZE, WIDTH - PADDING_RIGHT]);

    const extent = d3.extent(data.map(metric.value)) as [number, number];
    const maxExtent = Math.abs(extent[1]) < 1e-9 ? 1 : Math.ceil(extent[1]*100 * 1.3)/100;
    this._yScale
      .domain([0, maxExtent])
      .range([HEIGHT - XAXIS_SIZE, PADDING_TOP]);

    const uniqueTicks = new Set(this._yScale.ticks(5).map(tick => Math.round(tick * 100)));
    const yTicks = [...uniqueTicks].map(d => d / 100);
    this._yAxis.tickValues(yTicks.filter(tick => Number.isInteger(tick)));

    this._yAxis.tickFormat(metric.format());
    this._xAxis.tickValues([]);

    const dayTicks= d3.timeDays(d3.timeDay.floor(this.since), d3.timeDay.ceil(this.until));
    // console.log(dayTicks);
    // this._xAxis.tickValues(dayTicks);
    // this._xAxis.tickFormat((date, index) => index === 0 || index === dayTicks.length - 1 ? d3.timeFormat("%b %d")(date as Date) : '');

    d3.select(chart)
      .select<SVGGElement>('#xaxis')
      .call(this._xAxis)
      .attr('transform', `translate(0, ${HEIGHT - XAXIS_SIZE})`);
  
    d3.select(chart)
      .select<SVGGElement>('#yaxis')
      .call(this._yAxis)
      .attr('transform', `translate(${YAXIS_SIZE}, 0)`);

    const line = d3.line<WireTypes.CommitStats>().curve(d3.curveMonotoneX);
    line.x(d => this._xScale!(d.commit.commitId)!);
    line.y(d => this._yScale(metric.value(d)));

    d3.select(chart)
      .select('#edge')
      .attr('d', line(data))
      .attr('stroke', metric.color ?? 'var(--sl-color-neutral-500)');
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      flex: auto;
    }

    :host[selected-commit-id] {
      cursor: pointer;
    }

    #chart {
      height: 100%;
      width: 100%;
      position: relative;
    }

    #edge {
      stroke-width: 2px;
      fill: none;
    }

    #yaxis .tick line {
      opacity: 0.1;
    }

    sl-popup {
      --arrow-color: var(--sl-color-neutral-400);
      z-index: 100;
    }

    .popup-content {
      z-index: 100;
      padding: var(--sl-spacing-medium);
      display: block;
      background: white;
      border: 1px solid var(--sl-color-neutral-300);
    }
  `];
}
