import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { PageOrganization } from '../page-organization.js';
import { PageProjectCollaborators } from '../page-project-collaborators.js';
import { PageProjectSettings } from '../page-project-settings.js';
import { PageProjectUsage } from '../page-project-usage.js';
import { PageReport } from '../page-report.js';
import { linkStyles } from './cssstyles.js';

@customElement('project-header')
export class ProjectHeader extends LitElement {
  @consume({ context: contexts.project, subscribe: true }) @state() private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.projectCollaborators, subscribe: true }) @state() private _projectCollaborators?: ContextType<typeof contexts.projectCollaborators>;

  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) submenu?: string;

  override render() {
    const orgSlug = this.orgSlug;
    const projectSlug = this.projectSlug;
    return html`
      <app-header>
        <sl-breadcrumb>
          <span slot="separator">/</span>
          ${this.orgSlug ? html`
            <sl-breadcrumb-item href=${this.orgSlug ? PageOrganization.url({ orgSlug: this.orgSlug }) : nothing }>${this.orgSlug ?? nothing}</sl-breadcrumb-item>
          ` : nothing}
          ${orgSlug && projectSlug ? html`
            <sl-breadcrumb-item href=${orgSlug && projectSlug && PageReport.url({ orgSlug, projectSlug })}>
              <h-box style="gap: var(--sl-spacing-2x-small);"><span><b>${projectSlug}</b></span><project-visibility value=${this._project?.visibility}></project-visibility></h-box>
            </sl-breadcrumb-item>
          ` : nothing}
        </sl-breadcrumb>

        ${this.orgSlug && this.projectSlug ? html`
          <app-header-submenu
            href=${PageReport.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'report'}
          ><sl-icon name=pie-chart></sl-icon>Report</app-header-submenu>
          <app-header-submenu
            href=${PageProjectCollaborators.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'collaborators'}
          ><sl-icon name=people></sl-icon>Collaborators${
            this._projectCollaborators === undefined
              ? html`<sl-skeleton effect=pulse style="width: 2ch"></sl-skeleton>`
              : html`<sl-tag size=small pill>${this._projectCollaborators.length}</sl-tag>`
          }</app-header-submenu>
          <app-header-submenu
            href=${PageProjectUsage.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'usage'}
          ><sl-icon name=speedometer2></sl-icon>Usage</app-header-submenu>
          <app-header-submenu
            href=${PageProjectSettings.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'settings'}
          ><sl-icon name=gear></sl-icon>Settings</app-header-submenu>
        ` : nothing}
      </app-header>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    project-visibility {
      font-size: var(--sl-font-size-small);
    }

    sl-breadcrumb-item::part(base) {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-normal);
    }

    sl-breadcrumb-item::part(label) {
      color: var(--sl-color-neutral-800);
    }

    sl-tag::part(base) {
      height: 16px;
    }
  `];
}
