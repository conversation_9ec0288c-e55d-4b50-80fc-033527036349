import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { SlSelectEvent } from '@shoelace-style/shoelace';
import { api } from '../api.js';

@customElement('project-collaborator')
export class ProjectCollaborator extends LitElement {

  @property({ attribute: false }) project?: WireTypes.Project;
  @property({ attribute: false }) user?: WireTypes.User;
  @property({ attribute: false }) projectRole?: WireTypes.ProjectRole;

  override render() {
    const user = this.user;
    const project = this.project;
    const projectRole = this.projectRole;

    if (!user || !project)
      return nothing;

    const disabled = !this.project || this.project.access !== 'editor';
    return html`
      <fk-user .user=${user}>
        <sl-dropdown>
          <sl-button ?disabled=${disabled} slot="trigger" caret>${this.projectRole ?? 'No Access'}</sl-button>
          <sl-menu @sl-select=${async (event: SlSelectEvent) => {
            let newRole: 'editor'|'viewer' | undefined;
            if (event.detail.item.value === 'remove-access')
              newRole = undefined;
            else if (event.detail.item.value === 'make-viewer')
              newRole = 'viewer';
            else if (event.detail.item.value === 'make-editor')
              newRole = 'editor';
            await api.project.shareProject.mutate({
              orgSlug: project.org.orgSlug,
              projectSlug: project.projectSlug,
              userId: user.userId,
              access: newRole,
            });
            this.projectRole = newRole;
          }}>
            <sl-menu-item value='make-viewer' type=checkbox ?checked=${projectRole === 'viewer'}>Viewer</sl-menu-item>
            <sl-menu-item value='make-editor' type=checkbox ?checked=${projectRole === 'editor'}>Editor</sl-menu-item>
            ${projectRole ? html`
              <sl-divider></sl-divider>
              <sl-menu-item value='remove-access'>Remove access</sl-menu-item>  
            ` : nothing}
        </sl-dropdown>
      </fk-user>
    `;
  }

  static styles = [css`
  `]
}
