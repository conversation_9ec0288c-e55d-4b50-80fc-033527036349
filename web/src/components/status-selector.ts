import { Field } from '@flakiness/server/common/fql/fields.js';
import { Filter } from '@flakiness/server/common/fql/filter.js';
import { Operator } from '@flakiness/server/common/fql/operators.js';
import { ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement } from 'lit/decorators.js';
import { linkStyles } from '../components/cssstyles.js';
import { consume, contexts } from '../contexts.js';

@customElement('status-selector')
class StatusSelector extends LitElement {
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  private _url(value?: string) {
    if (!this._fql || !this._link)
      return undefined;
    let fql = this._fql.clearStatusFilters();

    if (value) {
      const filter = new Filter('status', Field.statusToString.STATUS, Operator.stringOperators.EQ, [value], false);
      fql = fql.toggleFilter(filter);
    }

    return this._fql && this._link && this._link.render(fql.serialize());
  }

  render() {
    return html`
      <sl-button-group>
      <sl-button href=${this._url('fire') ?? nothing}><sl-icon name=fire></sl-icon></sl-button>
      <sl-button href=${this._url() ?? nothing}>All</sl-button>
        <sl-button href=${this._url('failed') ?? nothing}>Failed</sl-button>
        <sl-button href=${this._url('flaked') ?? nothing}>Flaked</sl-button>
        <sl-button href=${this._url('skipped') ?? nothing}>Skipped</sl-button>
        <sl-button href=${this._url('passed') ?? nothing}>Passed</sl-button>
      </sl-button-group>
    `;
  }

  static styles = [linkStyles, css`
  `];
}