import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import { SlDialog, SlInput, SlSelect, SlSwitch } from '@shoelace-style/shoelace';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { api } from '../api.js';
import { assert } from '../utils.js';
import { linkStyles } from './cssstyles.js';

type PlanConfig = {
  name: string,
  seats?: number,
  orgSlug?: string,
  trialDays?: number,
  maxDataRetentionDays?: number,
  priceIdSeats?: string,
  priceIdStorage?: string,
  priceIdTestRuns?: string,
}

@customElement('new-product-plan')
class NewProductPlan extends LitElement {
  @property({ attribute: false }) orgSlug?: string;

  @query('#plan-name') private _fPlanName?: SlInput;
  @query('#annual') private _fAnnual?: SlSwitch;
  @query('#seats-count') private _fSeatsCount?: SlInput;
  @query('#data-retention') private _fDataRetention?: SlInput;
  @query('#trial-days') private _fTrialDays?: SlInput;
  @query('#seats-price') private _fPriceSeats?: SlSelect;
  @query('#storage-price') private _fPriceStorage?: SlSelect;
  @query('#testruns-price') private _fPriceTestRuns?: SlSelect;
  @query('#new-plan-dialog') private _newPlanDialog?: SlDialog;

  @state() private _billing: WireTypes.BillingPeriod = 'month';
  @state() private _planName?: string;
  @state() private _seats?: number;
  @state() private _trialDays?: number;
  @state() private _maxDataRetentionDays?: number;
  @state() private _priceIdSeats?: string;
  @state() private _priceIdStorage?: string;
  @state() private _priceIdTestRuns?: string;

  private _planPreviewTask = new Task(this, {
    args: () => [
      this.orgSlug, this._planName, this._seats, this._trialDays, this._maxDataRetentionDays, this._priceIdSeats, this._priceIdStorage, this._priceIdTestRuns
    ] as const,
    task: async ([orgSlug, name, seats, trialDays, maxDataRetentionDays, priceIdSeats, priceIdStorage, priceIdTestRuns], { signal }) => {
      assert(name);
      const config: PlanConfig = {
        name,
        seats,
        orgSlug,
        trialDays,
        maxDataRetentionDays,
        priceIdSeats,
        priceIdStorage,
        priceIdTestRuns,
      };
      const plan = await api.productPlans.previewPlan.mutate(config, { signal });
      return plan ? { plan, config } : undefined;
    },
  });

  private _pricesTask = new Task(this, {
    args: () => [],
    task: async ([], { signal }) => {
      return await api.productPlans.listPrices.query(undefined, { signal });
    },
  });

  private _updatePlanConfiguration() {
    this._billing = this._fAnnual?.checked ? 'year' : 'month';
    this._planName = this._fPlanName?.value;
    this._seats = this._fSeatsCount?.value ? parseInt(this._fSeatsCount?.value, 10) : undefined;
    this._trialDays = this._fTrialDays?.value ? parseInt(this._fTrialDays?.value, 10) : undefined;
    this._maxDataRetentionDays = this._fDataRetention?.value ? parseInt(this._fDataRetention?.value, 10) : undefined;
    this._priceIdSeats = (this._fPriceSeats?.value as string) || undefined;
    this._priceIdStorage = (this._fPriceStorage?.value as string) || undefined;
    this._priceIdTestRuns = (this._fPriceTestRuns?.value as string) || undefined;
  }

  render() {
    const previewPlan = this._planPreviewTask.value;
    const prices = this._pricesTask?.value;
    const seatPrices = prices?.seats.filter(price => price.billing === this._billing) ?? [];
    const storagePrices = prices?.storage.filter(price => price.billing === this._billing) ?? [];
    const testRunPrices = prices?.testRuns.filter(price => price.billing === this._billing) ?? [];
    return html`
      <sl-icon-button size=small name=plus-circle @click=${() => this._newPlanDialog?.show()}></sl-icon-button>
      <sl-dialog style="--width: 50vw;" id=new-plan-dialog label="New Product Plan">
        <v-box>
          ${this.orgSlug ? html`
            <fk-callout variant=tip>This product plan will be available <strong>ONLY</strong> to <strong>"${this.orgSlug}"</strong> organization
            </fk-callout>
          ` : nothing}
          <h-box style="gap: var(--sl-spacing-2x-large)">
            <v-box @sl-input=${this._updatePlanConfiguration}>
              <sl-switch id=annual>Annual</sl-switch>
              <sl-input id=plan-name label="Plan Name"></sl-input>
              <sl-input id=trial-days label="Trial Days" type="number" placeholder="Number" min=0 max=723></sl-input>
              <sl-input id=seats-count label="Seats Count" type="number" placeholder="Number" min=0 max=10000></sl-input>
              <sl-input id=data-retention label="Data Retention" type="number" placeholder="Number" min=0 max=10000 value=180>
                <div slot=suffix>days</div>
              </sl-input>
              <sl-select id=seats-price label="Price per seat" value=${seatPrices.at(0)?.priceId}>
                ${seatPrices.map(price => html`
                  <sl-option value=${price.priceId}>${price.description}</sl-option>
                `)}
              </sl-select>
              <sl-select id=storage-price label="Storage prices" value=${storagePrices.at(0)?.priceId}>
                ${storagePrices.map(price => html`
                  <sl-option value=${price.priceId}>${price.description}</sl-option>
                `)}
              </sl-select>
              <sl-select id=testruns-price label="Test Runs prices" value=${testRunPrices.at(0)?.priceId}>
                ${testRunPrices.map(price => html`
                  <sl-option value=${price.priceId}>${price.description}</sl-option>
                `)}
              </sl-select>
            </v-box>

            <product-plans-view
              no-enterprise
              no-subscribe
              .plans=${previewPlan ? [previewPlan.plan] : []}
            ></product-plans-view>
          </h-box>
        </v-box>

        <sl-button variant="primary" slot=footer ?disabled=${!previewPlan} @click=${async () => {
          if (!previewPlan)
            return;
          this._newPlanDialog?.hide();
          const plan = await api.productPlans.create.mutate(previewPlan.config);
          this.dispatchEvent(new CustomEvent<WireTypes.ProductPlan>('fk-select', {
            detail: plan
          }));
        }}>create</sl-button>
      </sl-dialog>
    `;
  }

  static styles = [linkStyles, css`
    product-plans {
      flex: auto;
      font-size: var(--sl-font-size-small);
    }
  `];
}
