import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from '../components/cssstyles.js';

@customElement('test-title')
class TestTitle extends LitElement {
  @property({ attribute: false }) test?: WireTypes.Test;
  @property({ type: Boolean }) tags?: boolean;

  render() {
    if (!this.test)
      return nothing;
    const elements: TemplateResult[] = [];
    for (const title of this.test.titles) {
      elements.push(html`<span>${title}</span>`);
      elements.push(html`<sl-icon name=chevron-right></sl-icon>`)
    }
    elements.pop();

    if (!this.tags || !this.test.tags || !this.test.tags.length)
      return elements;

    const tags = this.test.tags.map(tag => html`<test-tag .tag=${tag}></test-tag>`);

    return html`<span>${elements} ${tags}</span>`;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      align-items: center;
    }
    sl-icon {
      font-size: 73%;
    }
  `];
}