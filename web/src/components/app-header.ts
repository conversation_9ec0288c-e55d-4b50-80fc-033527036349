import { consume, ContextType } from '@lit/context';
import { SlDrawer } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import ms from 'ms';
import { contexts } from '../contexts.js';
import { PageAdministrationBilling } from '../page-administration-billing.js';
import { PageAdministrationQueues } from '../page-administration-queues.js';
import { getInitials } from '../utils.js';
import { linkStyles } from './cssstyles.js';

@customElement('app-header')
class AppHeader extends LitElement {
  @consume({ context: contexts.user, subscribe: true }) @state() private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.serverInfo, subscribe: true }) @state() private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  @property({}) customTitle?: string;

  @query('sl-drawer') private _drawer?: SlDrawer;

  override render() {
    const licenseValidUntil = this._serverInfo?.deploymentLicense.validUntilMs ?? Infinity;
    const isLicenseExpiringSoon = Math.abs(Date.now() - licenseValidUntil) < ms('30 days');
    const isLicenseExpiringVerySoon = Math.abs(Date.now() - licenseValidUntil) < ms('14 days');
    return html`
        ${isLicenseExpiringSoon ? html`
          <section id=expiring-license class=${classMap({ emergency: isLicenseExpiringVerySoon})}>
            <div>
              <span>flakiness.io license will expire <b><sl-relative-time date=${new Date(this._serverInfo?.deploymentLicense.validUntilMs!)}></sl-relative-time></b></span>– 
              <span>please reach out to us at <fk-contact-us></fk-contact-us> to extend the license.</span>
            </div>
          </section>
        ` : nothing}
        <header>
          <h-box class=first-line>
            <a class=logo header-link href='/'><img width=32px height=32px src="/logo.svg"></a>
            ${this.customTitle ? html`<b>${this.customTitle}</b>` : nothing}
            <slot></slot>

            <x-filler></x-filler>

            <a href='/docs'>Documentation</a>

            ${this._user ? html`
              <button class=reset @click=${() => this._drawer?.show()}>
                <sl-avatar class=${classMap({ isSuperUser: this._user.isSuperUser })} image=${this._user.avatarUrl} initials=${getInitials(this._user.userName)}></sl-avatar>
              </button>
            ` : html`
              <a href='/login/github'>Login</a>
            `}
          </h-box>
          <h-box class=second-line>
            <slot name=second-line></slot>
          </h-box>
        </header>

        <sl-drawer no-header>
          ${this._user ? html`
            <section class=header>
              <sl-avatar class=${classMap({ isSuperUser: this._user.isSuperUser })} image=${this._user.avatarUrl} initials=${getInitials(this._user.userName)}></sl-avatar>
              <strong class=name>${this._user?.userLogin}</strong>
              <div class=full-name>${this._user.userName}</div>
              <sl-icon-button @click=${() => this._drawer?.hide()} name="x-lg"></sl-icon-button>
            </section>
            <sl-divider></sl-divider>
            ${this._user.isSuperUser ? html`
              <a header-link href=${PageAdministrationQueues.url()}>Administration: Jobs</a>
              ${this._serverInfo?.enabledBilling ? html`<a header-link href=${PageAdministrationBilling.url()}>Administration: Billing</a>` : nothing}
            ` : nothing}
            <a header-link href="/logout">Sign out</a>
          ` : nothing}
          ${this._user?.isSuperUser ? html`
            <v-box slot="footer" style="text-align: left;">
              <sl-divider></sl-divider>
              <div>
                Flakiness.io version <b><a-ext href=${`https://github.com/flakiness/flakiness/commits/${COMMIT_SHA}`}>v${PACKAGE_JSON_VERSION}</a-ext></b> built <b><sl-relative-time date=${new Date(BUILD_TIMESTAMP).toISOString()}></sl-relative-time></b>
              </div>
              <div>
                License valid until: <sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" hour-format="12" date=${new Date(this._serverInfo?.deploymentLicense.validUntilMs!)}></sl-format-date>
              </div>
              <div>
                Billing is: <strong>${this._serverInfo?.enabledBilling ? 'enabled' : 'disabled'}</strong>
              </div>
              <div>
                Server hostname: <strong>${this._serverInfo?.serverHostname}</strong>
              </div>
            </v-box>
          ` : nothing}
        </sl-drawer>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    .first-line {
      gap: var(--sl-spacing-medium);
      flex-grow: 1;
      padding: var(--sl-spacing-medium) var(--sl-spacing-medium);
    }

    .second-line {
      gap: var(--sl-spacing-2x-small);
      padding: 0 var(--sl-spacing-medium);
    }

    header {
      display: block;
      background-color: var(--fk-color-canvas);
      gap: var(--sl-spacing-medium);
      border-bottom: var(--sl-panel-border-width) solid var(--sl-panel-border-color);

      .logo {
        width: 32px;
        display: flex;
      }

      & button {
        background: none;
        color: inherit;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
      }
    }

    #expiring-license {
      background-color: var(--sl-color-warning-200);
      padding: var(--sl-spacing-2x-small);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1ex;

      &.emergency {
        background-color: var(--sl-color-danger-200);
      }
    }

    sl-avatar {
      --size: 2rem;
      position: relative;
      z-index: 1;

      &.isSuperUser::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, 
          #ff0000, #ff9a00, #d0de21, #4fdc4a, 
          #3fdad8, #2fc9e2, #1c7fee, #5f15f2, 
          #ba0cf8, #fb07d9);
        border-radius: 50%;
        z-index: 0;
      }
    }

    sl-drawer::part(body) {
      display: grid;
      gap: var(--sl-spacing-small);
      align-content: start;
    }

    sl-drawer {
      a[header-link] {
        padding: var(--sl-spacing-3x-small) var(--sl-spacing-x-small);
        border-radius: var(--sl-border-radius-medium);
        text-decoration: none;
        color: var(--sl-color-neutral-900);
        font-size: var(--sl-font-size-small);
    
        &:hover {
          background-color: var(--fk-color-canvas);
        }
      }

      & .header {
        display: grid;
        grid-tempate-rows: auto auto;
        grid-template-columns: auto auto 1fr auto;
        column-gap: var(--sl-spacing-x-small);
        align-content: start;
        justify-content: start;

        & sl-avatar {
          grid-column: 1;
          grid-row: 1/3;
          align-self: center;
          justify-self: center;
        }

        & sl-icon-button {
          grid-column: 4;
          grid-row: 1/3;
        }

        & .name {
          grid-column: 2;
          grid-row: 1;
          font-size: var(--sl-font-size-x-small);
        }

        & .full-name {
          grid-column: 2;
          grid-row: 2;
          font-size: var(--sl-font-size-small);
          color: var(--sl-color-neutral-500);
        }
      }
    }
  `];
}
