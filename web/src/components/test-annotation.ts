import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('test-annotation')
class TestAnnotation extends LitElement {
  @property({ attribute: false }) annotation?: WireTypes.TestAnnotation;

  render() {
    if (!this.annotation)
      return nothing;
    let type: string | undefined;
    if (this.annotation.type === 'skip')
      type = 'warning';
    else if (this.annotation.type === 'fail')
      type = 'danger';
    else if (this.annotation.type === 'fixme')
      type = 'danger';
    else if (this.annotation.type === 'slow')
      type = 'neutral';
    let textRender = html`${this.annotation.type}`;
    if (this.annotation.description && URL.canParse(this.annotation.description))
      textRender = html`<a-ext href=${this.annotation.description}>${textRender}</a-ext>`;
    const tag = html`
      <sl-tag pill size=small variant=${type}>${textRender}</sl-tag>
    `;
    if (!this.annotation.description)
      return tag;
    return html`
      <sl-tooltip>
        <div slot=content>${this.annotation.description}</div>
        ${tag}
      </sl-tooltip>
    `;
  }


  static styles = [css`
    sl-tooltip {
      --max-width: 50rem;

      div[slot=content] {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: pre;
      }
    }
    sl-tag {
      
      display: inline-block;
    }
  `];
}
