import { Field } from '@flakiness/server/common/fql/fields.js';
import { Filter } from '@flakiness/server/common/fql/filter.js';
import { Operator } from '@flakiness/server/common/fql/operators.js';
import { Ranges } from '@flakiness/server/common/ranges.js';
import { ContextType, consume } from '@lit/context';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { linkStyles } from './cssstyles.js';
import { highlight } from './hl-span.js';

@customElement('test-tag')
class TestTag extends LitElement {
  @property({ attribute: false }) tag?: string;
  @property({ attribute: false }) hl?: Ranges.Ranges<number>;

  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  render() {
    if (!this.tag)
      return nothing;
    const filter = new Filter('tag', Field.testToTest, Operator.tagOperators.TAG_IS, [this.tag], false);
    const url = this._fql && this._link && this._link.render(this._fql.toggleFilter(filter).serialize());

    return html`<a href=${url} class=body>#${highlight(this.tag, this.hl)}</a>`
  }

  static styles = [linkStyles, css`
    .body {
      color: var(--sl-color-neutral-400) !important;
    }
  `];
}
