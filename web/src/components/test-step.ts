
import { FlakinessReport } from '@flakiness/report';
import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { contexts } from '../contexts.js';
import { githubLinks } from '../githubLinks.js';
import { humanReadableMs } from '../utils.js';
import { linkStyles } from './cssstyles.js';

@customElement('test-step')
class TestStep extends LitElement {
  @property({ type: Boolean, reflect: true }) open?: boolean;
  @property({ type: Boolean, attribute: false }) step?: FlakinessReport.TestStep;
  @property({ type: Boolean, attribute: false }) commitId?: FlakinessReport.CommitId;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  override render() {
    const clickable = !!this.step?.steps?.length || !!this.step?.snippet;
    return html`
      <v-box>
        <h-box class=${classMap({ header: true, clickable })} header @click=${clickable ? () => { this.open = !this.open; } : nothing }>
          <sl-icon name=${this.open ? 'chevron-down' : 'chevron-right'}></sl-icon>
          <fk-status ?success=${!this.step?.error}></fk-status>
          <div class=step-description>
            ${this.step?.title}
            ${this.step?.location && this._project && this.commitId ? html`<span class=step-location>— <a-ext href=${githubLinks.fileUrl(this._project, this.commitId, this.step.location.file, this.step.location.line)}>${this.step.location.file}:${this.step.location.line}</a-ext></span>` : nothing}
          </div>
          <x-filler></x-filler>
          <span class=step-duration>${humanReadableMs(this.step?.duration ?? 0)}</span>
        </h-box>
        ${clickable ? html`
          ${this.step?.snippet ? html`<ansi-text class=snippet .text=${this.step?.snippet}></ansi-text>` : nothing}
          <div class=substeps>
            <v-box>
              ${this.step?.steps?.map(step => html`<test-step .step=${step}></test-step>`)}
            </v-box>
          </div>
        ` : nothing}
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    .substeps, .snippet {
      display: none;
      margin-left: var(--sl-spacing-2x-large);
    }

    .header sl-icon {
      visibility: hidden;
      flex: none;
    }

    .header.clickable {
      cursor: pointer;

      sl-icon {
        visibility: visible;        
      }
    }

    .step-description {
      white-space: nowrap;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .step-duration {
      white-space: nowrap;
      flex: none;
    }

    .step-location {
      color: var(--sl-color-neutral-500);
    }

    :host {
      display: block;
    }

    :host([open]) {
      .header {
      }

      .substeps, .snippet {
        display: block;
      }
    }
  `];
}