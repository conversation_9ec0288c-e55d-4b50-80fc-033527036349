import { TestOutcomes } from '@flakiness/server/common/stats/testOutcomes.js';
import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import * as d3 from 'd3';
import { css, html, LitElement, nothing, PropertyValues } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { api } from '../api.js';
import { consume, contexts } from '../contexts.js';
import { assert } from '../utils.js';
import { linkStyles } from './cssstyles.js';

type DataPoint = {
  date: Date,
  regressed: number,
  expected: number,
  unexpected: number,
  skipped: number,
  flaked: number,
}

@customElement('chart-test-outcomes')
export class ChartTestOutcomes extends LitElement {

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) head?: string;
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;
  @property({ attribute: false }) timelineSplit?: TimelineSplit;
  @property({ attribute: false }) fql?: string;

  @query("svg") private _svg?: SVGElement;

  private _dataTask = new Task(this, {
    args: () => [
      this._project, this.head, this.since, this.until, this.fql, this.timelineSplit
    ] as const,
    task: async ([
      project, head, since, until, fql, timelineSplit,
    ], { signal }) => {
      assert(project && head && since && until && timelineSplit);
      const days = d3.timeDay.range(since, until);
      const opts = days.map(day => ({
        head: head,
        sinceTimestamp: +d3.timeDay.floor(day),
        untilTimestamp: +d3.timeDay.offset(d3.timeDay.floor(day), 1),
      }));

      const outcomes = await api.report.chronologicalOutcomes.mutate({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        commitOptions: opts,
        historyDepthDays: 30,
        timelineSplit: timelineSplit.serialize(),
        fql,
      }, { signal });

      return outcomes.map((outcome, index) => {
        const result = TestOutcomes.newOutcomeCounts();
        for (const timelineOutcome of outcome)
          TestOutcomes.accumulateOutcomeCounts(result, TestOutcomes.newOutcomeCounts(timelineOutcome.chronologicalOutcomes as TestOutcomes.TestOutcomes));
        return {
          ...result,
          date: days[index],
        } satisfies DataPoint;
      });
    }
  });

  override render() {
    if (!this._dataTask.value)
      return nothing;
    return html`
      <span class=svg-container style="width: 100%; height: 100%">
        <sl-resize-observer @sl-resize=${() => this._updateChart() }>
          <svg>
            <g id=chart></g>
            <g id=y-axis></g>
            <g id=x-axis></g>
          </svg>
        </sl-resize-observer>
      </span>
    `;
  }

  protected updated(changed: PropertyValues) {
    super.updated(changed);
    this._updateChart();
  }

  private _updateChart() {
    const chartBoundingBox = this._svg?.getBoundingClientRect();

    const data = this._dataTask.value;
    if (!chartBoundingBox || !chartBoundingBox.width || !chartBoundingBox.height || !this._svg || !data)
      return;
    const svg = d3.select(this._svg);

    const margins = {
      left: 50,
      right: 20,
      top: 10,
      bottom: 20,
    };

    const WIDTH = chartBoundingBox.width;
    const HEIGHT = chartBoundingBox.height;

    const keys = ['regressed', 'expected', 'unexpected', 'skipped', 'flaked'] as const;
    const stack = d3.stack<DataPoint>().keys(keys);

    const minTime = d3.min(data, d => d.date) ?? new Date();
    const maxTime = d3.max(data, d => d.date) ?? new Date();
    const x = d3.scaleTime()
      .domain([minTime, maxTime])
      .range([margins.left, WIDTH - margins.right]);

    const series = stack(data);
    const minValue = d3.min(series.flat().flat()) ?? 0;
    const maxValue = d3.max(series.flat().flat()) ?? minValue + 1;
    const y = d3.scaleLinear()
      .domain([minValue, maxValue])
      .range([HEIGHT - margins.bottom, margins.top]);

    const area = d3.area<d3.SeriesPoint<DataPoint>>()
      .curve(d3.curveMonotoneX)
      .x((d, i) => x(data[i].date))
      .y0(d => y(d[0]))
      .y1(d => y(d[1]));

    const xAxis = d3.axisBottom<Date>(x)
      .ticks(d3.timeDay.every(1))
      .tickFormat(d3.timeFormat('%b %d'));

    const maxTickCount = Math.floor((HEIGHT - margins.top - margins.bottom) / 15); // one label every ~30px
    const rawTicks = y.ticks(maxTickCount).filter(Number.isInteger);

    const yAxis = d3.axisLeft(y)
      .tickValues(rawTicks)
      .tickFormat(d3.format("d"));

    svg
      .select<SVGGElement>('#x-axis')
      .attr('transform', `translate(0, ${HEIGHT - margins.bottom})`)
      .call(xAxis);

    svg
      .select<SVGGElement>('#y-axis')
      .attr('transform', `translate(${margins.left}, 0)`)
      .call(yAxis);

    svg
      .select<SVGGElement>('#chart')
      .selectAll('path')
      .data(stack(data))
      .join('path')
      .attr('class', (d: d3.Series<DataPoint, string>) => d.key)
      .attr('d', area);
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      flex: auto;
      place-self: stretch;
    }

    path {
      &.regressed {
        fill: var(--fk-color-outcome-regressed);
      }
      &.expected {
        fill: color-mix(in srgb, var(--fk-color-outcome-expected) 50%, white 50%);
      }
      &.unexpected {
        fill: var(--fk-color-outcome-unexpected);
      }
      &.skipped {
        fill: var(--fk-color-outcome-skipped);
      }
      &.flaked {
        fill: var(--fk-color-outcome-flaked);
      }
    }

    .svg-container {
      align-self: stretch;
      justify-self: stretch;
      width: 100%;
      height: 100%;
      position: relative;

      svg {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
      }
    }
  `];
}
