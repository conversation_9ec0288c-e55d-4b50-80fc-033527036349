import { css, html, LitElement } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import slugify from 'slugify';
import { api } from '../api.js';
import { EventTask } from '../contexts.js';
import { assert } from '../utils.js';
import { InputWithStatus } from './input-with-status.js';

export type ProjectNamePicked = {
  orgSlug: string,
  projectName: string,
}

@customElement('pick-project-name')
export class PickProjectName extends LitElement {

  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) initial?: string;
  @property({ type: Boolean }) disabled?: boolean;

  @query('input-with-status') private _input?: InputWithStatus;

  private _validateProjectName = new EventTask(this, 'project-name', {
    autoRun: false,
    args: () => [this.orgSlug, this._input?.value()] as const,
    task: async ([orgSlug, projectName], { signal }): Promise<ProjectNamePicked> => {
      assert(orgSlug && projectName !== undefined);

      if (!projectName.trim().length) {
        this._input?.setStatus(false, 'Project name must not be blank');
      } else if (!(/^[.0-9a-z_-]+$/i).test(projectName)) {
        this._input?.setStatus(false, 'The project name can only contain ASCII letters, digits, and the characters ., -, and _.');
      } else {
        // Small cool-down to not spam server with requests.
        await new Promise(x => setTimeout(x, 200));
        signal.throwIfAborted();
        const projectSlug = slugify.default(projectName);
        const projectExists = await api.project.checkProjectExists.query({ orgSlug, projectSlug }).catch(e => false);
        if (projectExists) {
          this._input?.setStatus(false, `"${projectSlug}" is already taken`);
        } else {
          this._input?.setStatus(true, `"${projectSlug}" is available`);
        }
      }
      assert(this._input?.success());
      return { projectName, orgSlug };
    }
  });

  setStatus(success: boolean, message: string) {
    this._input?.setStatus(success, message);
  }

  override render() {
    return html`
      <input-with-status
        initial=${this.initial}
        placeholder="Project name"
        autocomplete=off
        ?disabled=${this.disabled}
        @sl-input=${() => this._validateProjectName.run()}
      >
        <slot></slot>
      </input-with-status>
    `;
  }

  static styles = [css`
  `];
}