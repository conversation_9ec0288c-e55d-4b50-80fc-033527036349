import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { consume, contexts } from '../contexts.js';
import { githubLinks } from '../githubLinks.js';
import { linkStyles } from './cssstyles.js';

@customElement('fk-commit')
class FKCommit extends LitElement {
  @property({ attribute: false }) commit?: WireTypes.Commit;
  @property() href?: string;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  override render() {
    const commit = this.commit;
    if (!commit)
      return nothing;
    return html`
      <div>
        <a class=message href=${this.href ?? nothing}>
          <span>${commit.message.split('\n')[0].trim()}</span>
          
        </a>

        <h-box style="gap: var(--sl-spacing-2x-small);">
          <a-ext href=${this._project ? githubLinks.commitUrl(this._project, commit.commitId) : nothing} class=details>
            <h-box style="gap: var(--sl-spacing-2x-small);">
              <sl-avatar image=${commit.avatar_url} label=${commit.author} loading=lazy></sl-avatar>
              ${commit.author} commited 
              <sl-relative-time date=${new Date(commit.timestamp).toISOString()}></sl-relative-time>
              </h-box>
            </a-ext>
            <span>•</span>
            <span class=hash>${commit.commitId.substring(0, 7)}</span>
        </h-box>
      </div>
    `;
  }

  static styles = [linkStyles, css`
    sl-avatar {
      --size: 18px; 
    }

    .hash {
      font-family: var(--sl-font-mono);
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }

    .message {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .details {
      gap: var(--sl-spacing-small);
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
  `];
}
