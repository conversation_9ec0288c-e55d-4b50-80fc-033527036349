import { Field } from '@flakiness/server/common/fql/fields.js';
import { Filter } from '@flakiness/server/common/fql/filter.js';
import { Operator } from '@flakiness/server/common/fql/operators.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType, consume } from '@lit/context';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { contexts } from '../contexts.js';

@customElement('report-outcomes')
class ReportOutcomes extends LitElement {
  @property({ attribute: false }) outcomes?: WireTypes.OutcomesCount;

  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  private _renderRate(name: string, from: number, total: number) {
    const rate = total === 0 ? 0 : Math.round(from / total * 100);
    return html`${rate}% ${name} rate (${from}/${total})`;
  }

  override render() {
    const {
      regressed = 0,
      unexpected = 0,
      expected = 0,
      skipped = 0,
      flaked = 0
    } = this.outcomes ?? {};
    const total = regressed + expected + unexpected + skipped + flaked;

    const fql = this._fql;
    const link = this._link;
    
    function statusLink(status: 'passed'|'flaked'|'skipped'|'failed'|'fire') {
      if (!fql || !link)
        return nothing;
      const filter = new Filter('status', Field.statusToString.STATUS, Operator.stringOperators.EQ, [status], false);
      const q = fql.isMatchingFilter(filter) ? fql.clearStatusFilters() : fql.clearStatusFilters().toggleFilter(filter);

      return link.render(q.serialize());
    }

    return html`
      <h-box>
        <v-box>
          <div class=pass-title>Passed</div>
          <a ?empty=${!expected} ?no-color=${unexpected + flaked > 0} href=${statusLink('passed')} class=pass-value>${expected}</a>
          <div class=pass-hint>${this._renderRate('pass', expected, total)}</div>
        </v-box>
        <v-box>
          <div class=regressed-title>New Failures</div>
          <a ?empty=${!regressed} href=${statusLink('fire')} class=regressed-value>${regressed}</a>
          <div class=regressed-hint>${this._renderRate('regressed', regressed, total)}</div>
        </v-box>
        <v-box>
          <div class=fail-title>Failed</div>
          <a ?empty=${!unexpected} href=${statusLink('failed')} class=fail-value>${unexpected}</a>
          <div class=fail-hint>${this._renderRate('failure', unexpected, total)}</div>
        </v-box>
        <v-box>
          <div class=flake-title>Flaked</div>
          <a ?empty=${!flaked} href=${statusLink('flaked')} class=flake-value>${flaked}</a>
          <div class=flake-hint>${this._renderRate('flaked', flaked, total)}</div>
        </v-box>
        <v-box>
          <div class=skip-title>Skipped</div>
          <a ?empty=${!skipped} href=${statusLink('skipped')} class=skip-value>${skipped}</a>
          <div class=skip-hint>${this._renderRate('skipped', skipped, total)}</div>
        </v-box>
      </h-box>
    `;
  }

  static styles = [css`
    :host {
      display: block;
    }

    v-box {
      --gap: var(--sl-spacing-3x-small);
    }

    a {
      display: inline-block;
      text-decoration: none;
  
      &[href]:hover {
        
      }
  
      &:visited, &:link {
        color: inherit;
      }
    }

    .pass-title { }
    a.pass-value {
      color: var(--fk-color-outcome-expected);
    }
    .pass-hint { }

    .regressed-title { }
    a.regressed-value { color: var(--fk-color-outcome-regressed); }
    .regressed-hint { }

    .fail-title { }
    a.fail-value { color: var(--fk-color-outcome-unexpected); }
    .fail-hint { }

    .skip-title { }
    a.skip-value { color: var(--fk-color-outcome-skipped); }
    .skip-hint { }

    .flake-title { }
    a.flake-value { color: var(--fk-color-outcome-flaked); }
    .flake-hint { }

    a[no-color] {
      color: unset;
    }

    a[empty] {
      color: var(--sl-color-neutral-200);
    }

    .pass-title, .fail-title, .skip-title, .flake-title, .regressed-title {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-semibold);
    }

    .pass-value, .fail-value, .skip-value, .flake-value, .regressed-value {
      font-size: var(--sl-font-size-2x-large);
    }

    .pass-hint, .fail-hint, .skip-hint, .flake-hint, .regressed-hint {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
  `];
}