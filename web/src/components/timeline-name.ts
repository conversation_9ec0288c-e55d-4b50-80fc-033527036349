import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { FlakinessReport } from '@flakiness/report';
import { parseTimeline } from '@flakiness/server/common/timelineUtils.js';
import { consume, ContextType } from '@lit/context';
import { classMap } from 'lit/directives/class-map.js';
import { contexts } from '../contexts.js';
import { githubLinks } from '../githubLinks.js';
import { linkStyles } from './cssstyles.js';

@customElement('timeline-name')
export class TimelineName extends LitElement {

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @property({ attribute: false }) timeline?: string;
  @property({ attribute: false }) href?: string|Function;

  override render() {
    const timelineText = this.timeline;
    if (timelineText === undefined)
      return nothing;

    const timeline = parseTimeline(timelineText);

    return html`
      <h-box class=content>
        <v-box style="gap: var(--sl-spacing-2x-small);">
          <a class=${classMap({ clickable: !!this.href })} href=${typeof this.href === 'string' ? this.href : nothing} @click=${typeof this.href === 'function' ? this.href : nothing}>
            <h-box class=timeline-name>
              ${timeline.system ? `[${timeline.system}]` : nothing}
              <div>${(timeline.name ?? '').trim().length ? timeline.name : html`<span class=noname>[no name]</span>`}</div>
              ${timeline.metadata ? `(${timeline.metadata})` : nothing}
            </h-box>
          </a>
          ${timeline.configPath ? html`
            <a-ext class=configpath href=${this._project ? githubLinks.fileUrl(this._project, 'main' as FlakinessReport.CommitId, timeline.configPath) : nothing}>${timeline.configPath}</a-ext>
          ` : nothing}
        </v-box>
      </h-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      flex: none;
    }

    .clickable {
      cursor: pointer;
    }

    sl-icon {
      color: var(--sl-color-neutral-500);
    }

    .timeline-name {
      gap: var(--sl-spacing-2x-small);
      display: flex;
      font-weight: var(--sl-font-weight-semibold);

      &[selected] {
        background-color: var(--fk-color-highlight);
      }
    }

    .noname {
      color: var(--sl-color-neutral-500);
    }

    .configpath {
      color: var(--sl-color-neutral-500);
      font-size: var(--sl-font-size-small);
    }
  `]
}
