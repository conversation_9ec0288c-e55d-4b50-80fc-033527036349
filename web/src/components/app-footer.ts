import { LitElement, css, html } from 'lit';
import { customElement } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('app-footer')
class AppFooter extends LitElement {

  render() {
    return html`
        <div>© 2024–2025 Degu Labs Inc – All Rights Reserved – Reach out <fk-contact-us></fk-contact-us></div>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex: auto;
      align-items: end;
      justify-content: center;
      color: var(--sl-color-neutral-500);
    }

    div {
      margin: var(--sl-spacing-2x-large) 0;
    }
  `];
}
