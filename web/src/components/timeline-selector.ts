import { TimelineSplit } from '@flakiness/server/common/timelineSplit.js';
import { parseTimeline } from '@flakiness/server/common/timelineUtils.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { SlDrawer, SlDropdown, SlHideEvent, SlSelectEvent } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { linkStyles } from './cssstyles.js';
import { ParametersTable } from './parameters-table.js';

const PW_PROJECTS = TimelineSplit.playwrightProjects();
const PW_PROJECTS_WITH_BROWSERS = TimelineSplit.playwrightProjectsWithBrowsers();

@customElement('timeline-selector')
class TimelineSelector extends LitElement {
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) split?: TimelineSplit;
  @property({ attribute: false }) envs?: WireTypes.RunEnvironment[];

  @property({ type: Boolean }) multiple?: boolean;

  @query('parameters-table') _params?: ParametersTable;
  @query('sl-dropdown') _dropdown?: SlDropdown;
  @query('sl-drawer') _drawer?: SlDrawer;

  render() {
    if (!this.split || !this.envs)
      return nothing;

    const pwTimelines = PW_PROJECTS.timelines(this.envs ?? []);
    const pwWithBrowsersTimelines = PW_PROJECTS_WITH_BROWSERS.timelines(this.envs ?? []);

    const renderTimelineSplitMenuItem = (split: TimelineSplit) => {
      return html`
        <sl-menu-item .split=${split} type=checkbox ?checked=${this.split?.isEqual(split)}>
          ${this._renderSplit(split, false)}
        </sl-menu-item>
      `;
    }

    return html`
      <sl-drawer style="--size: 50vw;" label='Timeline Editor' @sl-show=${() => {
        if (this._params)
          this._params.split = this.split;
      }}>
        <v-box style="">
          <parameters-table
            .split=${this.split}
            .envs=${this.envs}
            .multiple=${this.multiple}
          ></parameters-table>
        </v-box>
        <sl-button slot="footer" variant="primary" @click=${(event: SlHideEvent) => {
          this.split = this._params?.split;
          this.dispatchEvent(new CustomEvent<TimelineSplit>('fk-select', { detail: this.split }));
          this._drawer?.hide();
        }}>Accept</sl-button>
      </sl-drawer>

      <sl-dropdown @sl-select=${(event: SlSelectEvent) => {
        const item = event.detail.item;
        if ((item as any).openGranularTimeline) {
          this._drawer?.show();
        } else if ((item as any).split) {
          this.split = (item as any).split;
          this.dispatchEvent(new CustomEvent<TimelineSplit>('fk-select', { detail: this.split }));
        }
      }}>
        <sl-button slot=trigger caret>
          ${this._renderSplit(this.split, true)}
        </sl-button>
        <sl-menu>
          <sl-menu-item>
            <sl-icon src='/masks.svg' slot=prefix></sl-icon>
            Playwright Projects
            <sl-menu slot=submenu>
              ${this.multiple ? html`
                ${renderTimelineSplitMenuItem(PW_PROJECTS)}
                <sl-divider></sl-divider>
              ` : nothing}
              ${pwTimelines.map(timeline => renderTimelineSplitMenuItem(TimelineSplit.fromTimeline(timeline)))}
            </sl-menu>
          </sl-menu-item>
          ${TimelineSplit.someWithBrowserField(this.envs) ? html`
            <sl-menu-item>
              <sl-icon src='/masks.svg' slot=prefix></sl-icon>
              Playwright Projects / browsers
              <sl-menu slot=submenu>
                ${this.multiple ? html`
                  ${renderTimelineSplitMenuItem(PW_PROJECTS_WITH_BROWSERS)}
                  <sl-divider></sl-divider>
                ` : nothing}
                ${pwWithBrowsersTimelines.map(timeline => renderTimelineSplitMenuItem(TimelineSplit.fromTimeline(timeline)))}
              </sl-menu>
            </sl-menu-item>
          ` : nothing}
          <sl-menu-item .openGranularTimeline=${true}>
            <sl-icon name=sliders slot=prefix></sl-icon>
            Custom...
          </sl-menu-item>
        </sl-menu>
      </sl-dropdown>
    `;
  }

  private _renderSplit(split: TimelineSplit, icon: boolean) {
    const result = this._renderSplitInternal(split);
    return icon ? html`${result.icon}${result.content}` : html`${result.content}`;
  }

  private _renderSplitInternal(split: TimelineSplit,): { icon?: TemplateResult<1>, content: TemplateResult<1>} {
    if (PW_PROJECTS_WITH_BROWSERS.isEqual(split)) {
      return {
        icon: html`<sl-icon src='/masks.svg' slot=prefix></sl-icon>`,
        content: html`All Playwright Projects / All Browsers`,
      }
    }

    if (PW_PROJECTS.isEqual(split)) {
      return {
        icon: html`<sl-icon src='/masks.svg' slot=prefix></sl-icon>`,
        content: html`All Playwright Projects`,
      }
    }

    if (split.isPlaywrightProject() || split.isPlaywrightProjectWithBrowser()) {
      const parsed = parseTimeline(split.timeline());
      return {
        icon: html`<sl-icon src='/masks.svg' slot=prefix></sl-icon>`, 
        content: html`${parsed.name} ${parsed.metadata ? `/ ${parsed.metadata}` : nothing}`
      };
    }

    // If there's a single timeline...

    if (split.isUnifiedTimeline()) {
      return {
        icon: html`<sl-icon src='/masks.svg' slot=prefix></sl-icon>`, 
        content: html`Playwright Projects`,
      };
    }

    const timelines = this.multiple ? split.timelines(this.envs ?? []) : [split.timeline()];
    const timeline = timelines.length === 1 ? parseTimeline(split.timeline()) : undefined;
    if (timeline) {
      return {
        icon: html`<sl-icon name='graph-up' slot=prefix></sl-icon>`,
        content: html`
          ${timeline.system ? `[${timeline.system}]` : nothing}
          ${timeline.name ?? timeline.configPath}
          ${timeline.metadata ? `/ ${timeline.metadata}` : nothing}
        `,
      };
    }

    return {
      icon: html`<sl-icon name=graph-up slot=prefix></sl-icon>`,
      content: html`Custom`,
    };
  }

  static styles = [linkStyles, css`
    timeline-selector-view {
      background-color: white;
    }

    sl-dialog {
      --width: 75vw;
    }
  `];
}
