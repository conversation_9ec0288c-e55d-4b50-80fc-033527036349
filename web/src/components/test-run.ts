import { FlakinessReport } from '@flakiness/report';
import { ContextType } from '@lit/context';

import { Stats } from '@flakiness/server/common/stats/stats.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { Task } from '@lit/task';
import { css, html, LitElement, nothing, PropertyValues, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import mime from 'mime';
import { api } from '../api.js';
import { consume, contexts } from '../contexts.js';

import { Query } from '@flakiness/server/common/fql/query.js';
import { githubLinks } from '../githubLinks.js';
import { assert, deepQuerySelector, humanReadableMs } from '../utils.js';
import { linkStyles } from './cssstyles.js';
import { ImageDiff } from './image-diff.js';

export type Artifact = {
  fileName: string,
  url: string,
  contentType: string,
  attachmentName: string,
}

type TOCEntry = {
  id: string,
  name: string,
  icon: TemplateResult<1>,
};

@customElement('test-run')
class TestRun extends LitElement {

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) report?: FlakinessReport.Report;
  @property({ attribute: false }) reportId?: Stats.ReportId;
  @property({ attribute: false }) testId?: Stats.TestId;
  @property({ attribute: false }) timeline?: string;

  @property({ attribute: false }) isRegression?: boolean;

  @property({ attribute: false }) attempt?: number;

  private _testRunTask = new Task(this, {
    args: () => [
      this._project, this.report, this.testId, this.timeline, this.reportId,
    ] as const,
    task: async ([project, report, testId, timeline, reportId], { signal }) => {
      assert(project && report && testId && reportId !== undefined);
      const tfql = Query.parse(timeline ?? '');

      let testRun: FlakinessReport.TestRun|undefined;
      FlakinessReport.visitTests(report, (aTest, parentSuites) => {
        const wireTest = Stats.flakinessTestToWireTypesTest(aTest, report.commitId, parentSuites);
        if (wireTest.testId !== testId)
          return;
        for (const aTestRun of aTest.runs) {
          const env = Stats.jsonEnvToWireEnv(Stats.flakinessEnvToJSONEnv(report, report.environments[aTestRun.environmentIdx]));
          if (tfql.acceptsEnvironment(env))
            testRun = aTestRun;
        }
      });
      assert(testRun);

      // Finally, resolve artifact links.
      const attemptToArtifacts = new Multimap<FlakinessReport.RunAttempt, Artifact>();
      const attachmentIds = testRun.attempts.map(attempt => attempt.attachments ?? []).flat().map(attachment => attachment.id);
      if (attachmentIds.length) {
        // Attachment IDs are unique to report.
        const attachmentsResult = await api.run.rawURL.query({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          reportId,
          attachmentIds: [...new Set(attachmentIds)],
        });
  
        
        const attachmentIdToURL = new Map(attachmentsResult.attachments.map(({ url, id }) => [id, url]));
        for (const attempt of testRun.attempts) {
          for (const attachment of attempt.attachments ?? []) {
            const attachmentURL = attachmentIdToURL.get(attachment.id);
            if (!attachmentURL)
              continue;
            const fileName = attachment.name.includes('.') ? attachment.name : attachment.name + '.' + mime.getExtension(attachment.contentType);
            attemptToArtifacts.set(attempt, {
              attachmentName: attachment.name,
              contentType: attachment.contentType,
              url: attachmentURL,
              fileName,
            });
          }
        }
      }
      return { testRun, report, attemptToArtifacts };
    },
  });

  private _renderError(error: FlakinessReport.ReportError) {
    let value: string = error.stack ? error.stack : error.message ?? error.value ?? '';

    if (error.snippet && error.snippet.trim().length) {
      // Position snippet right before stacktrace, if any
      const prefixLines: string[] = value.trim().split('\n'); 
      const suffixLines: string[] = [];
      while (prefixLines.length && /^\s+at\s+/.test(prefixLines.at(-1)!))
        suffixLines.unshift(prefixLines.pop()!);
      value = [
        ...prefixLines,
        '',
        error.snippet,
        '',
        ...suffixLines
      ].join('\n');
    }

    return html`
      <ansi-text class=error .text=${value}></ansi-text>
    `
  }

  private _renderAnnotations(annotations: FlakinessReport.Annotation[], tableOfContents: TOCEntry[]) {
    if (!annotations.length)
      return nothing;
    tableOfContents.push({ id: 'section-annotations', name: 'Annotations', icon: html`<sl-icon name=info-circle></sl-icon>` });

    const typeToAnnotations = new Multimap<string, FlakinessReport.Annotation>();
    for (const annotation of annotations)
      typeToAnnotations.set(annotation.type, annotation);
    return html`
      <v-box id=section-annotations>
        ${[...typeToAnnotations].map(([type, annotations]) => html`
          <fk-callout variant=${
            type === 'fixme' || type === 'fail' ? 'danger' :
            type === 'skip' || type === 'slow' ? 'warn' :
            'tip'
          }>
            ${[...annotations].map(annotation => html`
              <h-box>
                <strong>${annotation.type}${annotation.description ? ':' : nothing}</strong>
                ${annotation.description && URL.canParse(annotation.description) ? html`
                  <a-ext href=${annotation.description}>${annotation.description}</a-ext>
                ` : annotation.description ? annotation.description : nothing}
              </h-box>
            `)}
          </fk-callout>
        `)}
      </v-box>
    `;
  }

  private _renderErrors(errors: FlakinessReport.ReportError[], tableOfContents: TOCEntry[]) {
    if (!errors.length)
      return nothing;
    tableOfContents.push({ id: 'section-errors', name: 'Errors', icon: html`<sl-icon name=bug></sl-icon>` });
    return html`
      <fk-section id="section-errors" open>
        <div slot="title">Errors</div>
        <v-box>
        ${errors.map(error => this._renderError(error))}
        </v-box>
      </fk-section>
    `;
  }

  private _renderSteps(attempt: FlakinessReport.RunAttempt, tableOfContents: TOCEntry[]) {
    tableOfContents.push({ id: 'section-test-steps', name: 'Test Steps', icon: html`<sl-icon name=bar-chart-steps></sl-icon>` });
    const body = attempt.steps && attempt.steps.length ? html` 
      <v-box>
        ${attempt.steps.map(step => html`<test-step .step=${step} .commitId=${this.report?.commitId}></test-step>`)}
      </v-box>
    ` : html`
      No Steps
    `;
    return html`
      <fk-section open id=section-test-steps>
        <h-box class=steps-title slot="title">
          <span>Test Steps</span>
          <x-filler></x-filler>
          <span>${humanReadableMs(attempt.duration)}</span>
        </h-box>
        ${body}
      </fk-section>
    `;
  }

  private _renderImageDiffs(imageDiffs: ImageDiff[], tableOfContents: TOCEntry[]) {
    tableOfContents.push(...imageDiffs.map((imageDiff, index) => ({
      id: `image-diff-${index}`,
      name: imageDiff.name,
      icon: html`<artifact-icon .type=${'image'}></artifact-icon>`
    })));
    return html`
      <div id=section-images></div>
      ${imageDiffs.map((imageDiff, index) => html`
        <fk-section open id=${`image-diff-${index}`}>
          <div slot="title">Image mismatch: ${imageDiff.name}</div>
          <image-diff .diff=${imageDiff}></image-diff>
        </fk-section>
      `)}
    `;
  }

  private _renderTraces(traces: Artifact[], tableOfContents: TOCEntry[]) {
    if (!traces.length)
      return nothing;
    tableOfContents.push({ id: 'section-traces', name: 'Traces', icon: html`<artifact-icon .type=${'pw-trace'}></artifact-icon>` });
    return html`
      <fk-section id="section-traces" open>
        <div slot="title">Traces</div>
        <h-box class=collection-trace-attachments>
          ${traces.map((trace) => html`
            <v-box class=trace-attachment>
              <a class=trace-link href=${`https://trace.playwright.dev/?trace=${encodeURIComponent(trace.url)}`}>
                <img class="dropshadow" src='/playwright-trace.png'>
              </a>
              <artifact-link .artifact=${trace}></artifact-link>
            </v-box>
          `)}
        </h-box>
      </fk-section>
    `;
  }

  private _renderVideoAttachments(videos: Artifact[], tableOfContents: TOCEntry[]) {
    if (!videos.length)
      return nothing;
    tableOfContents.push({ id: 'section-videos', name: 'Videos', icon: html`<artifact-icon .type=${'video'}></artifcat-icon>` });
    return html`
      <fk-section id=section-videos open>
        <div slot="title">Videos</div>
        <v-box class=collection-video-attachments>
          ${videos.map((video) => html`
            <v-box class=video-attachment>
              <video class=dropshadow controls>
                <source src=${video.url} type=${video.contentType}>
              </video>
              <artifact-link .artifact=${video}></artifact-link>
            </v-box>  
          `)}
        </v-box>
      </fk-section>
    `;
  }

  private _renderImageAttachments(images: Artifact[], tableOfContents: TOCEntry[]) {
    if (!images.length)
      return nothing;
    tableOfContents.push({
      id: 'section-images',
      name: 'Images',
      icon: html`<artifact-icon .type=${'image'}></artifact-icon>`
    });
    return html`
      <fk-section id=section-images open>
        <div slot="title">Images</div>
        <div class=collection-image-attachments>
          ${images.map((image) => html`
            <v-box class=image-attachment>
              <img class=dropshadow src=${image.url}>
              <artifact-link .artifact=${image}></artifact-link>
            </v-box>
          `)}
        </div>
      </fk-section>
    `;
  }

  private _renderSTDIO(attempt: FlakinessReport.RunAttempt, tableOfContents: TOCEntry[]) {
    const decodeSTDIO = (lines?: FlakinessReport.STDIOEntry[]) => lines ? lines.map(line => {
      return ('buffer' in line) ? atob(line.buffer) : line.text;
    }).join('') : '';;

    const stdout = decodeSTDIO(attempt.stdout);
    const stderr = decodeSTDIO(attempt.stderr);
    if (!stdout && !stderr)
      return nothing;
    tableOfContents.push({ id: 'section-terminal', name: 'Terminal Output', icon: html`<sl-icon name=terminal></sl-icon>` })
    return html`
      <fk-section id='section-terminal' open>
        <div slot="title">Terminal Output</div>
        <fk-tabbar selected=${(stdout ? 'stdout' : 'stderr')}>
          <fk-tab name=stdout>stdout</fk-tab>
          <fk-tab name=stderr>stderr</fk-tab>
          <fk-tabpane name=stdout>
            ${stdout ? html`<ansi-text .text=${stdout}></ansi-text>` : html`<span class=no-terminal-output>no output</span>`}
          </fk-tabpane>
          <fk-tabpane name=stderr>
            ${stderr ? html`<ansi-text .text=${stderr}></ansi-text>` : html`<span class=no-terminal-output>no output</span>`}
          </fk-tabpane>
        </fk-tabbar>
      </fk-section>
    `;
  }

  private _renderMisc(misc: Artifact[], tableOfContents: TOCEntry[]) {
    if (!misc.length)
      return nothing;
    tableOfContents.push({ id: 'section-attachments', name: 'Attachments', icon: html`<sl-icon name=paperclip></sl-icon>` });
    return html`
      <fk-section id=section-attachments open>
        <div slot="title">Attachments</div>
        <v-box>
          ${misc.map((someArtifact) => html`
            <artifact-link .artifact=${someArtifact}></artifact-link>
          `)}
        </v-box>
      </fk-section>
    `;
  }

  private _renderTOC(report: FlakinessReport.Report, testRun: FlakinessReport.TestRun, tableOfContents: TOCEntry[]) {
    return html`
      <aside>
        <v-box style="gap: var(--sl-spacing-x-small);">
          <div class=subtitle>On this page</div>
          <sl-divider style="--spacing: var(--sl-spacing-2x-small)"></sl-divider>
          ${tableOfContents.map(entry => html`
            <a class=toc-entry href=${'#' + entry.id}>${entry.icon} ${entry.name}</a>
          `)}
        </v-box>
        <v-box style="gap: var(--sl-spacing-2x-small);">
          <div class=subtitle>System Info</div>
          <sl-divider style="--spacing: var(--sl-spacing-2x-small)"></sl-divider>
          ${this._renderSystemData(report, report.environments[testRun.environmentIdx])}
        </v-box>
        <v-box style="gap: var(--sl-spacing-2x-small);">
          <div class=subtitle>User Metadata</div>
          <sl-divider style="--spacing: var(--sl-spacing-2x-small)"></sl-divider>
          ${this._renderUserMetadata(report.environments[testRun.environmentIdx].userSuppliedData)}
        </v-box>
      </aside>
    `;
  }

  private _renderSystemData(report: FlakinessReport.Report, env: FlakinessReport.Environment) {
    const os = [
      env.systemData.osName,
      env.systemData.osVersion,
      env.systemData.osArch,
    ].filter(x => !!x).join(' ');

    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <h-box class=system-field>
          <sl-icon class=system-icon name=tag></sl-icon>
          <span class=system-property>${env.name}</span>
        </h-box>
        ${report.configPath ? html`
          <h-box class=system-field>
            <sl-icon class=system-icon name=folder></sl-icon>
            <a-ext class=system-property href=${this._project ? githubLinks.fileUrl(this._project, report.commitId, report.configPath) : nothing}>${report.configPath}</a-ext>
          </h-box>
        ` : nothing}
        <h-box class=system-field>
          <sl-icon class=system-icon name=cpu></sl-icon>
          <span class=system-property>${os}</span>
        </h-box>
      </v-box>
    `
  }

  private _renderUserMetadata(data: any) {
    const entries = Object.entries(data);
    return html`
      <section class=user-metadata>
        ${entries.map(([key, value]) => html`
          <span class=key>${key}:</span>
          <span class=value>${typeof value === 'object' ? html`<json-viewer .json=${value}></json-viewer>` : value}</span>
        `)}
      </section>
    `;
  }

  private _renderAttempt(report: FlakinessReport.Report, testRun: FlakinessReport.TestRun, attempt: FlakinessReport.RunAttempt | undefined, artifacts: Artifact[]) {
    if (!attempt)
      return nothing;
    const { imageDiffs, videos, traces, screenshots, misc } = classifyArtifacts(artifacts);
    const tableOfContents: TOCEntry[] = [];
    return html`
      <h-box style="align-items: start; gap: var(--sl-spacing-2x-large);">
        <v-box class=attempt>
          ${this._renderAnnotations(testRun.annotations ?? [], tableOfContents)}
          ${this._renderErrors(attempt.errors ?? [], tableOfContents)}
          ${this._renderSTDIO(attempt, tableOfContents)}
          ${this._renderSteps(attempt, tableOfContents)}
          ${this._renderImageDiffs(imageDiffs, tableOfContents)}
          ${this._renderImageAttachments(screenshots, tableOfContents)}
          ${this._renderTraces(traces, tableOfContents)}
          ${this._renderVideoAttachments(videos, tableOfContents)}
          ${this._renderMisc(misc, tableOfContents)}
        </v-box>
        ${this._renderTOC(report, testRun, tableOfContents)}
      </h-box>
    `;
  }

  override render() {
    if (!this._testRunTask.value)
      return nothing;
    const { testRun, report, attemptToArtifacts } = this._testRunTask.value;

    const selectedAttempt = Math.max(Math.min(this.attempt ?? 0, testRun.attempts.length - 1), 0);

    return html`
        <fk-tabbar selected=${selectedAttempt}>
          ${testRun.attempts.map((attempt, attemptIdx) => html`
            <fk-tab name=${attemptIdx} @click=${() => {
              this.attempt = attemptIdx;
            }}>
              <h-box class=attempt-tab-title>
                <fk-outcome outcome=${
                  attempt.status === 'skipped' ? 'skipped' :
                  attempt.status === testRun.expectedStatus ? 'expected' : 
                  this.isRegression ? 'regressed' : 'unexpected'
                }></fk-outcome>
                ${attemptIdx === 0 ? `First Run` : `Retry ${attemptIdx}`}
              </h-box>
            </fk-tab>
            <fk-tabpane name=${attemptIdx}>
              ${this._renderAttempt(report, testRun, attempt, attemptToArtifacts.getAll(attempt))}
            </fk-tabpane>
          `)}
        </fk-tabbar>
    `;
  }

  protected updated(_changedProperties: PropertyValues): void {
    if (window.location.hash) {
      const element = deepQuerySelector(window.location.hash,  this.renderRoot);
      element?.scrollIntoView();
    }
  }

  static styles = [linkStyles, css`
    aside {
      position: sticky;
      margin-top: var(--sl-spacing-medium);
      top: var(--sl-spacing-medium);
      width: 250px;
      display: flex;
      flex-direction: column;
      flex: none;
      gap: var(--sl-spacing-large);

      .subtitle {
        font-weight: var(--sl-font-weight-bold);
        font-size: var(--sl-font-size-small);
      }

      .toc-entry {
        display: flex;
        align-items: center;
        gap: var(--sl-spacing-x-small);
      }
    }

    .attempt {
      flex: auto;
      /* overflow: auto; */
    }

    .attempt-tab-title {
      white-space: nowrap;
    }
    .collection-image-attachments {
      margin: var(--sl-spacing-large);
      display: flex;
      flex-wrap: wrap;
      gap: var(--sl-spacing-large);

      .image-attachment {
        img {
          width: 200px;
        }
      }
    }

    .collection-video-attachments {
      margin: var(--sl-spacing-large);
      .video-attachment {
        max-width: 500px;
      }
    }

    .dropshadow {
      box-shadow: 0 0 5px var(--sl-color-neutral-400);
    }

    .collection-trace-attachments {
      margin: var(--sl-spacing-large);

      .trace-attachment {
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        gap: var(--sl-spacing-small);
  
        img {
          width: 200px;        
        }
      }
    }

    .steps-title {
      flex: auto;
    }

    .no-terminal-output {
      color: var(--sl-color-neutral-400);
    }

    .user-metadata {
      display: grid;
      grid-template-columns: auto 1fr;
      column-gap: var(--sl-spacing-2x-small);
      row-gap: var(--sl-spacing-x-small);

      .key {
        white-space: nowrap;
        max-width: 125px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .value {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .system-field {
      gap: var(--sl-spacing-x-small);
      white-space: nowrap;

      .system-icon {
        flex: none;
      }
    }
  `];
}

function classifyArtifacts(artifacts: Artifact[]) {
  // Since we will mutate attachments later on, clone it real quick to protect callsite.
  artifacts = artifacts.slice();
  // First of all, let's sort all attachments by name.
  artifacts.sort((a1, a2) => a1.fileName < a2.fileName ? -1 : 1);

  const videos = extractArray(artifacts, artifact => artifact.contentType.startsWith('video/'));
  const traces = extractArray(artifacts, artifact => artifact.attachmentName === 'trace');
  const screenshots = extractArray(artifacts, artifact => artifact.contentType.startsWith('image/'));
  const misc = artifacts;

  // Walk over all screenshots and assign them to image diffs.
  const diffScreenshots = new Map<string, Artifact>();
  const expectationScreenshots = new Multimap<string, {
    category: string,
    screenshot: Artifact,
  }>();

  for (const screenshot of screenshots) {
    const match = screenshot.fileName.match(/^(.*)-(expected|actual|diff|previous)(\.[^.]+)?$/);
    if (!match)
      continue;
    const [, name, category, ext = ''] = match;
    const diffName = name + ext;
    if (category === 'diff')
      diffScreenshots.set(diffName, screenshot);
    else
      expectationScreenshots.set(diffName, { category, screenshot });
  }
  // Create all image diffs. Each should have a "diff" image and exactly 2 expectation screenshots.
  const screenshotsInImageDiffs = new Set<Artifact>();
  const imageDiffs: ImageDiff[] = [...expectationScreenshots].map(([name, expectations]) => {
    let [e1, e2] = [...expectations];
    if (!e1 || !e2)
      return [];
    if (e1.category > e2.category)
      [e1, e2] = [e2, e1];
    const diff = diffScreenshots.get(name);
    if (diff)
      screenshotsInImageDiffs.add(diff);
    screenshotsInImageDiffs.add(e1.screenshot);
    screenshotsInImageDiffs.add(e2.screenshot);
    return {
      name,
      diff,
      expectation1: {
        ...e1.screenshot,
        category: e1.category,
      },
      expectation2: {
        ...e2.screenshot,
        category: e2.category,
      },
    } satisfies ImageDiff;
  }).flat();
  extractArray(screenshots, screenshot => screenshotsInImageDiffs.has(screenshot));

  // Finally, before returning all these attachments, let's sort each group.
  // Most of the groups should be sorted already since we sorted the attachments initially; however,
  // ImageDiffs are not.
  imageDiffs.sort((diff1, diff2) => diff1.name < diff2.name ? -1 : 1);
  return { videos, traces, screenshots, imageDiffs, misc }
}

// Like filtering, but also extracting elements from the array.
function extractArray<T>(array: T[], predicate: (t: T) => boolean) {
  const result: T[] = [];
  for (let i = array.length - 1; i >= 0; i--) {
    if (predicate(array[i])) {
      result.push(array[i]);
      array.splice(i, 1);
    }
  }
  return result;
} 
