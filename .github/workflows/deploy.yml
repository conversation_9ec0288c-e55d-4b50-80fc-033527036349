name: Deploy flakiness.io

on:
  workflow_dispatch:
    inputs:
      appVersion:
        description: "Version of the container to deploy. Can be either a SHA of the commit, an app version from package.json, or latest."
        required: true
        default: "latest"

env:
  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_PROD_CI_USE_WITH_EXTREME_CAUTION }}

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1

      - name: Check 1Password CLI User
        run: op user get --me

      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4

      - name: Install umputun Spot
        run: curl -sSfL https://raw.githubusercontent.com/umputun/spot/master/install.sh | sudo sh

      - name: Deploy new website
        env:
          APP_VERSION: ${{ github.event.inputs.appVersion }}
        run: |
          ./prod/deploy.sh --dbg -e APP_VERSION:"${APP_VERSION}"
