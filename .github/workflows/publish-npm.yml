name: Publish to NPM

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'  # This will match tags like v1.0.0, v2.1.0, etc.

env:
  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_PROD_CI_USE_WITH_EXTREME_CAUTION }}

jobs:
  publish-to-npm:
    runs-on: ubuntu-latest

    steps:
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1

      - name: Check 1Password CLI User
        run: op user get --me

      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4

      - name: Login to NPM
        run: |
          TOKEN=$(op read "op://ci/npm/token")
          echo "//registry.npmjs.org/:_authToken=${TOKEN}" > ~/.npmrc
          npm whoami

      - name: Build & Publish shared & report packages
        run: |
          npm ci
          npx kubik ./shared/build.mts ./report/build.mts
          cd shared && npm publish && cd ..
          cd report && npm publish && cd ..

