---
title: Running flakiness.io
sidebar:
  order: 3
---

import { Aside } from '@astrojs/starlight/components';

After configuring the environment variables, you can launch Flakiness.io using Docker. Mount the `flakiness.env` file into the container and start the application:

```bash
docker run \
  --rm \
  -p 3000:3000 \
  -v ./flakiness.env:/etc/flakiness/env \
  -it cr.flakiness.io/app:latest \
  node --env-file=/etc/flakiness/env ./server/lib/units/app_process.js
```

This command:
- Exposes port 3000 for web access
- Mounts the environment file
- Runs the latest version of the Flakiness.io container
- Starts the application process with the specified environment

Once the container is running, you can access the Flakiness.io web interface by navigating to `http://localhost:3000` in your web browser. This confirms that the service is operational and ready for use.

