---
title: License Key
sidebar:
  order: 1.5
---

The license key grants access to the flakiness.io container registry and authorizes running the application.

To obtain a license key, contact <NAME_EMAIL>.

## Downloading the Container

First, authenticate with the container registry using your license key:

```bash
echo $FLAKINESS_LICENSE | docker login -u flakiness --password-stdin cr.flakiness.io
```

Then pull the latest version:

```bash
docker pull cr.flakiness.io/app:latest
```

:::note
Currently only x86-64 architecture is supported. Reach out to us if you need arm64 support.
:::

To verify the container, run:

```bash
docker run --rm -it cr.flakiness.io/app ./server/lib/units/app_process.js
```

You should see configuration validation errors, which is expected since we haven't configured the environment yet:

```bash
Database configuration
[FAIL] PGHOST This env variable must be defined
[FAIL] PGPORT This env variable must be defined
[FAIL] PGUSER This env variable must be defined
[FAIL] PGPASSWORD This env variable must be defined
[FAIL] PGDATABASE This env variable must be defined
[FAIL] DB_ENCRYPTION_KEY This env variable must be defined
ERROR: configuration had errors!
```
