---
title: Timelines
sidebar:
  order: 3
---

Timelines are a powerful feature in flakiness.io that allow you to create customized test history views based on specific criteria. They help you analyze test results across different environments, configurations, and test runs.

## How Timelines Work

Each test report sent to flakiness.io includes:
- A repository commit hash
- The Playwright project name
- Operating system information
- Additional metadata (customizable via environment variables)

Using this information, flakiness.io can create filtered views of your test history. For example, you can:
- View test results for a specific browser
- Track test performance on different operating systems
- Monitor test behavior across different configurations

## Default Timeline Behavior

By default, flakiness.io automatically creates separate timelines for each Playwright project in your repository. This helps you track test performance across different test suites independently.

## Custom Timelines

You can create additional timelines using FQL (Flakiness Query Language) to filter test reports based on any metadata. Here are some examples:

```fql
@browserName:chrome        # Timeline for Chrome-only tests
@os.name:ubuntu           # Timeline for Ubuntu tests
@environment:staging      # Timeline for staging environment tests
```

This flexibility allows you to:
- Compare test behavior across different environments
- Isolate environment-specific issues
- Track test performance in specific configurations

## Best Practices

1. Use meaningful metadata in your test reports to enable useful timeline filtering
2. Create separate timelines for critical test configurations
3. Use timelines to isolate and debug environment-specific test failures
