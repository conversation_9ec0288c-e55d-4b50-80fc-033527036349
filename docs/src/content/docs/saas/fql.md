---
title: Flakiness Query Language (FQL)
description: A comprehensive guide to filtering and searching test data
sidebar:
  order: 5
---

**F**lakiness **Q**uery **L**anguage (FQL) is a simple yet powerful language for filtering data in flakiness.io. It allows you to search and filter through your test results, environments, and error messages using an intuitive syntax.

## Basic Syntax

FQL supports three types of filters:

| Filter Type    | Prefix | Description                    |
|---------------|---------|--------------------------------|
| Test filters  | (none)  | Filter test names and metadata |
| Environment filters | `@` | Filter by environment properties |
| Error filters | `$`    | Filter by error messages        |

## Examples by Filter Type

### Test Filters

Regular words (without prefix) filter test names, files, and titles:

```fql
page-click           # Finds tests containing "page-click"
element drag         # Finds tests containing both "element" and "drag"
```

### Environment Filters

Use `@` prefix to filter by environment properties:

```fql
@linux              # Finds tests run on Linux
@browserName:chrome # Finds tests run in Chrome
@os.name:ubuntu     # Finds tests run on Ubuntu
```

### Error Filters

Use `$` prefix to filter by error messages:

```fql
$undefined            # Finds errors containing "undefined"
$"permission denied"  # Finds errors containing exact phrase
```

## Advanced Usage

### Combining Filters

You can combine multiple filters using spaces (logical AND):

```fql
installation $denied @debian  # Tests:     containing "installation"
                              # Errors:     containing "denied"
                              # Environment: running on Debian
```

### Handling Spaces and Special Characters

Wrap terms containing spaces or special characters in quotes:

```fql
@"windows 10"       # Finds tests run on Windows 10
$"null pointer"     # Finds errors containing "null pointer"
"login test"        # Finds tests containing "login test"
```

### Binary Operators

FQL supports several binary operators for precise filtering:

```fql
@os.name=ubuntu     # Exact match for Ubuntu
@browser≠firefox    # Exclude Firefox browser
status:(passed, failed)  # Match multiple values
```

## Best Practices

1. Start with broad filters and gradually refine them
2. Use quotes for exact phrase matching
3. Combine filters to narrow down results
4. Use environment filters to isolate platform-specific issues

## Related Features

- Learn how to use FQL with [Timelines](../timelines) for advanced analysis
- Explore environment-specific failures in the [Flakiness Detection](../flakiness) guide
