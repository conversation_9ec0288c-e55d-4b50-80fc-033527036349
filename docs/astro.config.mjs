// @ts-check
import starlight from '@astrojs/starlight';
import { defineConfig } from 'astro/config';

// https://astro.build/config
export default defineConfig({
	base: '/docs',
	integrations: [
		starlight({
			title: 'flakiness.io docs',
			social: {
				email: 'mailto:<EMAIL>',
			},
			sidebar: [
				{
					label: 'fakiness.io',
					autogenerate: { directory: 'saas' },
				},
				{
					label: 'On-Premise',
					autogenerate: { directory: 'on-premise' },
				},
			],
		}),
	],
});
