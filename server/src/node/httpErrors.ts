
export class HTTPError extends Error {
  statusCode: number;
  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
  }
}

export const errors = {
  BadRequestError: class BadRequestError extends HTTPError {
    constructor(message: string) {
      super(message, 400);
    }  
  },
  UnauthorizedError: class UnauthorizedError extends HTTPError {
    constructor(message: string) {
      super(message, 401);
    }
  },
  NotFoundError: class NotFoundError extends HTTPError {
    constructor(message: string) {
      super(message, 404);
    }
  },
};
