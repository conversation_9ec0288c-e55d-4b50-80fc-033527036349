import { Queue, <PERSON><PERSON><PERSON><PERSON><PERSON>, QueueWorkerCallbackOptions } from "@flakiness/database";

import { CachedDatabase } from "./cachedDatabase.js";

import { sha256Object } from "@flakiness/shared/common/utils.js";
import assert from "assert";
import debug from 'debug';
import ms from "ms";
import { LRUCache } from "../common/lruCache.js";
import { Stats as S } from '../common/stats/stats.js';
import { StatsBuilder } from "../common/stats/statsBuilder.js";
import { createSharedArrayBuffer } from "./nodeutils.js";
import { ReportIndex } from "./reportIndex.js";
import { S3Report, S3Stats } from "./s3layout.js";
import { S3Objects } from "./s3object.js";
import { XNotify, XNotifyChannel } from "./xnotify.js";

const log = debug('fk:stats_worker');

export class StatsWorker {
  private _queue: Queue<S3Stats.Id>;
  private _worker?: QueueWorker<S3Stats.Id>;
  private _channel: XNotifyChannel<S3Stats.Id>;

  private _statsStorageRawCached: LRUCache<S3Stats.Id, {
    buffer: Buffer,
    etag: string,
  }>;

  constructor(
    private _s3objects: S3Objects,
    private _db: CachedDatabase,
    xnotify: XNotify,
  ) {
    this._queue = this._db.queues.createQueue<S3Stats.Id>('stats-builder');

    this._statsStorageRawCached = new LRUCache({
      max: 100,
      ttl: ms('10min'),
      key: s3id => S3Stats.path(s3id),
      allowStale: true,
      compute: async s3id => {
        const file = await this._s3objects.stats.getBuffer(s3id);
        if (!file || file.metadata.version !== S.STATS_VERSION)
          return undefined;
        return {
          buffer: createSharedArrayBuffer(file.buffer),
          etag: file.metadata.etag,
        };
      },
    });

    this._channel = xnotify.createChannel<S3Stats.Id>('clean-stats-builder-cache', async id => {
      this._statsStorageRawCached.invalidate(id);
    });
  }

  async scheduleBuild(statsIds: S3Stats.Id[]) {
    await this._queue.sendMany(statsIds.map(statsId => ({
      data: statsId,
      options: {
        jobId: S3Stats.path(statsId),
        category: statsId.projectPublicId,
      }
    })));
  }

  async start(workerName: string) {
    assert(!this._worker);
    this._worker = this._queue.createWorker(workerName, async (jobInfo, options) => {
      const statsId = jobInfo.data;
      log('Aggregation started', statsId);
      const result = await this._aggregateStats(statsId, options);
      if (result.aggregated)
        this._channel.notify(statsId);
      log('Aggregation complete', statsId, result);
      // If we didn't finish aggregation in time, then schedule another job.
      if (result.preemptiveTermination)
        this.scheduleBuild([statsId]);
    }, {
      staleTimeoutMs: ms('15 minutes'),
    });
  }

  async stop() {
    assert(this._worker);
    await this._worker.stop();
    this._worker = undefined;
  }

  async rawShard(shard: S3Stats.Id) {
    return await this._statsStorageRawCached.get(shard);
  }

  private async _aggregateStats(statsId: S3Stats.Id, { signal }: QueueWorkerCallbackOptions): Promise<{ aggregated: number, preemptiveTermination?: boolean }> {
    const statsCloudMetadata = await this._s3objects.stats.getMetadata(statsId, signal);
    // If there's a saved stats version, and it is "newer" than what we can produce here,
    // than do nothing.
    if (statsCloudMetadata && statsCloudMetadata.version > S.STATS_VERSION)
      return { aggregated: 0 };

    // If report index version if something we don't understand - than bail out right away.
    const indexMetadata = await this._s3objects.reportIndex.getMetadata(statsId, signal);
    if (indexMetadata && indexMetadata.version !== ReportIndex.REPORT_INDEX_VERSION)
      return { aggregated: 0 };

    // Otherwise, compute expected etag for the current reports in store.
    const index = new ReportIndex.ReportIndex(statsId.projectPublicId, await this._s3objects.reportIndex.get({ projectPublicId: statsId.projectPublicId }, signal));
    const reportIds: S3Report.Id[] = index.shardReports(statsId);

    // If expected etag of the aggregation matches currently saved data, then there's nothing else to do.
    if (statsCloudMetadata?.version === S.STATS_VERSION &&
        statsCloudMetadata?.etag === computeETag(reportIds.map(r => r.reportId))) {
      return { aggregated: 0 };
    }

    // If there are no reports, then remove shard.
    if (!reportIds.length) {
      await this._s3objects.stats.remove(statsId, signal);
      return { aggregated: 0 };
    }

    const jsonStats = statsCloudMetadata?.version === S.STATS_VERSION ? await this._s3objects.stats.get(statsId, signal) : undefined;
    const builder = StatsBuilder.create(index.testIndex(), jsonStats?.stats);
    const missingReportIds = reportIds.filter(reportId => !builder.hasReport(reportId.reportId));

    const toBeRemoved = new Set(builder.reportIds()).difference(new Set(reportIds.map(r => r.reportId)));
    for (const reportId of toBeRemoved)
      builder.removeReport(reportId);

    let processedReports = 0;
    let preemptiveTermination: boolean = false;
    const deadline = Date.now() + ms('10 minutes');
    // Limit shard computation to 10 minutes - yield to another
    // job afterwards.
    for (const reportId of missingReportIds) {
      if (Date.now() > deadline) {
        preemptiveTermination = true;
        break;
      }
      ++processedReports;
      const flakinessReport = await this._s3objects.reports.get(reportId, signal);
      if (flakinessReport)
        builder.addReport(reportId.reportId, flakinessReport.report);
    }

    const statsJSON = builder.jsonStats();
    await this._s3objects.stats.set({
      statsId,
      etag: computeETag(builder.reportIds()),
      jsonStats: statsJSON,
    }, signal);
    log(`added ${processedReports}/${missingReportIds.length} reports to ${S3Stats.path(statsId)}`);
    return { aggregated: processedReports, preemptiveTermination };
  }
}

function computeETag(reportIds: Iterable<S.ReportId>): string {
  const sortedReportIds = [...reportIds].sort((a, b) => a < b ? -1 : 1);
  return sha256Object({
    reportIds: sortedReportIds,
  });
}
