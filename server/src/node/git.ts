import { FlakinessReport } from "@flakiness/report";
import { sha256, sha256Object } from "@flakiness/shared/common/utils.js";
import debug from "debug";
import { WireTypes } from "../common/wireTypes.js";

const log = debug('fk:git');

export namespace Git {
  export interface Provider {
    origin(): RepoOrigin;
  
    listCommits(options: {
      sha: string
    }): AsyncGenerator<Commit[], void, unknown>;
  
    allCommits(options: {
      sha?: string, // optional to load default branch commits
    }): Promise<Commit[]>;
  
    allBranches(): Promise<{ name: string, sha: string }[]>;
    defaultBranch(): Promise<string>;
    allOpenPullRequests(): Promise<PullRequest[]>;
  
    allPRCommits(options: {
      pullRequestId: string,
    }): Promise<Commit[]>;
  }

  export type Commit = {
    commitId: FlakinessReport.CommitId,
    timestamp: FlakinessReport.UnixTimestampMS, // ms
    message: string,
    avatar_url?: string,
    author?: string,  
  }

  export type Ref = {
    name: string,
    commit: Commit,  
  }

  export type PullRequest = {
    id: string,
    title: string,  
  }

  export type RepoOrigin = {
    provider: 'github', // github, gitlab, etc
    owner: string, // in the github.com/degulabs/flakiness, "owner" is "degulabs"
    repo: string, // in the github.com/degulabs/flakiness, "repo" is "flakiness"
  };

  export type JSONRepo = {
    version: number,
    origin: RepoOrigin,
    heads: {
      type: 'branch',
      name: string,
      commitIndex: number,
    }[];
    defaultBranch: string,
    commits: Commit[];
    strands: number[][],
  }

  export class Repository {
    static DATA_VERSION = 1;

    static deserialize(provider: Provider, json: JSONRepo | undefined) {
      if (json?.version === this.DATA_VERSION
          && json?.origin.owner === provider.origin().owner
          && json?.origin.repo === provider.origin().repo
          && json?.origin.provider === provider.origin().provider) {
        return new Repository(provider, json);
      }
      return undefined;
    }

    static createEmpty(provider: Provider) {
      return new Repository(provider, {
        version: this.DATA_VERSION,
        origin: provider.origin(),
        defaultBranch: '',
        heads: [],
        commits: [],
        strands: [],
      });
    }

    toJSON(): JSONRepo {
      const result: JSONRepo = {
        origin: this._provider.origin(),
        version: Repository.DATA_VERSION,
        defaultBranch: this._defaultBranch,
        heads: [],
        commits: [],
        strands: [],
      };
      const commitsToIndex = new Map<string, number>();
      for (const [branchName, sha] of this._branchNameToSha) {
        let wp: string | undefined = sha;
        const strand: number[] = [];
        while (wp && !commitsToIndex.has(wp)) {
          result.commits.push(this._shaToCommit.get(wp)!);
          const commitIndex = result.commits.length - 1;
          commitsToIndex.set(wp, commitIndex);
          strand.push(commitIndex);
          wp = this._parentSha.get(wp);
        }
        if (wp && strand.length)
          strand.push(commitsToIndex.get(wp)!);
        if (strand.length)
          result.strands.push(strand);
        result.heads.push({
          type: 'branch',
          name: branchName,
          commitIndex: commitsToIndex.get(sha)!,
        });
      }
      return result;
    }

    private _parentSha = new Map<string, string>();
    private _branchNameToSha = new Map<string, string>();
    private _shaToBranchName = new Map<string, string>();
    private _shaToCommit = new Map<string, Commit>();
    private _defaultBranch: string;

    private _provider: Provider;

    constructor(provider: Provider, json: JSONRepo) {
      this._provider = provider;
      this._defaultBranch = json.defaultBranch;
      for (const commit of json.commits)
        this._shaToCommit.set(commit.commitId, commit);
      for (const head of json.heads) {
        if (head.type === 'branch') {
          this._branchNameToSha.set(head.name, json.commits[head.commitIndex].commitId);
          this._shaToBranchName.set(json.commits[head.commitIndex].commitId, head.name);
        }
      }
      for (const commitIndexes of json.strands) {
        for (let i = 0; i < commitIndexes.length - 1; ++i) {
          const fromCommitId = json.commits[commitIndexes[i]]!.commitId;
          const toCommitId = json.commits[commitIndexes[i + 1]]!.commitId;
          this._parentSha.set(fromCommitId, toCommitId);
        }
      }
    }

    defaultBranch(): Ref {
      return {
        name: this._defaultBranch,
        commit: this.commit(this._branchNameToSha.get(this._defaultBranch)!)!,
      }
    }

    branches(): Ref[] {
      return [...this._branchNameToSha].map(([branchName, sha]) => ({
        commit: this.commit(sha)!,
        name: branchName,
      }));
    }

    etag() {
      const branches = [...this._branchNameToSha].sort((b1, b2) => b1[0] < b2[0] ? -1 : 1);
      const hashTokens = [
        sha256Object(this._provider.origin()),
      ];
      for (const [branchName, headCommit] of branches) {
        hashTokens.push(branchName);
        hashTokens.push(headCommit);
        let tailCommit = headCommit;
        while (this._parentSha.has(tailCommit))
          tailCommit = this._parentSha.get(tailCommit)!;
        hashTokens.push(tailCommit);
      }
      return sha256(hashTokens);
    }

    commit(headOrCommitId: string): Commit | undefined {
      const commitId = this._branchNameToSha.get(headOrCommitId) ?? headOrCommitId;
      return this._shaToCommit.get(commitId);
    }

    listCommits(options: WireTypes.ListCommitOptions): Commit[] {
      let sha: string | undefined = this._branchNameToSha.get(options.head) ?? options.head;
      // If we are asked for a head that we don't have, then return nothing.
      if (!this._shaToCommit.has(sha)) {
        log('Failed to find head when listing commits!', JSON.stringify(options));
        return [];
      }
      const commits: Commit[] = [];
      const { maxCount = Infinity, headOffset = 0 } = options;

      const sinceTimestamp = options.sinceTimestamp ?? -Infinity;
      const untilTimestamp = options.untilTimestamp ?? Infinity;

      for (let i = 0; i < headOffset && sha; ++i)
        sha = this._parentSha.get(sha);

      while (sha && commits.length < maxCount) {
        const commit = this._shaToCommit.get(sha)!;
        if (commit.timestamp < sinceTimestamp)
          break;
        if (sinceTimestamp <= commit.timestamp && commit.timestamp < untilTimestamp)
          commits.push(this._shaToCommit.get(sha)!);
        sha = this._parentSha.get(sha);
      }
      return commits;
    }

    private _addCommits(commits: Commit[], lastCommit?: Commit) {
      for (const commit of commits)
        this._shaToCommit.set(commit.commitId, commit);
      for (let i = 0; i < commits.length - 1; ++i) {
        this._parentSha.set(commits[i].commitId, commits[i + 1].commitId);
      }
      if (lastCommit && commits.length) {
        this._parentSha.set(lastCommit.commitId, commits[0].commitId);
      }
      return commits[commits.length - 1];
    }

    private async _fetchBranch(provider: Provider, branch: { name: string, sha: string }) {
      if (this._shaToCommit.has(branch.sha))
        return;
      log(`[Github API] Fetching branch: ${branch.name} ${branch.sha}`);
      let lastCommit = undefined;
      for await (const commits of provider.listCommits({ sha: branch.sha })) {
        const reachedExisting = commits.some(commit => this._shaToCommit.has(commit.commitId));
        lastCommit = this._addCommits(commits, lastCommit);
        if (reachedExisting)
          break;
      }
    }

    async fetch(): Promise<number> {
      try {
        this._defaultBranch = await this._provider.defaultBranch();
        const branches = await this._provider.allBranches();
        this._branchNameToSha = new Map(branches.map(branch => ([branch.name, branch.sha])));
        this._shaToBranchName = new Map(branches.map(branch => ([branch.sha, branch.name])));

        if (this._shaToCommit.size === 0) {
          log(`[Github API] bulk-loading initial commits!`);
          const initialCommits = await this._provider.allCommits({});
          this._addCommits(initialCommits);
        }
        await Promise.all(branches.map(branch => this._fetchBranch(this._provider, branch)));
        return 200;
      } catch (error: any) {
        log(error);
        if (error.response)
          return error.response.status;
        throw error;
      }
    }
  }

}
