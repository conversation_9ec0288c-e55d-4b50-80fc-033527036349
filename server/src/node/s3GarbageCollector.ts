import { ProjectPublicId, Queue, <PERSON><PERSON><PERSON>or<PERSON>, QueueWorkerCallbackOptions } from "@flakiness/database";
import assert from "assert";
import ms from "ms";
import { Ranges } from "../common/ranges.js";
import { Stats } from "../common/stats/stats.js";
import { CachedDatabase } from "./cachedDatabase.js";
import { S3Bucket } from "./s3.js";
import { parseProjectPublicId, S3Repo, S3Report, S3ReportIndex, S3Stats } from "./s3layout.js";
import { S3Objects } from "./s3object.js";

type GCJob = {
  type: 'collect-dead-projects',
} | {
  type: 'remove-reports',
  projectPublicId: ProjectPublicId,
  reports: Ranges.Ranges<Stats.ReportId>,
};

export class S3GarbageCollector {
  private _queue: Queue<GCJob>;
  private _queueWorker?: QueueWorker<GCJob>;

  constructor(
    private _s3data: S3Bucket,
    private _s3objects: S3Objects,
    private _db: CachedDatabase,
  ) {
    this._queue = this._db.queues.createQueue('data-remover');
  }

  async collectDeadProjects() {
    await this._queue.send({ type: 'collect-dead-projects' }, {
      jobId: 'full'
    });
  }

  async removeReports(projectPublicId: ProjectPublicId, reports: Ranges.Ranges<Stats.ReportId>) {
    if (!reports.length)
      return;
    await this._queue.send({
      type: 'remove-reports',
      projectPublicId,
      reports,
    });
  }

  async start(workerName: string) {
    assert(!this._queueWorker);
    this._queueWorker = this._queue.createWorker(workerName, async (job, options) => {
      if (job.data.type === 'remove-reports')
        await this._removeReports(job.data.projectPublicId, job.data.reports, options);
      else if (job.data.type === 'collect-dead-projects')
        await this._collectDeadProjects(options);
    }, {
      retries: 3,
      bookkeepingIntervalMs: ms('30 minutes'),
      pollIntervalMs: ms('30 seconds'), // no need to pull too often to remove stale data.
      staleTimeoutMs: ms('30 minutes'),
      heartbeatIntervalMs: ms('30 seconds'),
    });
  }

  async stop() {
    assert(this._queueWorker);
    await this._queueWorker.stop();
    this._queueWorker = undefined;
  }

  private async _removeReports(projectPublicId: ProjectPublicId, reportIds: Ranges.Ranges<Stats.ReportId>, { signal }: QueueWorkerCallbackOptions) {
    const deadline = Date.now() +  ms('5 minutes'); // Limit work to 5 minutes per turn.
    while (reportIds.length && Date.now() < deadline) {
      const reportId = Ranges.popInplace(reportIds)!;
      await this._s3objects.reports.remove({ projectPublicId, reportId }, signal);
    }
    if (reportIds.length)
      await this.removeReports(projectPublicId, reportIds);
  }

  private async _collectDeadProjects({ signal }: QueueWorkerCallbackOptions) {
    const existingProjects = new Set((await this._db.uncachedDatabase().projects.all()).map(project => project.project_public_id));
    const toBeRemoved = new Set<string>();

    const checkS3ProjectPaths = (paths: string[]) => {
      for (const s3ProjectDirectoryPath of paths) {
        const projectPublicId = parseProjectPublicId(s3ProjectDirectoryPath);
        if (projectPublicId && !existingProjects.has(projectPublicId))
          toBeRemoved.add(s3ProjectDirectoryPath);
      }
    }
    // Check all projects with reports
    for await (const paths of this._s3data.listDirectoryEntries(S3Report.projectsPrefix(), signal))
      checkS3ProjectPaths(paths);
    // Check all stats
    for await (const paths of this._s3data.listDirectoryEntries(S3Stats.projectsPrefix(), signal))
      checkS3ProjectPaths(paths);
    // Check all repos
    for await (const paths of this._s3data.listDirectoryEntries(S3Repo.projectsPrefix(), signal))
      checkS3ProjectPaths(paths);
    for await (const paths of this._s3data.listDirectoryEntries(S3ReportIndex.projectsPrefix(), signal))
      checkS3ProjectPaths(paths);

    for (const prefix of toBeRemoved) {
      for await (const keys of this._s3data.listObjects(prefix, signal)) {
        await this._s3data.deleteFiles(keys, signal);
      }
    }
  }
}