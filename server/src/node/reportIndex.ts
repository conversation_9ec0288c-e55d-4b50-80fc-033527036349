import { ProjectPublicId } from "@flakiness/database";
import { FlakinessReport } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import { randomUUID } from "@flakiness/shared/common/utils.js";
import assert from "assert";
import ms from "ms";
import { Ranges } from "../common/ranges.js";
import { Stats } from "../common/stats/stats.js";
import { TestIndex } from "../common/stats/testIndex.js";
import { WireTypes } from "../common/wireTypes.js";
import { S3Report, S3Stats } from "./s3layout.js";

export namespace ReportIndex {
  // TODO: all clients have to compare JSON version with REPORT_INDEX_VERSION
  // before creating this object. This is error-prone; we should unify this.
  export const REPORT_INDEX_VERSION = 4;

  export type JSONReportIndex = {
    version: number,
    tests: Stats.JSONTests,
    shards: JSONShard[];
    dailyReports: JSONDayReports[],
    unparsedReports: JSONUnparsedReport[],
  }

  export type JSONUnparsedReport = {
    reportId: Stats.ReportId,
    error: string,
  }

  export type JSONDayReports = {
    dayTimestampMs: FlakinessReport.UnixTimestampMS,
    reports: Ranges.CompressedRanges<Stats.ReportId>,
    reportBytes: number,
    attachmentBytes: number,
    testRunsCount: number,
  }

  export type JSONShard = {
    shardId: S3Stats.ShardId,
    commits: JSONCommit[];
  }

  export type JSONCommit = {
    commitId: FlakinessReport.CommitId,
    relatedCommitIds?: FlakinessReport.CommitId[],
    reports: Ranges.CompressedRanges<Stats.ReportId>,
  }

  type ParsedDayReports = {
    dayTimestampMs: FlakinessReport.UnixTimestampMS,
    reports: Ranges.Ranges<Stats.ReportId>,
    reportBytes: number,
    attachmentBytes: number,
    testRunsCount: number,
  }

  type ParsedShard = {
    shardId: S3Stats.ShardId,
    commits: Map<FlakinessReport.CommitId, ParsedCommit>;
  }

  type ParsedCommit = {
    commitId: FlakinessReport.CommitId,
    reports: Ranges.Ranges<Stats.ReportId>,
  }

  export class ReportIndex {
    private _shards = new Map<S3Stats.ShardId, ParsedShard>();
    private _dailyReports = new Map<FlakinessReport.UnixTimestampMS, ParsedDayReports>();
    private _commitIdToShard = new Map<Stats.CommitId, ParsedShard>();
    private _unparsedReportErrors = new Map<Stats.ReportId, string>();
    private _allReportIds: Ranges.Ranges<Stats.ReportId>;
    private _testIndex: TestIndex;

    constructor(private _projectPublicId: ProjectPublicId, json: JSONReportIndex|undefined) {
      // We should not try to work with data that has a version from future.
      assert(!json || json.version <= REPORT_INDEX_VERSION);
      // If JSON's version is less than our version, than bail out.
      if (json && json.version < REPORT_INDEX_VERSION)
        json = undefined;
      for (const jsonShard of json?.shards ?? []) {
        const shard: ParsedShard = {
          shardId: jsonShard.shardId,
          commits: new Map(),
        }
        for (const jsonCommit of jsonShard.commits) {
          this._commitIdToShard.set(jsonCommit.commitId, shard);
          shard.commits.set(jsonCommit.commitId, {
            commitId: jsonCommit.commitId,
            reports: Ranges.decompress(jsonCommit.reports),
          });
        }
        this._shards.set(shard.shardId, shard);
      }
      for (const dayReports of json?.dailyReports ?? []) {
        this._dailyReports.set(dayReports.dayTimestampMs, {
          dayTimestampMs: dayReports.dayTimestampMs,
          reports: Ranges.decompress(dayReports.reports),
          reportBytes: dayReports.reportBytes,
          attachmentBytes: dayReports.attachmentBytes,
          testRunsCount: dayReports.testRunsCount,
        });
      }
      for (const unparsedReport of json?.unparsedReports ?? [])
        this._unparsedReportErrors.set(unparsedReport.reportId, unparsedReport.error);

      this._testIndex = new TestIndex(json?.tests ?? Stats.createEmptyTests());
      this._allReportIds = Ranges.unionAll([...this._dailyReports.values()].map(parsed => parsed.reports));
    }

    testIndex(): TestIndex {
      return this._testIndex;
    }

    allReportIds(): Ranges.Ranges<Stats.ReportId> {
      return this._allReportIds;
    }

    serialize(): JSONReportIndex {
      return {
        version: REPORT_INDEX_VERSION,
        unparsedReports: [...this._unparsedReportErrors].map(([reportId, error]) => ({ reportId, error })),
        dailyReports: [...this._dailyReports.values()].map(parsedDay => ({
          dayTimestampMs: parsedDay.dayTimestampMs,
          reports: Ranges.compress(parsedDay.reports),
          reportBytes: parsedDay.reportBytes,
          attachmentBytes: parsedDay.attachmentBytes,
          testRunsCount: parsedDay.testRunsCount,
        })),
        shards: [...this._shards.values()].map(parsedShard => ({
          shardId: parsedShard.shardId,
          commits: [...parsedShard.commits.values()].map(parsedCommit => ({
            commitId: parsedCommit.commitId,
            reports: Ranges.compress(parsedCommit.reports),
          })),
        })),
        tests: this._testIndex.serialize(),
      }
    }

    private _toStatId(shardId: S3Stats.ShardId): S3Stats.Id {
      return {
        projectPublicId: this._projectPublicId,
        shardId,
      };
    }

    hasReport(reportId: Stats.ReportId) {
      return Ranges.contains(this._allReportIds, reportId);
    }

    // Returns a shard that has this commitId, if any - otherwise, returns nothing.
    getShard(commitId: FlakinessReport.CommitId): S3Stats.Id|undefined {
      const shard = this._commitIdToShard.get(commitId);
      return shard ? this._toStatId(shard.shardId) : undefined;
    }

    // Returns a shard that has this commitId, if any - otherwise, returns nothing.
    splitIntoShards(commitIds: FlakinessReport.CommitId[]): Multimap<S3Stats.Id, FlakinessReport.CommitId> {
      const shardToCommits = new Multimap<S3Stats.ShardId, FlakinessReport.CommitId>();
      for (const commitId of commitIds) {
        const shard = this._commitIdToShard.get(commitId);
        if (shard !== undefined)
          shardToCommits.set(shard.shardId, commitId);
      }
      const result = new Multimap<S3Stats.Id, FlakinessReport.CommitId>();
      for (const [shardIndex, commitIds] of shardToCommits) {
        const statId = this._toStatId(shardIndex);
        result.setAll(statId, commitIds);
      }
      return result;
    }

    getLastShard(): S3Stats.Id|undefined {
      if (!this._shards.size)
        return undefined;
      const lastShardId = [...this._shards.keys()].pop()!;
      return this._toStatId(lastShardId);
    }

    shardReports(shardId: S3Stats.Id): S3Report.Id[] {
      const shard = this._shards.get(shardId.shardId);
      if (!shard)
        return [];
      const reports = Ranges.unionAll([...shard.commits.values()].map(commit => commit.reports));
      return Ranges.toSortedList(reports).map(reportId => ({
        projectPublicId: this._projectPublicId,
        reportId
      }));
    }

    commitReportsCount(commitId: FlakinessReport.CommitId): number {
      const shard = this._commitIdToShard.get(commitId);
      if (!shard)
        return 0;
      const commit = shard.commits.get(commitId);
      return commit ? Ranges.cardinality(commit.reports) : 0;
    }

    metrics(): WireTypes.DailyMetrics[] {
      return Array.from(this._dailyReports.values()).map(parsedDay => ({
        dayTimestampMs: parsedDay.dayTimestampMs,
        reportsCount: Ranges.cardinality(parsedDay.reports),
        testsCount: parsedDay.testRunsCount,
        totalBytes: parsedDay.attachmentBytes + parsedDay.reportBytes,
      })).sort((a, b) => a.dayTimestampMs - b.dayTimestampMs);
    }

    createShard(): S3Stats.Id {
      const newShard: ParsedShard = {
        // prefix with timestamp to order shards by creation time. Might help with debugging later.
        shardId: Date.now() + '-' + randomUUID() as S3Stats.ShardId,
        commits: new Map(),
      };
      this._shards.set(newShard.shardId, newShard);
      return this._toStatId(newShard.shardId);
    }

    hasShard(shardId: S3Stats.Id): boolean {
      return this._shards.has(shardId.shardId);
    }

    enforceDataRetention(dataRetentionDays: number): { reportsToDelete: Ranges.Ranges<Stats.ReportId>, shardsToRebuild: S3Stats.Id[] } {
      if (Object.is(dataRetentionDays, Infinity)) {
        return {
          reportsToDelete: Ranges.EMPTY as Ranges.Ranges<Stats.ReportId>,
          shardsToRebuild: [],
        };
      }
      const timestamp = utcDayStart(Date.now()) - ms('1 day') * dataRetentionDays;
      const daysToRemove = [...this._dailyReports.values()].filter(report => report.dayTimestampMs < timestamp);
      const shardsToRecompute = new Set<S3Stats.ShardId>();
      const reportsToBeRemoved = Ranges.unionAll(daysToRemove.map(day => day.reports));

      for (const day of daysToRemove)
        this._dailyReports.delete(day.dayTimestampMs);

      for (const [shardIndex, shard] of this._shards) {
        let isShardChanged = false;
        for (const [commitId, commit] of shard.commits) {
          if (!Ranges.hasNonEmptyIntersection(commit.reports, reportsToBeRemoved))
            continue;
          isShardChanged = true;
          commit.reports = Ranges.subtract(commit.reports, reportsToBeRemoved);
          if (!commit.reports.length) {
            shard.commits.delete(commitId);
            this._commitIdToShard.delete(commitId);
          }
        }
        if (!shard.commits)
          this._shards.delete(shardIndex);
        if (isShardChanged)
          shardsToRecompute.add(shardIndex);
      }

      for (const reportId of this._unparsedReportErrors.keys()) {
        if (Ranges.contains(reportsToBeRemoved, reportId))
          this._unparsedReportErrors.delete(reportId);
      }

      this._allReportIds = Ranges.subtract(this._allReportIds, reportsToBeRemoved);
      return {
        reportsToDelete: reportsToBeRemoved,
        shardsToRebuild: [...shardsToRecompute].map(shardIdx => this._toStatId(shardIdx)),
      };
    }

    addFaultyReport(options: {
      reportId: Stats.ReportId,
      error: string,
      uploadTimestampMs: FlakinessReport.UnixTimestampMS,
      reportBytes: number,
      attachmentBytes: number,
    }) {
      this._unparsedReportErrors.set(options.reportId, options.error);
      this._addDayStats({
        attachmentBytes: options.attachmentBytes,
        reportBytes: options.reportBytes,
        reportId: options.reportId,
        testRunsCount: 0,
        uploadTimestampMs: options.uploadTimestampMs,
      });
    }

    addReport(options: {
      shard: S3Stats.Id,
      reportId: Stats.ReportId,
      report: FlakinessReport.Report,
      uploadTimestampMs: FlakinessReport.UnixTimestampMS,
      reportBytes: number,
      attachmentBytes: number,
    }) {
      const shard = this._shards.get(options.shard.shardId);
      assert(shard);
      let commit = shard.commits.get(options.report.commitId);
      if (!commit) {
        commit = {
          commitId: options.report.commitId,
          reports: Ranges.EMPTY as Ranges.Ranges<Stats.ReportId>,
        };
        shard.commits.set(options.report.commitId, commit);
        this._commitIdToShard.set(options.report.commitId, shard);
      }
      commit.reports = Ranges.union(commit.reports, Ranges.fromList([options.reportId]))
      // Add test to tests.
      let testRunsCount = 0;
      FlakinessReport.visitTests(options.report, (test, parentSuites) => {
        ++testRunsCount;
      });
      this._testIndex.addReport(options.report);

      this._addDayStats({
        reportId: options.reportId,
        attachmentBytes: options.attachmentBytes,
        reportBytes: options.reportBytes,
        testRunsCount,
        uploadTimestampMs: options.uploadTimestampMs,
      });
    }

    private _addDayStats(options: {
      reportId: Stats.ReportId,
      uploadTimestampMs: FlakinessReport.UnixTimestampMS,
      reportBytes: number,
      attachmentBytes: number,
      testRunsCount: number,
    }) {
      this._allReportIds = Ranges.union(this._allReportIds, Ranges.from(options.reportId));
      const dayMs = utcDayStart(options.uploadTimestampMs);
      let parsedDay = this._dailyReports.get(dayMs);
      if (!parsedDay) {
        parsedDay = {
          dayTimestampMs: dayMs,
          reports: Ranges.EMPTY as Ranges.Ranges<Stats.ReportId>,
          attachmentBytes: 0,
          reportBytes: 0,
          testRunsCount: 0,
        };
        this._dailyReports.set(dayMs, parsedDay);
      }
      parsedDay.reports = Ranges.union(parsedDay.reports, Ranges.fromList([options.reportId]))
      parsedDay.reportBytes += options.reportBytes;
      parsedDay.attachmentBytes += options.attachmentBytes;
      parsedDay.testRunsCount += options.testRunsCount;
    }
  }
}

export function utcDayStart(timestamp: number): FlakinessReport.UnixTimestampMS {
  const date = new Date(timestamp);
  return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()) as FlakinessReport.UnixTimestampMS;
}