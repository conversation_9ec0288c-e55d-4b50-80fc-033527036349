import { ProjectPublicId } from "@flakiness/database";
import { FlakinessReport } from "@flakiness/report";
import { Stats as S } from '../common/stats/stats.js';
import { Git } from "./git.js";
import { ReportIndex } from "./reportIndex.js";
import { S3Bucket } from "./s3.js";
import { S3Repo, S3Report, S3ReportIndex, S3Stats } from "./s3layout.js";
import { BrotliTextStorage, JSONStorage, S3Storage } from "./storage.js";

export class S3Objects {
  public readonly reports: Reports;
  public readonly stats: Stats;
  public readonly repositories: Repositories;
  public readonly reportIndex: SReportIndex;

  constructor(private _s3data: S3Bucket) {
    this.reports = new Reports(this._s3data);
    this.stats = new Stats(this._s3data);
    this.repositories = new Repositories(this._s3data);
    this.reportIndex = new SReportIndex(this._s3data);
  }
}

class Reports {
  private _reportsStorage: JSONStorage<S3Report.Id, FlakinessReport.Report>;
  
  constructor(private _s3data: S3Bucket) {
    this._reportsStorage = new JSONStorage<S3Report.Id, FlakinessReport.Report>({
      keyId: (node) => S3Report.path(node),
      serialize: value => JSON.stringify(value),
      deserialize: raw => JSON.parse(raw),
      next: new BrotliTextStorage({
        next: new S3Storage(this._s3data),
      }),
    });
  }

  async get(reportId: S3Report.Id, signal?: AbortSignal): Promise<{ report: FlakinessReport.Report, lastModified?: Date }|undefined> {
    const flakinessReport = await this._reportsStorage.getSafe(reportId, signal);
    return flakinessReport ? { report: flakinessReport.data, lastModified: flakinessReport.lastModified } : undefined;
  }

  async readonlyURLs(reportId: S3Report.Id, attachmentIds: string[], deadline: FlakinessReport.DurationMS) {
    return {
      reportURL: await this._s3data.getReadonlyURL(S3Report.path(reportId), deadline),
      attachments: await Promise.all(attachmentIds.map(async id => ({
        id, 
        url: await this._s3data.getReadonlyURL(S3Report.attachmentPath(reportId, id), deadline),
      })))
    }
  }

  async remove(reportId: S3Report.Id, signal?: AbortSignal) {
    return await this._s3data.deleteEverythingWithPrefix(S3Report.reportPrefix(reportId), signal);
  }
  
  async createUploadURLs(options: {
    reportId: S3Report.Id,
    attachmentIds: string[],
    expiration: FlakinessReport.DurationMS,
  }) {
    const attachmentUploadURLs = Object.fromEntries(await Promise.all(options.attachmentIds.map(async attachmentId => {
      const attachmentPath = S3Report.attachmentPath(options.reportId, attachmentId);
      const uploadURL = await this._s3data.getUploadURL(attachmentPath, options.expiration);
      return [attachmentId, uploadURL];
    })));
    return {
      reportUploadURL: await this._s3data.getUploadURL(S3Report.path(options.reportId), options.expiration),
      attachmentUploadURLs,
    };
  }

  async computeByteSize(reportId: S3Report.Id, signal?: AbortSignal) {
    return {
      attachmentsBytes: await this._computeTotalBytes(S3Report.attachmentsPrefix(reportId), signal),
      reportBytes: await this._computeTotalBytes(S3Report.path(reportId), signal),
    };
  }

  async *list(projectPublicId: ProjectPublicId, signal?: AbortSignal) {
    for await (const entries of this._s3data.listDirectoryEntries(S3Report.projectPrefix(projectPublicId), signal)) {
      const reportIds: S3Report.Id[] = entries.map(entry => ({
        // Each entry is an absolute path like this:
        //  reports/project-e6bbd672-d72f-4d6c-a575-985e0d08b32c/00001092/
        reportId: parseInt(entry.split('/').at(-2)!, 10) as S.ReportId,
        projectPublicId,
      }));
      yield reportIds;
    }
  }

  private async _computeTotalBytes(prefix: string, signal?: AbortSignal): Promise<number> {
    let totalBytes = 0;
    for await (const objects of this._s3data.listObjectSizes(prefix, signal)) {
      for (const [object, bytes] of objects)
        totalBytes += bytes;
    }
    return totalBytes;
  }
}

class Repositories {
  private _storage: JSONStorage<S3Repo.Id, Git.JSONRepo>;
  
  constructor(private _s3data: S3Bucket) {
    this._storage = new JSONStorage<S3Repo.Id, Git.JSONRepo>({
      keyId: (node) => S3Repo.path(node),
      serialize: value => JSON.stringify(value),
      deserialize: raw => JSON.parse(raw),
      next: new BrotliTextStorage({
        next: new S3Storage(this._s3data),
      }),
    });
  }

  async get(repoId: S3Repo.Id, signal?: AbortSignal): Promise<Git.JSONRepo|undefined> {
    const file = await this._storage.getSafe(repoId, signal);
    return file?.data;
  }

  async set(repoId: S3Repo.Id, jsonRepo: Git.JSONRepo, signal?: AbortSignal): Promise<void> {
    await this._storage.put(repoId, {}, jsonRepo, signal);
  }
}

//NOTE: metadata keys must be lower-case.
type RAWReportIndexMetadata = {
  version: string,
};

type ReportIndexMetadata = {
  version: number,
}

class SReportIndex {
  private _storage: JSONStorage<S3ReportIndex.Id, ReportIndex.JSONReportIndex, RAWReportIndexMetadata>;
  
  constructor(private _s3data: S3Bucket) {
    this._storage = new JSONStorage<S3ReportIndex.Id, ReportIndex.JSONReportIndex, RAWReportIndexMetadata>({
      keyId: (node) => S3ReportIndex.path(node),
      serialize: value => JSON.stringify(value),
      deserialize: raw => JSON.parse(raw),
      next: new BrotliTextStorage({
        next: new S3Storage(this._s3data),
      }),
    });
  }

  private _parseMetadata(metadata: RAWReportIndexMetadata): ReportIndexMetadata {
    return {
      version: parseInt(metadata.version ?? '0', 10),
    } satisfies ReportIndexMetadata;
  }

  async get(id: S3ReportIndex.Id, signal?: AbortSignal): Promise<ReportIndex.JSONReportIndex|undefined> {
    const file = await this._storage.getSafe(id, signal);
    return file?.data;
  }

  async getMetadata(id: S3ReportIndex.Id, signal?: AbortSignal): Promise<ReportIndexMetadata|undefined> {
    const response = await this._storage.getMetadataSafe(id, signal);
    return response ? this._parseMetadata(response) : undefined;
  }

  async set(id: S3ReportIndex.Id, json: ReportIndex.JSONReportIndex, signal?: AbortSignal): Promise<void> {
    await this._storage.put(id, { version: json.version + '' }, json, signal);
  }
}

//NOTE: metadata keys must be lower-case.
type RAWStatsMetadata = {
  version: string,
  etag: string,
};

type StatsMetadata = {
  version: number,
  etag: string,
}

class Stats {
  private _s3storage: S3Storage<RAWStatsMetadata>;

  private _statsStorage: JSONStorage<S3Stats.Id, S.JSONData, RAWStatsMetadata>;

  constructor(private _s3data: S3Bucket) {
    this._s3storage = new S3Storage(this._s3data);

    this._statsStorage = new JSONStorage({
      keyId: (node) => S3Stats.path(node),
      serialize: value => JSON.stringify(value),
      deserialize: raw => JSON.parse(raw),
      next: new BrotliTextStorage({
        next: this._s3storage,
      }),
    });
  }

  private _parseMetadata(metadata: RAWStatsMetadata): StatsMetadata {
    return {
      etag: metadata.etag,
      version: parseInt(metadata.version ?? '0', 10),
    } satisfies StatsMetadata;
  }

  async get(statsId: S3Stats.Id, signal?: AbortSignal): Promise<{ stats: S.JSONData, metadata: StatsMetadata }|undefined> {
    const response = await this._statsStorage.getSafe(statsId, signal);
    return response ? { stats: response.data, metadata: this._parseMetadata(response.metadata) } : undefined;
  }

  async getBuffer(statsId: S3Stats.Id, signal?: AbortSignal): Promise<{ buffer: Buffer, metadata: StatsMetadata }|undefined> {
    const response = await this._s3storage.getSafe(S3Stats.path(statsId), signal);
    return response ? { buffer: response.data, metadata: this._parseMetadata(response.metadata) } : undefined;
  }

  async getMetadata(statsId: S3Stats.Id, signal?: AbortSignal): Promise<StatsMetadata|undefined> {
    const response = await this._statsStorage.getMetadataSafe(statsId, signal);
    return response ? this._parseMetadata(response) : undefined;
  }

  async getSizeInBytes(statsId: S3Stats.Id, signal?: AbortSignal): Promise<number> {
    return await this._s3data.GetObjectSize(S3Stats.path(statsId), signal);
  }

  async remove(statsId: S3Stats.Id, signal?: AbortSignal) {
    return await this._s3data.deleteFiles([S3Stats.path(statsId)], signal);
  }

  async *list(projectPublicId: ProjectPublicId, signal?: AbortSignal) {
    for await (const entries of this._s3data.listDirectoryEntries(S3Stats.projectPrefix(projectPublicId), signal)) {
      const statIds: S3Stats.Id[] = entries.map(entry => ({
        // Each entry is an absolute path like this:
        //  stats/project-e6bbd672-d72f-4d6c-a575-985e0d08b32c/xxx-eee-aa.json.br
        shardId: entry.split('/').pop()!.split('.').at(0) as S3Stats.ShardId,
        projectPublicId,
      }));
      yield statIds;
    }
  }

  async set(options: {
    statsId: S3Stats.Id, etag: string, jsonStats: S.JSONData
  }, signal?: AbortSignal) {
    await this._statsStorage.put(options.statsId, {
      version: options.jsonStats.version + '',
      etag: options.etag,
    }, options.jsonStats, signal);
  }
}
