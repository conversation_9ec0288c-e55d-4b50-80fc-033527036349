import { LRUCache } from "../common/lruCache.js";
import { Storage } from "./storage.js";

export interface Cache<INPUT, OUTPUT extends {}> {
  get(input: INPUT): Promise<OUTPUT|undefined>;
}

export class ComputationCache<INPUT, OUTPUT extends {}> implements Cache<INPUT, OUTPUT> {
  private _cache: LRUCache<INPUT, OUTPUT>;

  constructor(private _options: {
    max: number,
    etag: (input: INPUT) => string,
    compute: (etag: string, input: INPUT) => Promise<OUTPUT>,
    fallbackStorage?: Storage<string, OUTPUT>,
  }) {
    this._cache = new LRUCache({
      max: this._options.max,
      key: this._options.etag,
      allowStale: false,
      ttl: 1,
      currentTime: () => 0,
      compute: async (input: INPUT, etag: string, stale: OUTPUT | undefined, signal) => {
        let value = (await this._options.fallbackStorage?.getSafe(etag))?.data;
        if (value)
          return value;
        value = await this._options.compute(etag, input);
        await this._options.fallbackStorage?.put(etag, {}, value);
        return value;
      }
    });
  }

  async get(input: INPUT): Promise<OUTPUT> {
    return (await this._cache.get(input))!;
  }
}
