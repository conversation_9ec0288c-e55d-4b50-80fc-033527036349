import { Database, Project, ProjectPublicId, Queue, QueueWorker } from "@flakiness/database";
import { measure } from "@flakiness/shared/common/utils.js";
import { Git } from "./git.js";
import { FlakinessOctokit } from "./integrations/flakinessOctokit.js";

import assert from "assert";
import debug from 'debug';
import ms from "ms";
import { LRUCache } from "../common/lruCache.js";
import { GithubApp } from "./integrations/githubApp.js";
import { S3Repo } from "./s3layout.js";
import { S3Objects } from "./s3object.js";
import { XNotify, XNotifyChannel } from "./xnotify.js";

const log = debug('fk:repo_worker');

function createProvider(octokit: FlakinessOctokit, owner: string, repo: string): Git.Provider {
  return {
    origin: () => ({ owner, repo, provider: 'github', }),
    allBranches: () => octokit.allBranches({ owner, repo }),
    listCommits: commitOptions => octokit.listCommits({ ...commitOptions, owner, repo }),
    allCommits: commitOptions => octokit.allCommits({ ...commitOptions, owner, repo }),
    defaultBranch: () => octokit.defaultBranch({ owner, repo }),
    allOpenPullRequests: () => octokit.allOpenPullRequests({ owner, repo }),
    allPRCommits: prOptions => octokit.allPRCommits({ ...prOptions, owner, repo }),
  };
}

export class RepoWorker {

  private _queue: Queue<S3Repo.Id>;
  private _worker?: QueueWorker<S3Repo.Id>;
  private _channel: XNotifyChannel<S3Repo.Id>;
  private _cachedStorage: LRUCache<S3Repo.Id, Git.Repository>;

  constructor(
    private _s3objects: S3Objects,
    private _githubApp: GithubApp,
    private _db: Database,
    xnotify: XNotify,
  ) {
    this._queue = this._db.queues.createQueue<S3Repo.Id>('repo-builder');
    
    this._cachedStorage = new LRUCache({
      max: 500,
      ttl: ms('10min'),
      key: repoId => S3Repo.path(repoId),
      allowStale: true,
      compute: async repoId => {
        // This doesn't happen more than once-every-10-minute,
        // so it is safe to schedule a build.
        await this.scheduleBuild(repoId);
        const project = await this._db.projects.getByPublicId(repoId.projectPublicId);
        assert(project);
        const provider = await this._getProvider(project);
        const jsonRepo = await this._s3objects.repositories.get(repoId);
        return Git.Repository.deserialize(provider, jsonRepo);    
      },
    });
    this._channel = xnotify.createChannel('clean-repo-builder-cache', async id => {
      this._cachedStorage.invalidate(id);
    });
  }

  async scheduleBuild(repoId: S3Repo.Id) {
    await this._queue.send(repoId, {
      jobId: S3Repo.path(repoId),
      category: repoId.projectPublicId,
    });
  }

  private async _getProvider(project: Project): Promise<Git.Provider> {
    const owner = project.source_owner_name;
    const repo = project.source_repo_name;

    const sourceType = project.source_auth_type;
    if (sourceType === Database.sourceAuthType.githubPat) {
      const personalAccessToken = await this._db.projectSource.getPersonalAccessToken(project.project_id);
      if (!personalAccessToken)
        throw new Error(`Internal error: failed to fetch github_pat for ${project.project_id}`);
      const octokit = await this._githubApp.apiForPAT(personalAccessToken);
      return createProvider(octokit, owner, repo);
    } else if (sourceType === Database.sourceAuthType.githubApp) {
      const installationId = await this._db.projectSource.getInstallationId(project.project_id);
      if (!installationId)
        throw new Error(`Internal error: failed to fetch installation id for ${project.project_id}`);
      const octokit = await this._githubApp.apiForInstallationId(parseInt(installationId, 10));
      return createProvider(octokit, owner, repo);
    }
    throw new Error(`project ${project.project_id} has unsupported source type ${sourceType}`)
  }

  async start(workerName: string) {
    assert(!this._worker);
    this._worker = this._queue.createWorker(workerName, jobInfo => this._fetchRepository(jobInfo.data));
  }

  async stop() {
    assert(this._worker);
    await this._worker.stop();
    this._worker = undefined;
  }

  async getRepo(projectPublicId: ProjectPublicId): Promise<Git.Repository|undefined> {
    return await this._cachedStorage.get({ projectPublicId });
  }

  private async _fetchRepository(repoId: S3Repo.Id) {
    log(`started fetching repository ${repoId.projectPublicId}`);
    const m = measure(`git fetching ${repoId.projectPublicId}`);
    // Keep using uncached database to have latest information.
    const project = await this._db.projects.getByPublicId(repoId.projectPublicId);
    assert(project);
    const provider = await this._getProvider(project);
    const jsonRepo = await this._s3objects.repositories.get(repoId);
    const repo = Git.Repository.deserialize(provider, jsonRepo) ?? Git.Repository.createEmpty(provider);

    const etag = repo.etag();
    const fetchResult = await repo.fetch();
    m(`http response ${fetchResult}`);
    await this._db.projects.update(project.project_id, {
      source_last_fetch_http_status: fetchResult,
      source_last_fetch_timestamp_seconds: Math.floor(Date.now() / 1000),
    });
    if (fetchResult === 200 && repo.etag() !== etag) {
      await this._s3objects.repositories.set(repoId, repo.toJSON());
      await this._channel.notify(repoId);
      m(`commited new changes`);
    } else {
      m(`no changes`);
    }
  }
}
