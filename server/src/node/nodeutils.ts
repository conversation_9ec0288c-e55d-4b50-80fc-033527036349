import { Brand } from '@flakiness/shared/common/utils.js';
import debug from 'debug';
import fs from 'fs';
import path from 'path';
import util from 'util';
import * as vlq from 'vlq';
import { Worker } from 'worker_threads';
import zlib, { gunzip } from 'zlib';

const packageJSONText = fs.readFileSync(path.join(import.meta.dirname, '../../../package.json'), 'utf-8');
export const packageJSON = JSON.parse(packageJSONText);

export const asyncBrotliCompress = util.promisify(zlib.brotliCompress);
export const asyncBrotliDecompress = util.promisify(zlib.brotliDecompress);

export const gunzipAsync = (buffer: Buffer) => new Promise<Buffer>((resolve, reject) => {
  gunzip(buffer, (error, result) => {
    if (error) reject(error); else resolve(result);
  })
});

export function createSharedArrayBuffer(buffer: Buffer): Buffer {
  const sharedArrayBuffer = new SharedArrayBuffer(buffer.length);
  const sharedBuffer = Buffer.from(sharedArrayBuffer);
  sharedBuffer.set(buffer);
  return sharedBuffer;
}

export type CompressedArray = Brand<string, 'compressed array'>;

export function compressArray(numbers: number[]): CompressedArray {
  if (numbers.length === 0)
    return '' as CompressedArray;
  const offsets = [numbers[0]];
  for (let i = 1; i < numbers.length; ++i)
    offsets.push(numbers[i] - numbers[i - 1]);
  return vlq.encode(offsets) as CompressedArray;
}

export function decompressArray(encoded: CompressedArray): number[] {
  if (!encoded.length)
    return [] as number[];
  const offsets = vlq.decode(encoded);
  const numbers: number[] = [offsets[0]];
  for (let i = 1; i < offsets.length; ++i)
    numbers.push(numbers[i - 1] + offsets[i]);
  return numbers;
}

const log = debug('fk:worker_service');

export function startWorkerService(path: string, workerData?: any): { worker: Worker } {
  const service = {
    worker: new Worker(path, { workerData }),
  };

  // Handle worker errors
  service.worker.on('error', (error) => {
      log('Worker error:', error);
  });

  // Restart the worker if it stops unexpectedly
  service.worker.on('exit', (code) => {
    log('worker exit');
    if (code !== 0) {
      log(`Worker stopped with exit code ${code}. Restarting...`);
      service.worker = startWorkerService(path, workerData).worker; // Restart the worker
    } else {
      service.worker.terminate();
    }
  });
  return service;
}
