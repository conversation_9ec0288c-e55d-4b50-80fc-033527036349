import { Database, GithubOAuthToken, OrgAccessRole, Organization, Project, ProjectAccessRole, User } from '@flakiness/database';
import { initTRPC, TRPCError } from '@trpc/server';
import { WireTypes } from '../common/wireTypes.js';
import type { Authentication } from './authentication.js';
import { QueryService } from './queryService.js';
import { Services } from './services.js';

export type APIContext = {
  auth: Authentication,
  services: Services,
  queryService: QueryService,
  userInfo?: {
    user: User,
    githubToken: GithubOAuthToken,
  },
  project?: Project,
  projectRole?: ProjectAccessRole,
  org?: Organization,
  orgBillingStatus?: WireTypes.BillingStatus,
  orgRole?: OrgAccessRole,
  jwtSignSecret: string,
  preferences: {
    onlySuperusersCanCreateOrganizations: boolean,
  },
};

export class PaymentRequiredError extends Error { };

const t = initTRPC.context<APIContext>().create({
  errorFormatter({ error, shape }) {
    let httpStatus = shape.data.httpStatus;
    let code: string = shape.data.code;
    if (error.cause instanceof PaymentRequiredError) {
      httpStatus = 402; // HTTP status code for Payment Required
      code = 'PAYMENT_REQUIRED';
    }

    return {
      ...shape,
      data: {
        ...shape.data,
        code,
        httpStatus,
      },
    };
  },
});

// you can reuse this for any procedure
export const router = t.router;

// Public procedure will try to resolve context, where possible.
export const publicProcedure = t.procedure.use(t.middleware(async ({ ctx, next, rawInput }) => {
  const input = (rawInput ?? {}) as any;

  const orgSlug = input['orgSlug'] as string|undefined;
  const projectSlug = input['projectSlug'] as string|undefined;

  const org = orgSlug ? await ctx.services.db.orgs.getBySlug(orgSlug) : undefined;
  const project = org && projectSlug ? await ctx.services.db.projects.getBySlug(org.org_id, projectSlug) : undefined;
  const orgRole = org && ctx.userInfo ? await ctx.auth.getUserOrgRole(ctx.userInfo.user, org) : undefined;
  const projectRole = project ? await ctx.auth.getUserProjectRole(ctx.userInfo?.user, project) : undefined;
  const billing = org ? await ctx.services.stripeApp?.status(org) : undefined;

  return next({
    ctx: { project, projectRole, org, orgRole, orgBillingStatus: billing } satisfies Partial<APIContext>,
  });
}));

// CI uploads are special since they resolve using flakinessAccessToken, and they
// have a special permissions due to billint.
export const ciUpload = t.procedure.use(t.middleware(async ({ ctx, next, rawInput }) => {
  const input = (rawInput ?? {}) as any;

  const flakinessAccessToken = input['flakinessAccessToken'];
  const project = await ctx.services.db.projects.getByAccessToken(flakinessAccessToken);
  const org = project ? await ctx.services.db.orgs.get(project.org_id) : undefined;

  if (!project || !org)
    throw new TRPCError({ code: 'NOT_FOUND' });
  const status = await ctx.services.stripeApp?.status(org);
  if (status?.restrictedCIUploads)
    throw new PaymentRequiredError();

  return next({
    ctx: { project, org } satisfies Partial<APIContext>,
  });
}));


// Authorized will throw if user is not authorized
export const authorizedProcedure = publicProcedure.use(t.middleware(async ({ ctx, next }) => {
  if (!ctx.userInfo)
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  return next({
    ctx: { userInfo: ctx.userInfo, },
  });
}));

// Proj read will throw if arguments don't resolve to a project accessible by a user.
export const projReadIgnorePaymentStatus = publicProcedure.use(t.middleware(async ({ ctx, next }) => {
  const { projectRole, project, org, orgRole } = ctx;
  if (!project || !projectRole || !org)
    throw new TRPCError({ code: 'NOT_FOUND' });
  if (projectRole !== Database.projectAccessRole.editor && projectRole !== Database.projectAccessRole.viewer)
    throw new TRPCError({ code: 'NOT_FOUND' });
  return next({
    ctx: { projectRole, project, org, orgRole } satisfies Partial<APIContext>,
  });
}));

// Proj read will throw if arguments don't resolve to a project accessible by a user.
export const projRead = projReadIgnorePaymentStatus.use(t.middleware(async ({ ctx, next }) => {
  const { orgBillingStatus } = ctx;
  if (orgBillingStatus?.restrictedProjectAccess)
    throw new PaymentRequiredError();
  return next({
    ctx: { orgBillingStatus } satisfies Partial<APIContext>,
  });
}));

export const projReadWrite = authorizedProcedure.use(t.middleware(async ({ ctx, next }) => {
  const { userInfo, projectRole, project, org, orgRole, orgBillingStatus } = ctx;
  if (!project || !org || !projectRole || !userInfo)
    throw new TRPCError({ code: 'NOT_FOUND' });
  if (projectRole !== Database.projectAccessRole.editor)
    throw new TRPCError({ code: 'NOT_FOUND' });
  if (orgBillingStatus?.restrictedProjectAccess)
    throw new PaymentRequiredError();
  return next({
    ctx: { userInfo, projectRole, project, org, orgRole, orgBillingStatus } satisfies Partial<APIContext>,
  });
}));

export const orgMember = authorizedProcedure.use(t.middleware(async ({ ctx, next }) => {
  const { userInfo, org, orgRole } = ctx;
  if (!org || !ctx.userInfo || !orgRole)
    throw new TRPCError({ code: 'FORBIDDEN' });

  if (orgRole !== Database.orgAccessRole.admin && orgRole !== Database.orgAccessRole.member)
    throw new TRPCError({ code: 'FORBIDDEN' });

  return next({
    ctx: { userInfo, org, orgRole } satisfies Partial<APIContext>,
  });
}));

export const orgAdmin = authorizedProcedure.use(t.middleware(async (opts) => {
  const { ctx } = opts;
  const { userInfo, org, orgRole } = ctx;
  if (orgRole !== Database.orgAccessRole.admin || !org || !userInfo)
    throw new TRPCError({ code: 'FORBIDDEN' });

  return opts.next({
    ctx: { userInfo, org, orgRole } satisfies Partial<APIContext>,
  });
}));

export const orgOwner = authorizedProcedure.use(t.middleware(async (opts) => {
  const { ctx } = opts;
  const { userInfo, org, orgRole } = ctx;
  if (!org || !userInfo || org.owner_id !== userInfo.user.user_id)
    throw new TRPCError({ code: 'FORBIDDEN' });

  return opts.next({
    ctx: { userInfo, org, orgRole } satisfies Partial<APIContext>,
  });
}));

// Proj read will throw if logged in user is not a superuser.
export const superuserAccess = authorizedProcedure.use(t.middleware(async (opts) => {
  const { ctx } = opts;

  if (!ctx.auth.isSuperUser(ctx.userInfo?.user))
    throw new TRPCError({ code: 'UNAUTHORIZED' });

  return opts.next({
    ctx: {
      userInfo: ctx.userInfo,
    },
  });
}));
