import { Database, GithubOAuthToken, OrgAccessRole, Organization, Project, ProjectAccessRole, User, UserPublicId } from '@flakiness/database';
import { randomUUID } from 'crypto';
import debug from 'debug';
import type { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { type CachedDatabase } from './cachedDatabase.js';

const log = debug('fk:authentication');

export class Authentication {

  constructor(private _db: CachedDatabase, private _jwtSignSecret: string, private _superuserGithubIds: Set<string>) { }

  isSuperUser(user?: User): boolean {
    return user ? this._superuserGithubIds.has(String(user.github_id)) : false;
  }

  async loginOrSignUpWithGithub(response: Response, { githubId, name, login, avatarUrl, token }: { githubId: number, name: string, login: string, avatarUrl: string, token: Gith<PERSON>OAuthToken }): Promise<void> {
    let user = await this._db.users.getByGithubId(githubId);
    if (!user) {
      user = await this._db.users.create({
        user_public_id: randomUUID() as UserPublicId,
        user_login: login,
        user_name: name,
        github_id: githubId,
        user_avatar_url: avatarUrl,
      });
    }
    await this._db.githubOauthUserTokens.set(user.user_id, token);

    const sessionToken = jwt.sign( { userPublicId: user.user_public_id }, this._jwtSignSecret);
    response.cookie('session_token', sessionToken, {
      httpOnly: true, // Makes the cookie HTTP-only
      secure: true,   // Makes the cookie to be sent over HTTPS only
    });
  }

  async logout(response: Response) {
    response.clearCookie('session_token');
  }

  async getUserProjectRole(user: User|undefined, project: Project): Promise<ProjectAccessRole | undefined> {
    if (this.isSuperUser(user))
      return Database.projectAccessRole.editor;

    let impliedRole: ProjectAccessRole|undefined;
    let explicitRole: ProjectAccessRole|undefined;

    if (user) {
      const owned = await this._db.users.getOrganizations(user.user_id);
      const shared = await this._db.orgSharing.getOrganizations(user.user_id);
      if (owned.some(orgId => orgId === project.org_id)) {
        impliedRole = Database.projectAccessRole.editor;
      } else {
        const sharedOrg = shared.find(entry => entry.orgId === project.org_id);
        if (sharedOrg?.accessRole === Database.orgAccessRole.admin)
          impliedRole = Database.projectAccessRole.editor;
        else if (sharedOrg?.accessRole === Database.orgAccessRole.member)
          impliedRole = Database.projectAccessRole.viewer;
      }

      const projectSharing = (await this._db.projectSharing.getUsers(project.project_id)).find(entry => entry.userId === user.user_id);
      explicitRole = projectSharing?.accessRole;  
    }

    if (impliedRole === undefined && project.visibility === Database.projectVisibility.public)
      impliedRole = Database.projectAccessRole.viewer;

    if (explicitRole === Database.projectAccessRole.editor || impliedRole === Database.projectAccessRole.editor)
      return Database.projectAccessRole.editor;
    if (explicitRole === Database.projectAccessRole.viewer || impliedRole === Database.projectAccessRole.viewer)
      return Database.projectAccessRole.viewer;
    return undefined;
  }

  async getGithubOAuthUserToken(user: User, refreshCallback: (refreshToken: string) => Promise<GithubOAuthToken>): Promise<GithubOAuthToken|undefined> {
    const token = await this._db.githubOauthUserTokens.get(user.user_id);
    if (!token)
      return undefined;
    const now = Date.now();
    // If the token is up-to-date, use it.
    if (token.accessExpirationMs > now)
      return token;
    // If refresh token is expired, then bail out.
    if (token.refreshExpirationMs < now)
      return undefined;
    const newToken = await this._db.githubOauthUserTokens.refresh(user.user_id, refreshCallback).catch(e => {
      log(`Error while refreshing oauth user token`);
      log(e);
      return undefined;
    });
    if (!newToken)
      return undefined;
    return newToken;
  }

  async getUserOrgRole(user: User, organization: Organization): Promise<OrgAccessRole | undefined> {
    const owned = await this._db.users.getOrganizations(user.user_id);
    if (this.isSuperUser(user))
      return Database.orgAccessRole.admin;
    if (owned.some(orgId => orgId === organization.org_id))
      return Database.orgAccessRole.admin;
    const shared = await this._db.orgSharing.getOrganizations(user.user_id);
    for (const { orgId, accessRole } of shared) {
      if (organization.org_id === orgId)
        return accessRole;
    }
    return undefined;
  }

  async getLoggedInUser(req: Request): Promise<User | undefined> {
    const sessionToken = req.cookies?.session_token;
    if (!sessionToken)
      return undefined;
    try {
      const obj = jwt.verify(sessionToken, this._jwtSignSecret) as { userPublicId?: UserPublicId };
      if (!obj?.userPublicId || (typeof obj.userPublicId !== 'string'))
        return undefined;
      return await this._db.users.getByPublicId(obj.userPublicId);
    } catch (e) {
      return undefined;
    }
  }
}
