import { CopyObjectCommand, Create<PERSON>ucket<PERSON>ommand, DeleteO<PERSON>Command, GetObjectCommand, HeadObjectCommand, ListBucketsCommand, ListObjectsV2Command, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { retryUntilDeadline } from '@flakiness/shared/common/utils.js';
import debug from 'debug';
import fs from 'fs';
import ms from 'ms';
import { Readable } from 'stream';
import { Config } from './configuration.js';
import * as metrics from './telemetry.js';

const log = debug('fk:s3');

const histS3storage = metrics.createHistogram({
  name: 'flakiness_s3_duration_seconds',
  help: 'S3 storage operations performance',
  labelNames: [ 'method' ],
});

export type S3Configuration = {
  endpoint: string;
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
};

export type CloudFile = {
  data: Buffer,
  lastModified?: Date,
  metadata?: Record<string, string>,
}

export type S3Config = {
  endpoint: string,
  region: string,
  accessKeyId: string,
  secretAccessKey: string,
  bucketName: string,
}

export class S3 {
  private _client: S3Client;

  static async configFromEnvOrDie(): Promise<S3Config> {
    return await Config.fromEnvironmentOrDie('S3 configuration', async env => ({
      endpoint: await env.url({
        env: 'S3_ENDPOINT',
        description: ``,
        required: true,
      }),
      region: await env.text({
        env: 'S3_REGION',
        description: ``,
        required: true,
      }),
      accessKeyId: await env.secret({
        env: 'S3_ACCESS_KEY_ID',
        description: ``,
        required: true,
      }),
      secretAccessKey: await env.secret({
        env: 'S3_SECRET_ACCESS_KEY',
        description: ``,
        required: true,
      }),
      bucketName: await env.text({
        env: 'S3_BUCKET_NAME',
        description: ``,
        required: true,
      }),
    }));
  }

  static async initialize(s3config: S3Config): Promise<S3Bucket> {
    const s3 = await S3.initializeOrDie({
      endpoint: s3config.endpoint,
      accessKeyId: s3config.accessKeyId,
      secretAccessKey: s3config.secretAccessKey,
      region: s3config.region,
      connectionTimeout: 2000 // 2 seconds.
    });

    const buckets = await s3.listBuckets();
    const s3data = buckets.find((bucket: S3Bucket) => bucket.name() === s3config.bucketName);
    if (!s3data)
      throw new Error(`s3 storage must have a "${s3config.bucketName}" bucket`)
    return s3data;
  }

  static async initializeOrDie(s3config: S3Configuration & { connectionTimeout: number }): Promise<S3> {
    const s3 = new S3(s3config);
    try {
      await retryUntilDeadline(() => s3.listBuckets(), Date.now() + s3config.connectionTimeout);
      log('S3 is functioning successfully.');
      return s3;
    } catch (e) {
      log('ERROR: s3 is not accessible! %s', e)
      log(e);
      process.exit(1);
    }
  }

  constructor({ endpoint, region, accessKeyId, secretAccessKey }: S3Configuration) {
    this._client = new S3Client({
      endpoint, region, credentials: {
        accessKeyId,
        secretAccessKey,
      },
      forcePathStyle: true,
    });
  }

  async listBuckets(abortSignal?: AbortSignal): Promise<S3Bucket[]> {
    const result = await this._client.send(new ListBucketsCommand({}), { abortSignal });
    const bucketNames: string[] = (result.Buckets ?? []).map(rawBucket => rawBucket.Name).filter(rawBucket => !!rawBucket) as string[];
    return bucketNames.map(bucketName => new S3Bucket(this._client, bucketName));
  }

  async createBucket(bucketName: string, abortSignal?: AbortSignal): Promise<S3Bucket> {
    const command = new CreateBucketCommand({
      Bucket: bucketName,
    });
    await this._client.send(command, { abortSignal });
    return new S3Bucket(this._client, bucketName);
  }

  getBucket(bucketName: string): S3Bucket {
    return new S3Bucket(this._client, bucketName);
  }
}

export class S3Bucket {
  constructor(private _client: S3Client, private _bucketName: string) { }

  toString() { return `S3Bucket<${this._bucketName}>`}

  name() { return this._bucketName; }

  async getUploadURL(key: string, expiresInMs: number) {
    return await getSignedUrl(this._client, new PutObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { expiresIn: Math.round(expiresInMs / 1000) /* API accepts seconds */ });
  }

  async getReadonlyURL(key: string, expiresInMs: number = ms('10 min')) {
    return await getSignedUrl(this._client, new GetObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { expiresIn: Math.round(expiresInMs / 1000) /* API accepts seconds */ });
  }

  async uploadFile(key: string, file: CloudFile, abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    await this._client.send(new PutObjectCommand({
      Bucket: this._bucketName,
      Key: key,
      Body: file.data,
      Metadata: file.metadata,
    }), { abortSignal });
  }

  async uploadFilePath(key: string, filePath: string, abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    await this._client.send(new PutObjectCommand({
      Bucket: this._bucketName,
      Key: key,
      Body: fs.createReadStream(filePath),
    }), { abortSignal });
  }

  async *listDirectoryEntries(directory: string, abortSignal?: AbortSignal) {
    let continuationToken: any = undefined;
    do {
      abortSignal?.throwIfAborted();
      const result = await this._client.send(new ListObjectsV2Command({
        Bucket: this._bucketName,
        Prefix: directory,
        Delimiter: '/',
        ContinuationToken: continuationToken,
      }), { abortSignal });
      const files = (result.Contents ?? []).map(entry => entry.Key) as string[];
      const folders = (result.CommonPrefixes ?? []).map(entry => entry.Prefix) as string[];
      continuationToken = result.NextContinuationToken;
      yield [...files, ...folders];
    } while (continuationToken);
  }

  async *listObjects(prefix: string, abortSignal?: AbortSignal) {
    let continuationToken: any = undefined;
    do {
      abortSignal?.throwIfAborted();
      const result = await this._client.send(new ListObjectsV2Command({
        Bucket: this._bucketName,
        Prefix: prefix,
        ContinuationToken: continuationToken,
      }), { abortSignal });
      const objects = (result.Contents ?? []).map(entry => entry.Key) as string[];
      continuationToken = result.NextContinuationToken;
      yield [...objects];
    } while (continuationToken);
  }

  async *listObjectSizes(prefix: string, abortSignal?: AbortSignal) {
    let continuationToken: any = undefined;
    do {
      abortSignal?.throwIfAborted();
      const result = await this._client.send(new ListObjectsV2Command({
        Bucket: this._bucketName,
        Prefix: prefix,
        ContinuationToken: continuationToken,
      }), { abortSignal });
      const objects = (result.Contents ?? []).map(entry => [entry.Key, entry.Size]) as [string, number][];
      continuationToken = result.NextContinuationToken;
      yield [...objects];
    } while (continuationToken);
  }

  async GetObjectSize(key: string, abortSignal?: AbortSignal): Promise<number> {
    abortSignal?.throwIfAborted();
    return await this._client.send(new HeadObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { abortSignal }).then(response => response.ContentLength ?? 0).catch(() => 0);
  }

  async hasFile(key: string, abortSignal?: AbortSignal): Promise<boolean> {
    abortSignal?.throwIfAborted();
    return await this._client.send(new HeadObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { abortSignal }).then(() => true).catch(e => false);
  }

  async getMetadataSafe(key: string, abortSignal?: AbortSignal): Promise<Record<string, string>|undefined> {
    abortSignal?.throwIfAborted();
    return await this._client.send(new HeadObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { abortSignal }).then(response => response.Metadata).catch(e => undefined);
  }

  async deleteEverythingWithPrefix(prefix: string, abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    for await (const keys of this.listObjects(prefix, abortSignal))
      await this.deleteFiles(keys, abortSignal);
  }

  async deleteFiles(keys: string[], abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    // GCP does not support deletion of multiple files.
    // So instead of using DeleteObjectsCommand, we will delete keys
    // one-by-one.
    await Promise.all(keys.map(async key => {
      await this._client.send(new DeleteObjectCommand({
        Bucket: this._bucketName,
        Key: key,
      }), { abortSignal });
    }));
  }

  async copyFile(fromBucket: S3Bucket, fromKey: string, toKey: string, abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    await this._client.send(new CopyObjectCommand({
      CopySource: fromBucket._bucketName + '/' + fromKey,
      Bucket: this._bucketName,
      Key: toKey,
    }), { abortSignal });
  }

  async downloadFile(key: string, abortSignal?: AbortSignal): Promise<{ metadata?: Record<string, string>, streamFile: () => Promise<CloudFile> }|undefined> {
    abortSignal?.throwIfAborted();
    const response = await this._client.send(new GetObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { abortSignal }).catch(e => undefined);
    if (!response)
      return undefined;

    const body = response.Body;
    // Ensure the Body is a readable stream
    if (!(body instanceof Readable))
      return undefined;

    return {
      metadata: response.Metadata,
      streamFile: async () => {
        const chunks = [];
        for await (const chunk of body) {
          abortSignal?.throwIfAborted();
          chunks.push(chunk);
        }
        return {
          metadata: response.Metadata,
          lastModified: response.LastModified,
          data: Buffer.concat(chunks),
        }
      }
    }
  }

  async getFileAsBuffer(key: string, abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    const response = await this._client.send(new GetObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { abortSignal });
    // Ensure the Body is a readable stream
    if (!(response.Body instanceof Readable))
      throw new Error("Response body is not a readable stream");

    const chunks = [];
    for await (const chunk of response.Body) {
      abortSignal?.throwIfAborted();
      chunks.push(chunk);
    }
    return { data: Buffer.concat(chunks), metadata: response.Metadata ?? {} };
  }

  async getFileAsText(key: string, encoding: BufferEncoding = 'utf-8', abortSignal?: AbortSignal) {
    abortSignal?.throwIfAborted();
    const { data } = await this.getFileAsBuffer(key, abortSignal);
    return data.toString(encoding);
  }

  async saveToFilePath(key: string, filePath: string, abortSignal?: AbortSignal): Promise<void> {
    abortSignal?.throwIfAborted();
    const response = await this._client.send(new GetObjectCommand({
      Bucket: this._bucketName,
      Key: key,
    }), { abortSignal });
    // Ensure the Body is a readable stream
    if (!(response.Body instanceof Readable))
      throw new Error("Response body is not a readable stream");

    const fileStream = fs.createWriteStream(filePath);
    // Pipe the S3 stream directly to the file stream
    response.Body.pipe(fileStream);

    return new Promise((resolve, reject) => {
      fileStream.on('finish', () => resolve());
      fileStream.on('error', reject);
    });
  }
}