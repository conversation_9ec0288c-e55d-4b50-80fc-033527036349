import { GithubOAuthToken, User } from '@flakiness/database';
import { sha256Object } from '@flakiness/shared/common/utils.js';
import { Response, Router } from 'express';
import got from 'got';
import jwt from 'jsonwebtoken';
import ms from 'ms';
import { App, Octokit } from 'octokit';
import { LRUCache } from '../../common/lruCache.js';
import { ComputationCache } from '../caches.js';
import { Config } from '../configuration.js';
import { errors } from '../httpErrors.js';
import { FlakinessOctokit } from './flakinessOctokit.js';

export type GithubAppConfig = {
  appId: string,
  privateKey: string,
  clientId: string,
  clientSecret: string,
  callbackUrl: string,
  publicUrl: string,
}

export class GithubApp {
    static async configFromEnvOrDie(): Promise<GithubAppConfig> {
    return await Config.fromEnvironmentOrDie('Github App configuration', async env => ({
      appId: await env.text({
        env: 'GITHUB_APP_ID',
        description: ``,
        required: true,
      }),
      privateKey: await env.secret({
        env: 'GITHUB_APP_PRIVATE_KEY',
        description: ``,
        required: true,
      }),
      clientId: await env.text({
        env: 'GITHUB_APP_CLIENT_ID',
        description: ``,
        required: true,
      }),
      clientSecret: await env.secret({
        env: 'GITHUB_APP_CLIENT_SECRET',
        description: ``,
        required: true,
      }),
      callbackUrl: await env.url({
        env: 'GITHUB_APP_CALLBACK_URL',
        description: ``,
        required: true,
      }),
      publicUrl: await env.url({
        env: 'GITHUB_APP_PUBLIC_URL',
        description: ``,
        required: true,
      }),
    }));
  }

  private _app: App;
  private _installationIdCache = new LRUCache<{
    user: User,
    userGithubToken: GithubOAuthToken,
    owner: string,
    repo: string,
  }, number>({
    ttl: ms('1min'),
    max: 100,
    allowStale: true,
    key: (request) => sha256Object({
      githubId: request.user.github_id,
      token: request.userGithubToken.accessToken,
      owner: request.owner,
      repo: request.repo,
    }),
    compute: async ({ user, userGithubToken, owner, repo }) => {
      const repoInstallation = await this._app.octokit.rest.apps.getRepoInstallation({ owner, repo, }).catch(e => {
        return undefined;
      });
      if (!repoInstallation || !repoInstallation?.data?.id)
        return undefined;

      const octokit = new Octokit({ auth: userGithubToken.accessToken });
      const result = await octokit.request('GET /user/installations', {
        headers: {
          'X-GitHub-Api-Version': '2022-11-28'
        }
      }).catch(e => {
        return undefined;
      });
      const userInstallations = result?.data?.installations ?? [];
      return userInstallations.some(installation => installation.id === repoInstallation.data.id) ? repoInstallation.data.id : undefined;
    }
  });

  private _checkPATAccessCache = new LRUCache<{
    owner: string,
    repo: string,
    accessToken: string,
  }, boolean>({
    ttl: ms('1min'),
    max: 100,
    allowStale: true,
    key: sha256Object,
    compute: async (request) => {
      const octokit = new Octokit({ auth: request.accessToken });
      return await octokit.request('HEAD /repos/{owner}/{repo}/branches', {
        owner: request.owner,
        repo: request.repo,
      }).then(() => true).catch((error: any) => false);
    }
  });

  private _installationIdToFlakinessOctokit = new ComputationCache<number, FlakinessOctokit>({
    max: 100,
    etag: (request) => String(request),
    compute: async (etag: string, input: number) => {
      return new FlakinessOctokit(await this._app.getInstallationOctokit(input));
    }
  });

  private _patToFlakinessOctokit = new ComputationCache<string, FlakinessOctokit>({
    max: 100,
    etag: (request) => String(request),
    compute: async (etag: string, input: string) => {
      return new FlakinessOctokit(new Octokit({ auth: input }));
    }
  });

  constructor(private _options: GithubAppConfig) {
    this._app = new App({ appId: this._options.appId, privateKey: this._options.privateKey });
  }

  publicUrl(): string {
    return this._options.publicUrl;
  }

  async apiForInstallationId(installationId: number): Promise<FlakinessOctokit> {
    return await this._installationIdToFlakinessOctokit.get(installationId);
  }

  async apiForPAT(pat: string): Promise<FlakinessOctokit> {
    return await this._patToFlakinessOctokit.get(pat);
  }

  async getInstallationId({ user, owner, repo, userGithubToken } : { user: User, owner: string, repo: string, userGithubToken: GithubOAuthToken }): Promise<number|undefined> {
    return await this._installationIdCache.get({ user, owner, repo, userGithubToken });
  }

  async checkPATAccess(options: { accessToken: string, repo: string, owner: string }): Promise<boolean> {
    return await this._checkPATAccessCache.get(options) ?? false;
  }

  async refreshAccessToken(options: { refreshToken?: string, code?: string }): Promise<GithubOAuthToken> {
    const json = await got.post('https://github.com/login/oauth/access_token', {
      headers: {
        Accept: 'application/json',
      },
      searchParams: options.code ? {
        'client_id': this._options.clientId,
        'client_secret': this._options.clientSecret,
        'code': options.code,
      } : {
        'client_id': this._options.clientId,
        'client_secret': this._options.clientSecret,
        'refresh_token': options.refreshToken,
        'grant_type': 'refresh_token',
      }
    }).json<{
      access_token?: string,
      expires_in?: number,
      refresh_token?: string,
      refresh_token_expires_in?: number,
    }>();
    if (!json || !json.access_token)
      throw new errors.UnauthorizedError('Failed to obtain token');
    // If github app doesn't have user-to-server token expiration enabled (optional github app feature),
    // then there will be no refresh token and no expirations.
    // In this case, we'll say that given token is good for 100 years, and that refresh token
    // is expired already.
    if (!json.expires_in || !json.refresh_token || !json.refresh_token_expires_in) {
      return {
        accessToken: json.access_token,
        accessExpirationMs: Date.now() + ms('5 years'),
        refreshToken: '',
        refreshExpirationMs: Date.now() - ms('1 year'),
      };
    }
    return {
      accessToken: json.access_token,
      accessExpirationMs: Date.now() + json.expires_in * 1000,
      refreshToken: json.refresh_token,
      refreshExpirationMs: Date.now() + json.refresh_token_expires_in * 1000,
    };
  }

  createRouter(jwtSignSecret: string, callback: (res: Response<any>, data: {
    githubId: number,
    login: string,
    name?: string,
    avatarUrl: string,
    token: GithubOAuthToken,
  }) => Promise<void>) {
    const router = Router();
    router.get('/login/github', (req, res) => {
      const url = new URL('https://github.com/login/oauth/authorize');
      url.searchParams.set('client_id', this._options.clientId);
      url.searchParams.set('redirect_uri', new URL('login/github/callback', this._options.callbackUrl).toString());
      url.searchParams.set('scope', ['read:user'].join(' '));
      const state = jwt.sign({ date: Date.now() }, jwtSignSecret, { expiresIn: 60 * 60 /* 1 hour */});
      url.searchParams.set('state', state);

      // Set headers to prevent caching
      res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      res.set('Expires', '0');
      res.set('Pragma', 'no-cache');

      res.redirect(301, url.toString());
    });

    router.get('/login/github/callback', async (req, res) => {
      const { state, code } = req.query;
      if (typeof state !== 'string' || typeof code !== 'string')
        throw new errors.BadRequestError('"state" parameter must be a string');
      try {
        jwt.verify(state, jwtSignSecret);
      } catch (e) {
        throw new errors.BadRequestError(e instanceof Error ? e.message : 'failed to verify nonce');
      }

      const token = await this.refreshAccessToken({ code });

      const response = await got.get('https://api.github.com/user', {
        headers: {
          Authorization: 'Bearer ' + token.accessToken,
        },
      }).json<{
        id: number,
        login: string,
        avatar_url: string,
        name?: string,
      }>();

      await callback(res, {
        githubId: response.id,
        name: response.name,
        login: response.login,
        avatarUrl: response.avatar_url,
        token,
      });

      res.redirect(301, '/');
    });
    return router;
  }
}
