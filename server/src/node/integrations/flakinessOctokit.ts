import { FlakinessReport } from "@flakiness/report";
import { sha256Object } from "@flakiness/shared/common/utils.js";
import type { RestEndpointMethodTypes } from "@octokit/plugin-rest-endpoint-methods";
import { TRPCError } from "@trpc/server";
import assert from "assert";
import debug from 'debug';
import ms from "ms";
import { Octokit } from "octokit";
import { ERROR_MESSAGES } from "../../common/errors.js";
import { LRUCache } from "../../common/lruCache.js";
import { Git } from "../git.js";

const log = debug('fk:octokit');

export class FlakinessOctokit {
  private _getRepoDetails = new LRUCache<{
    owner: string,
    repo: string,
  }, RestEndpointMethodTypes["repos"]['get']["response"]>({
    ttl: ms('1min'),
    max: 1000,
    allowStale: false,
    key: sha256Object,
    compute: (request, key, stale) => cachedGithubAPIRequest(headers => this._octokit.rest.repos.get({
      owner: request.owner,
      repo: request.repo,
      headers,
    }), stale),
  });

  private _listOpenPRs = new LRUCache<{
    owner: string,
    repo: string,
    pageSize: number,
    page: number,
    state: 'open',
  }, RestEndpointMethodTypes["pulls"]["list"]["response"]>({
    ttl: ms('1min'),
    max: 1000,
    allowStale: false,
    key: sha256Object,
    compute: (request, key, stale) => cachedGithubAPIRequest(headers => this._octokit.rest.pulls.list({
      owner: request.owner,
      repo: request.repo,
      per_page: request.pageSize,
      page: request.page,
      state: request.state,
      headers,
    }), stale),
  });

  private _listCommits = new LRUCache<{
    owner: string,
    repo: string,
    pageSize: number,
    page: number,
    sha?: string,
  }, RestEndpointMethodTypes["repos"]["listCommits"]["response"]>({
    ttl: ms('1min'),
    max: 1000,
    allowStale: false,
    key: sha256Object,
    compute: (request, key, stale) => cachedGithubAPIRequest(headers => this._octokit.rest.repos.listCommits({
      owner: request.owner,
      repo: request.repo,
      sha: request.sha,
      per_page: request.pageSize,
      page: request.page,
      headers,
    }), stale)
  });

  private _listPRCommits = new LRUCache<{
    owner: string,
    repo: string,
    pullNumber: number,
    page: number,
    pageSize: number,
  }, RestEndpointMethodTypes["pulls"]["listCommits"]["response"]>({
    ttl: ms('1min'),
    max: 1000,
    allowStale: false,
    key: sha256Object,
    compute: (request, key, stale) => cachedGithubAPIRequest(headers => this._octokit.rest.pulls.listCommits({
      owner: request.owner,
      repo: request.repo,
      per_page: request.pageSize,
      page: request.page,
      pull_number: request.pullNumber,
      headers,
    }), stale),
  });

  private _listBranches = new LRUCache<{
    owner: string,
    repo: string,
    pageSize: number,
    page: number,
  }, RestEndpointMethodTypes["repos"]["listBranches"]["response"]>({
    ttl: ms('1min'),
    max: 1000,
    allowStale: false,
    key: sha256Object,
    compute: (request, key, stale) => cachedGithubAPIRequest(headers => this._octokit.rest.repos.listBranches({
      owner: request.owner,
      repo: request.repo,
      per_page: request.pageSize,
      page: request.page,
      headers,
    }), stale),
  });

  constructor(private _octokit: Octokit) {}

  async *listCommits(options: { repo: string, owner: string, sha: string }) {
    let page = 0;
    // Artificially limit number of requests; github doesn't let us do more then 5000 in an hour.
    while (page < 5000) {
      log(`[Github API] issuing commits request ${options.owner}/${options.repo} sha=${options.sha} page=${page}`);
      const response = await this._listCommits.get({
        owner: options.owner,
        repo: options.repo,
        sha: options.sha,
        pageSize: 100,
        page: page++,
      }).catch(e => {
        throw handleGithubError(e);
      });
      assert(response);
      const commits: Git.Commit[] = response.data.map(commit => ({
        author: commit.author?.name ?? commit.author?.login,
        avatar_url: commit.author?.avatar_url,
        message: commit.commit.message,
        commitId: commit.sha as FlakinessReport.CommitId,
        timestamp: +new Date(commit.commit.committer?.date ?? 0) as FlakinessReport.UnixTimestampMS,
      }));
      yield commits;
      const linkHeader = response.headers.link;
      const hasNext = linkHeader && linkHeader.includes(`rel=\"next\"`);
      if (!hasNext)
        break;
    }
  }

  async allCommits(options: {
    owner: string,
    repo: string,
    sha?: string, // optional to load default branch commits
  }): Promise<Git.Commit[]> {
    const listCommits = async (page: number, pageSize: number) => {
      const result = await this._listCommits.get({
        owner: options.owner,
        repo: options.repo,
        sha: options.sha,
        pageSize,
        page,
      });
      assert(result);
      return result;
    }
    const responses = await this._bulkLoad(listCommits).catch(e => { throw handleGithubError(e); });
    return responses.map(response => response.data.map(commit => ({
      author: commit.author?.name ?? commit.author?.login,
      avatar_url: commit.author?.avatar_url,
      message: commit.commit.message,
      commitId: commit.sha as FlakinessReport.CommitId,
      timestamp: +new Date(commit.commit.committer?.date ?? 0) as FlakinessReport.UnixTimestampMS,
    }))).flat();
  }

  async allBranches(options: { repo: string, owner: string }): Promise<{ name: string, sha: string }[]> {
    const listBranches = async (page: number, pageSize: number) => {
      const result = await this._listBranches.get({
        owner: options.owner,
        repo: options.repo,
        pageSize,
        page,
      });
      assert(result);
      return result;
    };
    const responses = await this._bulkLoad(listBranches).catch(e => {
      throw handleGithubError(e);
    });
    return responses.map(response => response.data.map(branch => ({
      name: branch.name,
      sha: branch.commit.sha,
    }))).flat();
  }

  async defaultBranch(options: { repo: string, owner: string }) {
    const result = await this._getRepoDetails.get({
      owner: options.owner,
      repo: options.repo,
    });
    return result?.data.default_branch ?? '';
  }

  async allOpenPullRequests(options: { repo: string, owner: string }): Promise<Git.PullRequest[]> {
    const listPRs = async (page: number, pageSize: number) => {
      const result = await this._listOpenPRs.get({
        owner: options.owner,
        repo: options.repo,
        page,
        pageSize,
        state: 'open',
      });
      assert(result);
      return result;
    };
    const responses = await this._bulkLoad(listPRs).catch(e => { throw handleGithubError(e); });
    return responses.map(response => response.data.map(pr => ({
      title: pr.title,
      id: pr.number + '',
    }))).flat();
  }

  async allPRCommits(options: {
    repo: string,
    owner: string
    pullRequestId: string,
  }): Promise<Git.Commit[]> {
    const pullNumber = parseInt(options.pullRequestId, 10);
    if (isNaN(pullNumber))
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'prid must be a number' });
    const listCommits = async (page: number, pageSize: number) => {
      const result = await this._listPRCommits.get({
        owner: options.owner,
        repo: options.repo,
        pullNumber,
        page,
        pageSize,
      });
      assert(result);
      return result;
    }
    const responses = await this._bulkLoad(listCommits).catch(e => { throw handleGithubError(e); });
    return responses.map(response => response.data.map(commit => ({
      author: commit.author?.name ?? commit.author?.login,
      avatar_url: commit.author?.avatar_url,
      message: commit.commit.message,
      commitId: commit.sha as FlakinessReport.CommitId,
      timestamp: +new Date(commit.commit.committer?.date ?? 0) as FlakinessReport.UnixTimestampMS,
    }))).flat();
  }

  private async _bulkLoad<T extends { url: string, headers: Record<string, string|number|undefined> }>(fetch: (page: number, pageSize: number) => Promise<T>): Promise<(T)[]> {
    const pageSize = 100;
    const firstResponse = await fetch(1, pageSize);
    const pageCount = getPageCount(firstResponse.url, firstResponse.headers.link);
    const promises = [];
    for (let page = 2; page <= pageCount; ++page) {
      promises.push(fetch(page, pageSize));
    }
      
    const result = [firstResponse, ...await Promise.all(promises)];
    return result;
  }
}

function handleGithubError(error: any): any {
  if (error.response && error.response.status === 401) {
    log(`ERROR: github auth error`);
    return new TRPCError({
      code: 'UNAUTHORIZED',
      message: ERROR_MESSAGES.SOURCE_AUTHORIZATION_FAILED,
    });
  }
  // In case of github outage, we will get HTTP 500.
  // Return stale value in this case.
  if (error.response && error.response.status === 500) {
    log(`ERROR: github api outage`);
    return new TRPCError({
      code: 'UNAUTHORIZED',
      message: ERROR_MESSAGES.GITHUB_OUTAGE_ERROR,
    });
  }
  return error;
}

function getPageCount(baseUrl: string, linkHeader: string | number | undefined) {
  if (linkHeader === undefined)
    return 1;
  linkHeader = String(linkHeader);
  const parsedLink = parseLinkHeader(baseUrl, linkHeader).find(e => e.relations.has('last'));
  if (!parsedLink)
    throw new Error(`ERROR: failed to get commits count because GitHub did not return last link; header is "${linkHeader}"`);
  const lastPage = parseInt(parsedLink.url.searchParams.get('page')!, 10);
  if (Object.is(NaN, lastPage))
    throw new Error(`ERROR: failed to get last page from 'last' link relation in header "${linkHeader}"`);
  return lastPage;
}

function parseLinkHeader(baseURL: string, linkHeader: string): { url: URL, relations: Set<string> }[] {
  const result: { url: URL, relations: Set<string> }[] = [];
  for (const aLink of linkHeader.split(',')) {
    const [ angledLink, ...rawRelations ] = aLink.split(';');
    const urlMatch = angledLink.match(/<(.*)>/);
    if (!urlMatch)
      continue;
    const url = new URL(urlMatch[1], baseURL);
    const relations = new Set<string>();
    for (const rawRelation of rawRelations) {
      const match = rawRelation.toLowerCase().match(/\s*(.+)\s*=\s*"?([^"]+)"?/);
      if (!match || match[1] !== 'rel')
        continue;
      for (const rel of match[2].split(/\s+/))
        relations.add(rel);
    }
    result.push({ url, relations });
  }
  return result;
}

async function cachedGithubAPIRequest<T extends { headers: any }>(fetch: (headers?: Record<string, string>) => Promise<T>, stale?: T) {
  const headerPairs: [string, string][] = [];
  if (stale?.headers.lastModified)
    headerPairs.push(['If-Modified-Since', stale.headers.lastModified]);
  if (stale?.headers.etag)
    headerPairs.push(['If-None-Match', stale.headers.etag]);
  try {
    return await fetch(headerPairs ? Object.fromEntries(headerPairs) : undefined);
  } catch (error: any) {
    if (error.response && error.response.status === 304)
      return stale!;
    throw error;
  }
}