import { WireTypes } from "./wireTypes.js";

export type PageOptions = {
  size: number,
  number: number,
}

export function pagedResponse<T>(elements: T[], pageOptions: PageOptions): WireTypes.PagedResponse<T> {

  let pageSize = pageOptions.size;
  let pageNumber = pageOptions.number;
  const totalPages = Math.ceil(elements.length / pageSize);
  if (-totalPages < pageNumber && pageNumber < totalPages)
    pageNumber = mod(pageNumber, totalPages);
  const pagePosition = pageNumber * pageSize;
  const pageElements = elements.slice(pagePosition, pagePosition + pageSize);

  return {
    elements: pageElements,
    pageNumber,
    pageSize,
    totalPages,
    totalElements: elements.length,
  };
}

// Support negative numbers.
function mod(n: number, m: number) {
  return ((n % m) + m) % m;
}
