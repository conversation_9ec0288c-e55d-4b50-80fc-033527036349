import { sha256Object } from "@flakiness/shared/common/utils.js";
import { WireTypes } from "../wireTypes.js";
import { Token } from "./tokens.js";

export class Field<T, K> {
  readonly defaultName: string;
  readonly aliases: string[];
  readonly extract: (element: T) => K | undefined;
  readonly nameTokens: Token[][] = [];
  readonly isUserSupplied: boolean;
  constructor(options: { defaultName: string, isUserSupplied?: boolean, aliases?: string[], extract: (e: T) => K | undefined }) {
    this.defaultName = options.defaultName;
    this.aliases = options.aliases ?? [];
    this.extract = options.extract;
    this.isUserSupplied = !!options.isUserSupplied;
    for (const name of [this.defaultName, ...this.aliases])
      this.nameTokens.push(Token.tokenize(name).tokens);
  }

  parse(tokens: Token[], tokenIndex: number) {
    for (const nameTokens of this.nameTokens) {
      if (nameTokens.every((fieldToken, fieldTokenIndex) => fieldToken.isEqual(tokens[tokenIndex + fieldTokenIndex])))
        return tokenIndex + nameTokens.length;
    }
    return -1;
  }

  isEqual(other: Field<any, any>) {
    // Fields can be dynamically created, so we have to compare them by name.
    return this.defaultName === other.defaultName && this.isUserSupplied === other.isUserSupplied;
  }

  hash() {
    return sha256Object({ name: this.defaultName, user: this.isUserSupplied });
  }

  static testToTest = new Field<WireTypes.Test, WireTypes.Test>({
    defaultName: '',
    extract: test => test,
  });

  static errorToError = new Field<WireTypes.TestError, WireTypes.TestError>({
    defaultName: '',
    extract: error => error,
  });

  static envToEnv = new Field<WireTypes.RunEnvironment, WireTypes.RunEnvironment>({
    defaultName: '',
    extract: env => env,
  });

  static testToString = {
    TEST_ID: new Field<WireTypes.Test, string>({
      defaultName: 'test.id',
      extract: test => test.testId,
    }),
  } satisfies Record<string, Field<WireTypes.Test, string>>;

  static statusToString = {
    STATUS: new Field<WireTypes.Outcome, string>({
      defaultName: 'status',
      aliases: ['s'],
      extract: (element: WireTypes.Outcome) => {
        return outcomeToStatusFilter(element);
      }
    }),
  } satisfies Record<string, Field<WireTypes.Outcome, string>>;

  static envToString = {
    ENV_ID: new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@id',
      extract: env => env.envId,
    }),
    OS_NAME: new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@os.name',
      extract: env => env.systemData.osName,
    }),
    OS_ARCH: new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@os.arch',
      extract: env => env.systemData.osArch,
    }),
    OS_VERSION: new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@os.version',
      extract: env => env.systemData.osVersion,
    }),
    ENV_NAME: new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@name',
      extract: env => env.name,
    }),
    ENV_PATH: new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@file',
      extract: env => env.configPath,
    }),
  } satisfies Record<string, Field<WireTypes.RunEnvironment, string>>;

  static errorToString = {
    ERR_TEXT: new Field<WireTypes.TestError, string>({
      defaultName: '$text',
      extract: err => err.message ?? err.value ?? '',
    }),
  } satisfies Record<string, Field<WireTypes.TestError, string>>;

  static createUserSuppliedDataField(name: string): Field<WireTypes.RunEnvironment, string> {
    return new Field<WireTypes.RunEnvironment, string>({
      defaultName: '@' + name,
      isUserSupplied: true,
      extract: env => {
        if (!env.userSuppliedData)
          return undefined;
        const value = env.userSuppliedData[name];
        if (value !== undefined)
          return String(value);
        return undefined;
      },
    });
  }

  static maybeCreateUserSuppliedDataField(tokens: Token[], tokenIndex: number): Field<WireTypes.RunEnvironment, string>|undefined {
    if (!tokens[tokenIndex]?.isPunctuation('@') || !tokens[tokenIndex + 1]?.isWord())
      return undefined;
    return Field.createUserSuppliedDataField(tokens[tokenIndex + 1].value);
  }
}

export function outcomeToStatusFilter(outcome: WireTypes.Outcome) {
  if (outcome === 'regressed')
    return 'fire';
  if (outcome === 'unexpected')
    return 'failed';
  if (outcome === 'flaked')
    return 'flaked';
  if (outcome === 'expected')
    return 'passed';
  if (outcome === 'skipped')
    return 'skipped';
  throw new Error(`Unknown outcome - "${outcome}"`)
}