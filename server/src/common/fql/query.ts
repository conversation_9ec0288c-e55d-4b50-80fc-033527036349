import { FlakinessReport } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import { Ranges } from "../ranges.js";
import type { Stats } from "../stats/stats.js";
import { WireTypes } from "../wireTypes.js";
import { Field } from "./fields.js";
import { Filter } from "./filter.js";
import { Operator } from "./operators.js";
import { Token } from "./tokens.js";

/**
 * This is the main interface to work with FQL.
 * All mutating APIs are returning a new `string`-representation of the query;
 * this way, we can always guarantee that Query objects circulating among clients
 * are parsed from string.
 * 
 * NOTE: the Query object is immutable; all mutating operations are actually returning a new string.
 */
export type QueryParseOptions = {
  disallowTestFilters?: boolean,
}

export class Query {
  static createForEnv(env: WireTypes.RunEnvironment) {
    let fql = Query.parse('');
    if (env.name)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.ENV_NAME, Operator.stringOperators.EQ, [env.name], false));
    if (env.configPath)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.ENV_PATH, Operator.stringOperators.EQ, [env.configPath], false));
    
    if (env.systemData.osName)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.OS_NAME, Operator.stringOperators.EQ, [env.systemData.osName], false));
    if (env.systemData.osVersion)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.OS_VERSION, Operator.stringOperators.EQ, [env.systemData.osVersion], false));
    if (env.systemData.osArch)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.OS_ARCH, Operator.stringOperators.EQ, [env.systemData.osArch], false));

    for (const [key, value] of Object.entries(env.userSuppliedData ?? {}))
      fql = fql.toggleFilter(new Filter('env', Field.createUserSuppliedDataField(key), Operator.stringOperators.EQ, [String(value)], false));
    return fql;
  }

  static join(query: string[]) {
    //TODO: is this safe enough? Implement a nicer joiner.
    return query.join(' ');
  }

  static create(options: {
    testId?: Stats.TestId,
    envId?: Stats.EnvId,
    envName?: string,
    configPath?: FlakinessReport.GitFilePath,
  }): Query {
    let fql = Query.parse('');
    if (options.envId)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.ENV_ID, Operator.stringOperators.EQ, [options.envId], false));
    if (options.testId)
      fql = fql.toggleFilter(new Filter('test', Field.testToString.TEST_ID, Operator.stringOperators.EQ, [options.testId], false));
    if (options.envName)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.ENV_NAME, Operator.stringOperators.EQ, [options.envName], false));
    if (options.configPath)
      fql = fql.toggleFilter(new Filter('env', Field.envToString.ENV_PATH, Operator.stringOperators.EQ, [options.configPath], false));
    return fql;
  }

  static parse(query: string, options: QueryParseOptions = {}, unparsedTokens: Token[] = []): Query {
    const {
      disallowTestFilters = false,
    } = options;
    const testUnaryFilters: Filter<WireTypes.Test, WireTypes.Test>[] = [];
    const testBinaryFilters: Filter<WireTypes.Test, string>[] = [];
    const tagUnaryFilters: Filter<WireTypes.Test, WireTypes.Test>[] = [];
    const envUnaryFilters: Filter<WireTypes.RunEnvironment, WireTypes.RunEnvironment>[] = [];
    const envBinaryFilters: Filter<WireTypes.RunEnvironment, string>[] = [];
    const statusBinaryFilters: Filter<WireTypes.Outcome, string>[] = [];
    const errorUnaryFilters: Filter<WireTypes.TestError, WireTypes.TestError>[] = [];
    const errorBinaryFilters: Filter<WireTypes.TestError, string>[] = [];
    const { tokens, error: tokenizationError } = Token.tokenize(query);
    let tokenIndex = 0;
    while (tokenIndex < tokens.length) {
      // First, try parsing all binary nodes.
      const testBinary = disallowTestFilters ? undefined : Filter.parse('test', Object.values(Field.testToString), Object.values(Operator.stringOperators), tokens, tokenIndex);
      if (testBinary) {
        testBinaryFilters.push(testBinary.result);
        tokenIndex = testBinary.newTokenIndex;
        continue;
      }
      const statusBinary = Filter.parse('status', Object.values(Field.statusToString), Object.values(Operator.stringOperators), tokens, tokenIndex);
      if (statusBinary) {
        statusBinaryFilters.push(statusBinary.result);
        tokenIndex = statusBinary.newTokenIndex;
        continue;
      }
      const errorBinary = Filter.parse('error', Object.values(Field.errorToString), Object.values(Operator.stringOperators), tokens, tokenIndex);
      if (errorBinary) {
        errorBinaryFilters.push(errorBinary.result);
        tokenIndex = errorBinary.newTokenIndex;
        continue;
      }
      const customEnvField = Field.maybeCreateUserSuppliedDataField(tokens, tokenIndex);
      const envFields = [
        Array.from(Object.values(Field.envToString)),
        customEnvField ? [customEnvField] : [],
      ].flat();
      const envBinary = Filter.parse('env', envFields, Object.values(Operator.stringOperators), tokens, tokenIndex);
      if (envBinary) {
        envBinaryFilters.push(envBinary.result);
        tokenIndex = envBinary.newTokenIndex;
        continue;
      }
      // Then try parsing all unary nodes.
      const envUnary = Filter.parse('env', [Field.envToEnv], Object.values(Operator.envOperators), tokens, tokenIndex);
      if (envUnary) {
        envUnaryFilters.push(envUnary.result);
        tokenIndex = envUnary.newTokenIndex;
        continue;
      }

      // Hidden feature: a list of values $(foo, bar, baz) is the hidden OR search for error names.
      const errorUnary = Filter.parse('error', [Field.errorToError], Object.values(Operator.errorOperators), tokens, tokenIndex);
      if (errorUnary ) {
        errorUnaryFilters.push(errorUnary.result);
        tokenIndex = errorUnary.newTokenIndex;
        continue;
      }
      const testUnary = disallowTestFilters ? undefined : Filter.parse('test', [Field.testToTest], Object.values(Operator.testOperators), tokens, tokenIndex);
      // Hidden feature: a list of values (foo, bar, baz) is the hidden OR search for test names.
      if (testUnary ) {
        testUnaryFilters.push(testUnary.result);
        tokenIndex = testUnary.newTokenIndex;
        continue;
      }
      const tagUnary = disallowTestFilters ? undefined : Filter.parse('tag', [Field.testToTest], Object.values(Operator.tagOperators), tokens, tokenIndex);
      // Hidden feature: a list of values (foo, bar, baz) is the hidden OR search for test names.
      if (tagUnary ) {
        tagUnaryFilters.push(tagUnary.result);
        tokenIndex = tagUnary.newTokenIndex;
        continue;
      }

      // If we failed to parse anything, then mark this token as "unparsed"
      unparsedTokens.push(tokens[tokenIndex++]);
    }
    return new Query(false, testUnaryFilters, testBinaryFilters, tagUnaryFilters, envUnaryFilters, envBinaryFilters, statusBinaryFilters, errorUnaryFilters, errorBinaryFilters);
  }

  static parseFilters(query: string, options?: QueryParseOptions): { filters: Filter<any, any>[], unparsed: Token[] } {
    const unparsedTokens: Token[] = [];
    const q = Query.parse(query, options, unparsedTokens);
    return {
      filters: q._allFilters,
      unparsed: unparsedTokens,
    };
  }

  private _memoizedOutcomes = new Map<WireTypes.Outcome, boolean>();
  private _memoizedTests = new Map<Stats.TestId, boolean>();
  private _memoizedEnvs = new Map<Stats.EnvId, boolean>();
  private _memoizedErrors = new Map<Stats.ErrorId, boolean>();

  private _memoizedTestsHighlight = new Map<Stats.TestId, Map<string, Ranges.Ranges<number>>>();
  private _memoizedEnvsHighlight = new Map<Stats.EnvId, Map<string, Ranges.Ranges<number>>>();
  private _memoizedErrorsHighlight = new Map<Stats.ErrorId, Map<string, Ranges.Ranges<number>>>();

  private _allFilters: Filter<any, any>[];
  private _normalized: Query;

  constructor(
    private _isNormalized: boolean,
    private readonly _testUnaryFilters: Filter<WireTypes.Test, WireTypes.Test>[],
    private readonly _testBinaryFilters: Filter<WireTypes.Test, string>[],
    private readonly _tagUnaryFilters: Filter<WireTypes.Test, WireTypes.Test>[],
    private readonly _envUnaryFilters: Filter<WireTypes.RunEnvironment, WireTypes.RunEnvironment>[],
    private readonly _envBinaryFilters: Filter<WireTypes.RunEnvironment, string>[],
    private readonly _statusBinaryFilters: Filter<WireTypes.Outcome, string>[],
    private readonly _errorUnaryFilters: Filter<WireTypes.TestError, WireTypes.TestError>[],
    private readonly _errorBinaryFilters: Filter<WireTypes.TestError, string>[],
  ) {
    this._allFilters = [
      this._tagUnaryFilters,
      this._testUnaryFilters,
      this._testBinaryFilters,
      this._envUnaryFilters,
      this._envBinaryFilters,
      this._statusBinaryFilters,
      this._errorUnaryFilters,
      this._errorBinaryFilters
    ].flat().sort(Filter.compare);
    // Consider the query "status:passed status:failed"
    // 
    // Even though this is technically an "And" condition, we treat it as an "OR" condition.
    // Query normalization will convert this to "status:(passed,failed)" with an "OR" condition.
    // And this is waht we will use for all evaluations.
    this._normalized = this._isNormalized ? this : this.normalize();
  }

  hasErrorFilters(): boolean {
    return this._errorUnaryFilters.length > 0 || this._errorBinaryFilters.length > 0;
  }

  hasStatusFilters(): boolean {
    return this._statusBinaryFilters.length > 0;
  }

  hasFieldFilters(field: Field<any, any>): boolean {
    return this._allFilters.some(filter => filter.field.defaultName === field.defaultName && filter.field.isUserSupplied === field.isUserSupplied);
  }

  hasTestFilters(): boolean {
    return this._tagUnaryFilters.length > 0 || this._testUnaryFilters.length > 0 || this._testBinaryFilters.length > 0;
  }

  normalize(): Query {
    return new Query(
      true,
      this._testUnaryFilters.map(filter => filter.normalize()),
      normalizeStringFilters(this._testBinaryFilters).map(filter => filter.normalize()),
      this._tagUnaryFilters.map(filter => filter.normalize()),
      this._envUnaryFilters.map(filter => filter.normalize()),
      normalizeStringFilters(this._envBinaryFilters).map(filter => filter.normalize()),
      normalizeStringFilters(this._statusBinaryFilters).map(filter => filter.normalize()),
      this._errorUnaryFilters.map(filter => filter.normalize()),
      normalizeStringFilters(this._errorBinaryFilters).map(filter => filter.normalize()),
    );
  }

  mapQuery(mapper: <T, K>(f: Filter<T, K>) => Filter<T, K> | undefined): Query {
    const removeUndefines = <S>(value: S | undefined): value is S => value !== undefined;
    return new Query(
      false,
      this._testUnaryFilters.map(mapper).filter(removeUndefines),
      this._testBinaryFilters.map(mapper).filter(removeUndefines),
      this._tagUnaryFilters.map(mapper).filter(removeUndefines),
      this._envUnaryFilters.map(mapper).filter(removeUndefines),
      this._envBinaryFilters.map(mapper).filter(removeUndefines),
      this._statusBinaryFilters.map(mapper).filter(removeUndefines),
      this._errorUnaryFilters.map(mapper).filter(removeUndefines),
      this._errorBinaryFilters.map(mapper).filter(removeUndefines),
    );
  }

  toggleFilter(toggle: Filter<any, any>): Query {
    const allValues = new Set(toggle.values.map(value => value.toLowerCase()));
    const toBeAdded = new Set(allValues);
    function mapper<T, K>(a: Filter<T, K>): Filter<T, K> | undefined {
      if (!a.field.isEqual(toggle.field))
        return a;
      if (!a.op.isEqual(toggle.op))
        return a;
      // Find all indexes that should be removed.
      const indexesToFilterOut = new Set<number>();
      for (let i = 0; i < a.values.length; ++i) {
        toBeAdded.delete(a.values[i]);
        if (allValues.has(a.values[i].toLowerCase()))
          indexesToFilterOut.add(i);
      }
      const values = a.values.filter((value, index) => !indexesToFilterOut.has(index));
      if (!values.length)
        return undefined;
      return new Filter<T, K>(a.type, a.field, a.op, values, a.negation, a.sourceInfo ? {
        ...a.sourceInfo,
        valuesTokens: a.sourceInfo.valuesTokens.filter((t, index) => !indexesToFilterOut.has(index)),
      } : undefined);
    }
    let q = this.mapQuery(mapper).serialize();
    if (toBeAdded.size) {
      const leftover = new Filter(toggle.type, toggle.field, toggle.op, toBeAdded, toggle.negation);
      if (q.length)
        q += ' ';
      q += leftover.serialize();
    }
    return Query.parse(q);
  }

  clearFieldFilters(field: Field<any, any>): Query {
    return this.mapQuery(e => e.field.defaultName === field.defaultName && e.field.isUserSupplied === field.isUserSupplied ? undefined : e);
  }

  filters() {
    return this._allFilters.map(filter => filter.clone());
  }
  
  clearStatusFilters(): Query {
    return this.mapQuery(e => e.type === 'status' ? undefined : e);
  }

  clearEnvFilters(): Query {
    return this.mapQuery(e => e.type === 'env' ? undefined : e);
  }

  clearErrorFilters(): Query {
    return this.mapQuery(e => e.type === 'error' ? undefined : e);
  }

  clearTestIdAndEnvIdFilters(): Query {
    return this.mapQuery(e =>
      (e.type === 'test' && e.field === Field.testToString.TEST_ID as any) ||
      (e.type === 'env' && e.field === Field.envToString.ENV_ID as any)
       ? undefined : e);
  }

  clearTestFilters(): Query {
    return this.mapQuery(e => e.type === 'test' ? undefined : e);
  }

  toggleQuery(query: Query): Query {
    let result: Query = this;
    for (const f of query._allFilters)
      result = result.toggleFilter(f);
    return result;
  }

  isMatchingFilter(filter: Filter<any, any>): boolean {
    const valuesToFind = new Set(filter.values.map(v => v.toLowerCase()));
    for (const f of this._allFilters) {
      if (!f.field.isEqual(filter.field) || !f.op.isEqual(filter.op))
        continue;
      for (const value of f.values)
        valuesToFind.delete(value.toLowerCase());
    }
    return valuesToFind.size === 0;
  }

  serialize(): string {
    return this._allFilters
        .map(filter => filter.serialize())
        .join(' ');
  }

  acceptsOutcome(outcome: WireTypes.Outcome): boolean {
    let result = this._memoizedOutcomes.get(outcome);
    if (result === undefined) {
      result = true;
      for (const filter of this._normalized._statusBinaryFilters)
        result = result && filter.accepts(outcome);
      this._memoizedOutcomes.set(outcome, result);
    }
    return result;
  }

  acceptsTest(test: WireTypes.Test): boolean {
    let result = this._memoizedTests.get(test.testId);
    if (result === undefined) {
      result = true;
      for (const filter of this._normalized._testUnaryFilters)
        result = result && filter.accepts(test);
      for (const filter of this._normalized._testBinaryFilters)
        result = result && filter.accepts(test);
      for (const filter of this._normalized._tagUnaryFilters)
        result = result && filter.accepts(test);
      this._memoizedTests.set(test.testId, result);
    }
    return result;
  }

  highlightTest(test: WireTypes.Test): Map<string, Ranges.Ranges<number>> {
    let result = this._memoizedTestsHighlight.get(test.testId);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._testUnaryFilters)
        filter.highlight(test, hl);
      for (const filter of this._normalized._testBinaryFilters)
        filter.highlight(test, hl);
      for (const filter of this._normalized._tagUnaryFilters)
        filter.highlight(test, hl);
      result = new Map<string, Ranges.Ranges<number>>();
      for (const [key, values] of hl)
        result.set(key, Ranges.unionAll(values as Iterable<Ranges.Ranges<number>>));
      this._memoizedTestsHighlight.set(test.testId, result);
    }
    return result;
  }

  acceptsEnvironment(env: WireTypes.RunEnvironment): boolean {
    let result = this._memoizedEnvs.get(env.envId);
    if (result === undefined) {
      result = true;
      for (const filter of this._normalized._envUnaryFilters)
        result = result && filter.accepts(env);
      for (const filter of this._normalized._envBinaryFilters)
        result = result && filter.accepts(env);
      this._memoizedEnvs.set(env.envId, result);
    }
    return result;
  }

  highlightEnvironment(env: WireTypes.RunEnvironment): Map<string, Ranges.Ranges<number>> {
    let result = this._memoizedEnvsHighlight.get(env.envId);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._envUnaryFilters)
        filter.highlight(env, hl);
      for (const filter of this._normalized._envBinaryFilters)
        filter.highlight(env, hl);
      result = new Map<string, Ranges.Ranges<number>>();
      for (const [key, values] of hl)
        result.set(key, Ranges.unionAll(values as Iterable<Ranges.Ranges<number>>));
      this._memoizedEnvsHighlight.set(env.envId, result);
    }
    return result;
  }

  acceptsError(error: WireTypes.TestError): boolean {
    let result = this._memoizedErrors.get(error.errorId);
    if (result === undefined) {
      result = true;
      for (const filter of this._normalized._errorUnaryFilters)
        result = result && filter.accepts(error);
      for (const filter of this._normalized._errorBinaryFilters)
        result = result && filter.accepts(error);
      this._memoizedErrors.set(error.errorId, result);
    }
    return result;
  }

  highlightError(error: WireTypes.TestError): Map<string, Ranges.Ranges<number>> {
    let result = this._memoizedErrorsHighlight.get(error.errorId);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._errorUnaryFilters)
        filter.highlight(error, hl);
      for (const filter of this._normalized._errorBinaryFilters)
        filter.highlight(error, hl);
      result = new Map<string, Ranges.Ranges<number>>();
      for (const [key, values] of hl)
        result.set(key, Ranges.unionAll(values as Iterable<Ranges.Ranges<number>>));
      this._memoizedErrorsHighlight.set(error.errorId, result);
    }
    return result;
  }
}

function normalizeStringFilters<K>(filters: Filter<K, string>[]): Filter<K, string>[] {
  if (!filters.length)
    return [];
  const type = filters[0].type;
  for (const f of filters) {
    if (f.type !== type)
      throw new Error('All filters must be of the same type');
  }

  const byFieldName = new Multimap<string, Filter<K, string>>();
  const fieldNameToField = new Map<string, Field<K, string>>();
  for (const filter of filters) {
    byFieldName.set(filter.field.defaultName, filter);
    fieldNameToField.set(filter.field.defaultName, filter.field);
  }

  const result = new Set<Filter<K, string>>(filters);
  for (const [fieldName, filters] of byFieldName) {
    // Group all same-named nodes by binary operator
    const byOperator = new Multimap<Operator<string>, Filter<K, string>>();
    for (const filter of filters)
      byOperator.set(filter.op, filter);

    // Consider the following use case:
    //    s:failed s:flaked
    // Mathematically, we would treat this as an intersection of (failed) and (flaked) -> ø,
    // however, we will instead consider this as a UNION. This way, this is the same as
    //    s:(failed, flaked)
    const eqValues = new Set(Array.from([
      ...byOperator.getAll(Operator.stringOperators.EQ).filter(f => !f.negation),
      ...byOperator.getAll(Operator.stringOperators.NEQ).filter(f => f.negation),
    ], filter => filter.values).flat());
    const neqValues = new Set(Array.from([
      ...byOperator.getAll(Operator.stringOperators.NEQ).filter(f => !f.negation),
      ...byOperator.getAll(Operator.stringOperators.EQ).filter(f => f.negation),
    ], filter => filter.values).flat());

    // Either EQ or NEQ node; if there's a `x = 1 x ≠ 1` expression, then this is
    // an empty element.
    if (eqValues.size && neqValues.size)
      result.add(new Filter(type, fieldNameToField.get(fieldName)!, Operator.stringOperators.EQ, setDifference(eqValues, neqValues), false));
    else if (eqValues.size)
      result.add(new Filter(type, fieldNameToField.get(fieldName)!, Operator.stringOperators.EQ, eqValues, false));
    else if (neqValues.size)
      result.add(new Filter(type, fieldNameToField.get(fieldName)!, Operator.stringOperators.NEQ, neqValues, false));

    for (const node of byOperator.getAll(Operator.stringOperators.EQ))
      result.delete(node);
    for (const node of byOperator.getAll(Operator.stringOperators.NEQ))
      result.delete(node);
  }
  return [...result].map(node => node.normalize());
}

function setDifference<T>(set1: Set<T>, set2: Set<T>): Set<T> {
  const result = new Set<T>(set1);
  for (const v of set2)
    result.delete(v);
  return result;
}
