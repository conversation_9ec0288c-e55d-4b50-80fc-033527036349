import { Multimap } from "@flakiness/shared/common/multimap.js";
import { Field } from "./fields.js";
import { Operator } from "./operators.js";
import { Token } from "./tokens.js";

type FilterSourceInfo = {
  first: Token;
  last: Token;
  fieldTokens: Token[];
  opTokens: Token[];
  valuesTokens: Token[][];
};

export type FilterType = 'env' | 'test' | 'error' | 'status' | 'tag';

export class Filter<F, T> {
  static parse<K, V>(type: FilterType, fields: Iterable<Field<K, V>>, operators: Iterable<Operator<V>>, tokens: Token[], tokenIndex: number): { result: Filter<K, V>, newTokenIndex: number }|undefined {
    const isNegation = tokens[tokenIndex].type === 'punctuation' && tokens[tokenIndex].value === '-';
    if (isNegation)
      ++tokenIndex;
    const field = parseFields(fields, tokens, tokenIndex);
    if (!field)
      return undefined;
    const op = parseOperators(operators, tokens, field.newTokenIndex);
    if (!op)
      return undefined;
    const values = parseValueList(tokens, op.newTokenIndex);
    if (!values)
      return undefined;
    return {
      result: new Filter(type, field.field, op.operator, values.values, isNegation, {
        first: tokens[tokenIndex],
        last: tokens[values.newTokenIndex - 1],
        fieldTokens: tokens.slice(tokenIndex, field.newTokenIndex),
        opTokens: tokens.slice(field.newTokenIndex, op.newTokenIndex),
        valuesTokens: values.valueTokens,
      }),
      newTokenIndex: values.newTokenIndex,
    };
  } 

  static compare(a: Filter<any, any>, b: Filter<any, any>): number {
    if (a.sourceInfo && b.sourceInfo)
      return a.sourceInfo.first.from - b.sourceInfo.first.from;
    if (a.field.defaultName !== b.field.defaultName)
      return a.field.defaultName < b.field.defaultName ? -1 : 1;
    if (a.op.defaultName !== b.op.defaultName)
      return a.op.defaultName < b.op.defaultName ? -1 : 1;
    if (a.values.length !== b.values.length)
      return a.values.length - b.values.length;
    for (let i = 0; i < a.values.length; ++i) {
      if (a.values[i].toLowerCase() !== b.values[i].toLowerCase())
        return a.values[i].toLowerCase() < b.values[i].toLowerCase() ? -1 : 1;
    }
    return 0;
  }

  static envFilter<F, T>(field: Field<F, T>, op: Operator<T>, values: Iterable<string>) {
    return new Filter('env', field, op, values, false);
  }

  public values: string[];

  constructor(
    public type: FilterType,
    public field: Field<F, T>,
    public op: Operator<T>,
    values: Iterable<string>,
    public negation: boolean,
    public sourceInfo?: FilterSourceInfo,
  ) {
    this.values = Array.from(values);
  }

  accepts(e: F): boolean {
    const extracted = this.field.extract(e);
    const result = this.values.some(value => this.op.evaluate(extracted, value));
    return this.negation ? !result: result;
  }

  highlight(e: F, highlight: Multimap<string, [number, number]>): boolean {
    const extracted = this.field.extract(e);
    let result = this.values.some(value => this.op.evaluate(extracted, value));
    if (this.negation)
      result = !result;
    if (result && highlight && extracted) {
      for (const value of this.values)
        this.op.highlight(extracted, value, highlight);
    }
    return this.negation ? !result: result;
  }

  clone(): Filter<F, T> {
    return new Filter<F, T>(this.type, this.field, this.op, this.values, this.negation, structuredClone(this.sourceInfo));
  }

  normalize(): Filter<F, T> {
    // Just dropping the source info
    return new Filter<F, T>(this.type, this.field, this.op, this.values.map(value => value.toLowerCase()).sort((a, b) => a < b ? -1 : 1), this.negation);
  }

  serialize(): string {
    if (this.sourceInfo) {
      return [
        this.negation ? '-' : '',
        serializeTokens(this.sourceInfo.fieldTokens),
        serializeTokens(this.sourceInfo.opTokens),
        serializeValueTokens(this.sourceInfo.valuesTokens),
      ].join('');
    }
    return [
      this.negation ? '-' : '',
      this.field.defaultName,
      this.op.defaultName,
      serializeValues(this.values),
    ].join('');
  }
}

function serializeValueTokens(tokens: Token[][]) {
  if (tokens.length === 0)
    return '()';
  if (tokens.length === 1)
    return serializeTokens(tokens[0]);
  return `(${tokens.map(valueTokens => serializeTokens(valueTokens)).join(', ')})`;
}

export function parseFields<K, V>(fields: Iterable<Field<K, V>>, tokens: Token[], tokenIndex: number) {
  for (const field of fields) {
    const newTokenIndex = field.parse(tokens, tokenIndex);
    if (newTokenIndex !== -1)
      return { field, newTokenIndex, source: tokens.slice(tokenIndex, newTokenIndex).map(token => token.value).join('') };
  }
  return undefined;
}

export function parseOperators<K>(operators: Iterable<Operator<K>>, tokens: Token[], tokenIndex: number) {
  for (const operator of operators) {
    const newTokenIndex = operator.parse(tokens, tokenIndex);
    if (newTokenIndex !== -1)
      return { operator, newTokenIndex, source: tokens.slice(tokenIndex, newTokenIndex).map(token => token.value).join('') };
  }
  return undefined;
}

export function parseValueList(tokens: Token[], tokenIndex: number): { values: string[], valueTokens: Token[][], newTokenIndex: number } | undefined {
  if (tokens[tokenIndex]?.isWord())
    return { values: [tokens[tokenIndex].value], valueTokens: [[tokens[tokenIndex]]], newTokenIndex: tokenIndex + 1 };

  if (!tokens[tokenIndex]?.isPunctuation('('))
    return undefined;
  ++tokenIndex;
  const values: string[] = [];
  const valueTokens: Token[][] = [];
  while (tokenIndex < tokens.length) {
    if (!tokens[tokenIndex]?.isWord())
      return undefined;
    valueTokens.push([tokens[tokenIndex]]);
    values.push(tokens[tokenIndex].value);
    ++tokenIndex;
    if (tokens[tokenIndex]?.isPunctuation(')'))
      return { values, valueTokens, newTokenIndex: tokenIndex + 1 };
    if (tokens[tokenIndex]?.isPunctuation(',')) {
      ++tokenIndex;
      continue;
    }
    return undefined;
  }
  return undefined;
}

function serializeValues(values: string[]) {
  const words = values.map(value => Token.serializeWord(value)).join(', ');
  return values.length > 1 ? `(${words})` : words;
}

function serializeTokens(tokens: Token[]) {
  if (!tokens.length)
    return '';
  let result = [];
  let lastToken: Token|undefined;
  for (const token of tokens) {
    if (lastToken)
      result.push(' '.repeat(token.from - lastToken.to))
    result.push(token.source.substring(token.from, token.to));
    lastToken = token;
  }
  return result.join('');
}