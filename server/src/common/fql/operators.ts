import { Multimap } from "@flakiness/shared/common/multimap.js";
import { WireTypes } from "../wireTypes.js";
import { Token } from "./tokens.js";

export class Operator<T> {
  defaultName: string;
  aliases?: string[];
  evaluate: (left: T | undefined, value: string) => boolean;
  highlight: (left: T, value: string, hl: Multimap<string, [number, number]>) => void;
  private nameTokens: Token[][] = [];

  constructor(options: {
    defaultName: string,
    aliases?: string[],
    evaluate: (left: T | undefined, value: string) => boolean,
    highlight: (left: T, value: string, hl: Multimap<string, [number, number]>) => void,
  }) {
    this.defaultName = options.defaultName;
    this.aliases = options.aliases ?? [];
    this.evaluate = options.evaluate;
    this.highlight = options.highlight;
    for (const name of [this.defaultName, ...this.aliases])
      this.nameTokens.push(Token.tokenize(name).tokens);
  }

  parse(tokens: Token[], tokenIndex: number) {
    for (const nameTokens of this.nameTokens) {
      if (nameTokens.every((opToken, opTokenIndex) => opToken.isEqual(tokens[tokenIndex + opTokenIndex])))
        return tokenIndex += nameTokens.length;
    }
    return -1;
  }

  isEqual(other: Operator<T>) {
    // Operators are never created dynamically, so we can compare them by identity.
    return this === other;
  }

  static stringOperators = {
    EQ: new Operator<string>({
      defaultName: ':',
      aliases: ['='],
      evaluate: (left, value) => value.toLowerCase() === left?.toLowerCase(),
      highlight: (left, value, hl) => hl.set(left, [0, value.length]),
    }),
    NEQ: new Operator<string>({
      defaultName: '≠',
      aliases: ['!='],
      evaluate: (left, value) => value.toLowerCase() !== left?.toLowerCase(),
      highlight: (left, value, hl) => {},
    }),
    STARTS_WITH: new Operator<string>({
      defaultName: '^=',
      evaluate: (left, value) => left ? left.toLowerCase().startsWith(value.toLowerCase()) : false,
      highlight: (left, value, hl) => hl.set(left, [0, value.length]),
    }),
  } satisfies Record<string, Operator<string>>;

  // Unary test operators.
  static testOperators: Record<string, Operator<WireTypes.Test>> = {
    // The "TEST_INCLUDES" is unusual: we treat single words, like "foo", as an unary operator "TEST_UNCLUDES" with value "foo".
    TEST_INCLUDES: new Operator<WireTypes.Test>({
      defaultName: '',
      evaluate: (test, needle) => {
        needle = needle.toLowerCase();
        if (test?.filePath.toLowerCase().indexOf(needle) !== -1)
          return true;
        if (test?.titles.some(title => title.toLowerCase().indexOf(needle) !== -1))
          return true;
        if (test?.tags?.some(tag => tag.toLowerCase().indexOf(needle) !== -1))
          return true;
        return false;
      },
      highlight: (test, needle, hl) => highlightSubstrings(hl, needle.toLowerCase(), [test.filePath, ...test.titles, ...test.tags ?? []]),
    }),
  };

  // Unary test operators.
  static tagOperators: Record<string, Operator<WireTypes.Test>> = {
    TAG_IS: new Operator<WireTypes.Test>({
      defaultName: '#',
      evaluate: (test, needle) => {
        needle = needle.toLowerCase();
        return (test?.tags?.some(tag => tag.toLowerCase() === needle)) ?? false;
      },
      highlight: (test, needle, hl) => {},
    }),
  };

  static errorOperators: Record<string, Operator<WireTypes.TestError>> = {
    ERROR_INCLUDES: new Operator<WireTypes.TestError>({
      defaultName: '$',
      evaluate: (error, needle) => {
        needle = needle.toLowerCase();
        if (!error)
          return false;
        if (error.message && error.message.toLowerCase().indexOf(needle) !== -1)
          return true;
        if (error.value && error.value.toLowerCase().indexOf(needle) !== -1)
          return true;
        return false;
      },
      highlight: (error, needle, hl) => highlightSubstrings(hl, needle.toLowerCase(), [error.message, error.value])
    }),
  };

  static envOperators: Record<string, Operator<WireTypes.RunEnvironment>> = {
    ENV_INCLUDES: new Operator<WireTypes.RunEnvironment>({
      defaultName: '@',
      evaluate: (env, needle) => {
        if (!env)
          return false;
        needle = needle.toLowerCase();
        const tokens: (string|undefined)[] = [
          env.configPath,
          env.name,
          env.systemData.osArch,
          env.systemData.osName,
          env.systemData.osVersion,
          ...Object.values(env.userSuppliedData ?? {}).map(entry => String(entry)),
        ];
        return tokens.some(token => token !== undefined && token.toLowerCase().includes(needle));
      },
      highlight: (env, needle, hl) => highlightSubstrings(hl, needle.toLowerCase(), [
        env.configPath,
        env.name,
        env.systemData.osArch,
        env.systemData.osName,
        env.systemData.osVersion,
        ...Object.values(env.userSuppliedData ?? {}).map(entry => String(entry)),
      ]),
    }),
  };
}

function highlightSubstrings(hl: Multimap<string, [number, number]>, needle: string, tokens: (string|undefined)[]) {
  for (const text of tokens) {
    if (!text)
      continue;
    const i = text.toLowerCase().indexOf(needle);
    if (i !== -1)
      hl.set(text, [i, i + needle.length])
  }
}