import { Brand } from '@flakiness/shared/common/utils.js';
import * as vlq from 'vlq';

export namespace Ranges {
  export type Ranges<T extends number> = Brand<T[], 'ranges'>;
  export type CompressedRanges<T extends number> = Brand<string, 'compressed-ranges'>;
  export type Domain<T extends number> = {
    min: T,
    max: T,
  }

  export const EMPTY = ([] as number[]) as Ranges<number>;
  export const FULL = ([-Infinity, Infinity]) as Ranges<number>;

  export function isFull(ranges: Ranges.Ranges<any>): boolean {
    return ranges.length === 2 && Object.is(ranges[0], -Infinity) && Object.is(ranges[1], Infinity);
  }

  /**
   * ranges have the following properties:
   * - sorted number pairs
   * - in each pair [a, b], b >= a
   * - for each consecutive pairs [a, b], [c, d]: c > d
   * - lots of pairs are of type [a, a]
   * 
   * This lets us compress these pairs like this:
   * 1. First number is the very first number of the ranges - 1.
   * 2. Then, transform the array of numbers into array of deltas between numbers.
   *    NOTICE: deltas between pairs are always > 0, deltas inside pairs could be 0
   * 3. Now, if the delta inside pair is 0, then record delta for this whole pair as negative.
   *    This allows us to encode ranges more efficiently than just array of numbers.
   * 4. Finally, VLQ64 the resulting array.
   * 
   * Example:
   *       ranges: [-5, -5, 2, 4]
   *   compressed: []
   * 
   * 1. first number: -5 - 1 = -6
   * 2. deltas: [1, 0, 7, 2] (the first 1 is since we compute running delta from -6)
   * 3. encoding: [-6, -1, 7, 2]
   */
  export function compress<T extends number>(ranges: Ranges<T>): CompressedRanges<T> {
    if (!ranges.length)
      return '' as CompressedRanges<T>;
    if (isInfinite(ranges))
      throw new Error('Compression of infinite ranges is not supported');
    const prepared: number[] = [];
    let last = ranges[0] - 1;
    prepared.push(last);
    for (let i = 0; i < ranges.length; i += 2) {
      if (ranges[i] === ranges[i + 1]) {
        prepared.push(-(ranges[i] - last));
      } else {
        prepared.push(ranges[i] - last);
        prepared.push(ranges[i + 1] - ranges[i]);
      }
      last = ranges[i + 1];
    }
    return vlq.encode(prepared) as CompressedRanges<T>;
  }

  export function compressValues<T extends number>(numbers: Iterable<T>): CompressedRanges<T> {
    const list = [...numbers];
    list.sort((a, b) => a - b);
    return compress(fromSortedList(list));
  }

  export function decompress<T extends number>(compressed: CompressedRanges<T>): Ranges<T> {
    if (!compressed.length)
      return [] as number[] as Ranges<T>;
    const prepared = vlq.decode(compressed) as number[];
    const result = [] as number[] as Ranges<T>;
    let last = prepared[0];
    for (let i = 1; i < prepared.length; ++i) {
      if (prepared[i] < 0) {
        result.push((-prepared[i] + last) as T);
        result.push((-prepared[i] + last) as T);
        last -= prepared[i];
      } else {
        result.push((prepared[i] + last) as T);
        last += prepared[i];
      }
    }
    return result;
  }

  export function toString<T extends number>(ranges: Ranges<T>) {
    const tokens = [];
    for (let i = 0; i < ranges.length - 1; i += 2) {
      if (ranges[i] === ranges[i + 1])
        tokens.push(ranges[i])
      else
        tokens.push(`${ranges[i]}-${ranges[i + 1]}`);
    }
    if (!tokens.length)
      return `[]`
    return `[ ` + tokens.join(', ') + ` ]`;
  }

  export function popInplace<T extends number>(ranges: Ranges<T>): T|undefined {
    if (isInfinite(ranges))
      throw new Error('cannot pop from infinite ranges!');
    const last = ranges.at(-1);
    const prelast = ranges.at(-2);

    if (last === undefined || prelast === undefined)
      return undefined;
    if (last === prelast) {
      ranges.pop();
      ranges.pop();
    } else {
      ranges[ranges.length - 1] = (last - 1) as T;
    }
    return last;
  }

  export function *iterate<T extends number>(ranges: Ranges<T>) {
    if (isInfinite(ranges))
      throw new Error('cannot iterate infinite ranges!');
    for (let i = 0; i < ranges.length - 1; i += 2) {
      for (let j = ranges[i]; j <= ranges[i + 1]; ++j)
        yield j;
    }
  }

  export function toSortedList<T extends number>(ranges: Ranges<T>): T[] {
    if (isInfinite(ranges))
      throw new Error('cannot convert infinite ranges!');
    const list = [];
    for (let i = 0; i < ranges.length - 1; i += 2) {
      for (let j = ranges[i]; j <= ranges[i + 1]; ++j)
        list.push(j);
    }
    return list;
  }

  export function fromList<T extends number>(x: T[]): Ranges<T> {
    for (let i = 0; i < x.length - 1; ++i) {
      if (x[i] > x[i + 1]) {
        x = x.toSorted((a: number, b: number) => a - b);
        break;
      }
    }
    return fromSortedList(x);
  }

  export function from<T extends number>(x: T): Ranges<T> {
    return [x, x] as Ranges<T>;
  }

  export function fromSortedList<T extends number>(sorted: T[]): Ranges<T> {
    const ranges: Ranges<T> = ([] as number[]) as Ranges<T>;
    let rangeStart = 0;
    for (let i = 1; i <= sorted.length; ++i) {
      if (i < sorted.length && sorted[i] - sorted[i - 1] <= 1)
        continue;
      ranges.push(sorted[rangeStart], sorted[i - 1]);
      rangeStart = i;
    }
    return ranges;
  }

  export function isInfinite<T extends number>(ranges: Ranges<T>): boolean {
    return ranges.length > 0 && (Object.is(ranges[0], Infinity) || Object.is(ranges[ranges.length - 1], Infinity));
  }

  export function cardinality(ranges: Ranges<any>): number {
    if (isInfinite(ranges))
      return Infinity;

    let sum = 0;
    for (let i = 0; i < ranges.length - 1; i += 2)
      sum += ranges[i + 1] - ranges[i] + 1;
    return sum;
  }
  
  export function contains<T extends number>(r: Ranges<T>, needle: T): boolean {
    for (let i = 0; i < r.length - 1; i += 2) {
      if (r[i] <= needle && needle <= r[i + 1])
        return true;
    }
    return false;
  }

  export function intersect<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): Ranges<T> {
    const ranges: Ranges<T> = ([] as number[]) as Ranges<T>;
    if (!ranges1.length || !ranges2.length)
      return ranges;
    if (ranges1[ranges1.length - 1] < ranges2[0] || ranges2[ranges2.length - 1] < ranges1[0])
      return ranges;

    let p1 = 0;
    let p2 = 0;
    while (p1 < ranges1.length - 1 && p2 < ranges2.length - 1) {
      const a1 = ranges1[p1], a2 = ranges1[p1 + 1];
      const b1 = ranges2[p2], b2 = ranges2[p2 + 1];
      if (a2 < b1) {
        p1 += 2;
      } else if (b2 < a1) {
        p2 += 2;
      } else {
        // Ranges intersect; push intersection
        ranges.push(Math.max(a1, b1) as T, Math.min(a2, b2) as T);
        if (a2 < b2) {
          p1 += 2;
        } else if (a2 > b2) {
          p2 += 2;
        } else {
          p1 += 2;
          p2 += 2;
        }
      }
    }
    return ranges;
  }

  export function hasNonEmptyIntersection<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): boolean {
    let p1 = 0;
    let p2 = 0;
    while (p1 < ranges1.length - 1 && p2 < ranges2.length - 1) {
      const a1 = ranges1[p1], a2 = ranges1[p1 + 1];
      const b1 = ranges2[p2], b2 = ranges2[p2 + 1];
      if (a2 < b1) {
        p1 += 2;
      } else if (b2 < a1) {
        p2 += 2;
      } else {
        return true;
      }
    }
    return false;
  }
  
  export function complement<T extends number>(r: Ranges<T>): Ranges<T> {
    if (r.length === 0)
      return [-Infinity, Infinity] as Ranges<T>;
    const result = ([] as number[]) as Ranges<T>;
    // [1, 5], [8, 8]
    // [-inf, 0], [6, 7], [9, inf] 
    // 
    // [-inf, 1], [8, 9]
    // [2, 7], [10, inf]
    // 
    if (!Object.is(r[0], -Infinity))
      result.push(-Infinity as T, r[0] - 1 as T);
    for (let i = 1; i < r.length - 2; i += 2)
      result.push(r[i] + 1 as T, r[i + 1] - 1 as T);
    if (!Object.is(r[r.length - 1], Infinity))
      result.push(r[r.length - 1] + 1 as T, Infinity as T);
    return result;
  }

  export function subtract<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): Ranges<T> {
    return intersect(ranges1, complement(ranges2));
  }

  export function singleRange<T extends number>(from: T, to: T) {
    return [from, to] as Ranges.Ranges<T>;
  }

  export function unionAll<T extends number>(ranges: Iterable<Ranges<T>>) {
    let result = Ranges.EMPTY as Ranges<T>;
    for (const range of ranges)
      result = Ranges.union(range, result);
    return result;
  }

  export function intersectAll<T extends number>(ranges: Iterable<Ranges<T>>) {
    let result = Ranges.FULL as Ranges<T>;
    for (const range of ranges)
      result = Ranges.intersect(result, range);
    return result;
  }

  export function domain<T extends number>(ranges: Ranges<T>): Domain<T>|undefined {
    if (!ranges.length)
      return undefined;
    return { min: ranges[0], max: ranges[ranges.length - 1] };
  }

  export function union<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): Ranges<T> {
    const ranges = ([] as number[]) as Ranges<T>;

    const merge = (a1: number, a2: number) => {
      if (!ranges.length || ranges[ranges.length - 1] < a1 - 1) {
        ranges.push(a1 as T, a2 as T);
        return;
      }
      ranges.push(Math.max(ranges.pop()!, a2) as T);
    }
  
    let p1 = 0;
    let p2 = 0;
    while (p1 < ranges1.length - 1 && p2 < ranges2.length - 1) {    
      if (ranges1[p1] < ranges2[p2]) {
        merge(ranges1[p1], ranges1[p1 + 1]);
        p1 += 2;
      } else {
        merge(ranges2[p2], ranges2[p2 + 1]);
        p2 += 2;
      }
    }
    for (; p1 < ranges1.length - 1; p1 +=2)
      merge(ranges1[p1], ranges1[p1 + 1]);
    for (; p2 < ranges2.length - 1; p2 +=2)
      merge(ranges2[p2], ranges2[p2 + 1]);
    return ranges;
  }

  export class WeightedSum<T extends number> {

    private _ranges: Ranges.Ranges<T>[] = [];
    private _weights: number[] = [];
    private _domain?: Domain<T>;

    constructor() {
    }

    static sum<T extends number>(ranges: Ranges<T>[]) {
      const union = Ranges.unionAll(ranges);
      const sum = new WeightedSum();
      for (const range of ranges)
        sum.addRange(range, 1);
      return sum.list(union);
    }

    addRange(ranges: Ranges<T>, weight: number) {
      if (!ranges.length)
        return this;
      const d = domain(ranges);
      if (!this._domain) {
        this._domain = d;
      } else if (d) {
        this._domain = {
          min: Math.min(this._domain.min, d.min) as T,
          max: Math.max(this._domain.max, d.max) as T,
        };
      }
      this._ranges.push(ranges);
      this._weights.push(weight);
      return this;
    }

    add(other: WeightedSum<T>) {
      this._ranges.push(...other._ranges);
      this._weights.push(...other._weights);
      if (!this._domain) {
        this._domain = other._domain;
      } else if (other._domain) {
        this._domain = {
          min: Math.min(this._domain.min, other._domain.min) as T,
          max: Math.max(this._domain.max, other._domain.max) as T,
        };
      }
    }

    list(ranges: Ranges.Ranges<T>): Map<T, { sum: number, count: number }> {
      if (!this._domain)
        return new Map();
      const d = this._domain;
      const dsize = d.max - d.min + 1;

      const weightsStart = new Int32Array(dsize);
      const weightsEnd = new Int32Array(dsize);
      const countsStart = new Int32Array(dsize);
      const countsEnd = new Int32Array(dsize);

      for (let rangeIdx = 0; rangeIdx < this._ranges.length; ++rangeIdx) {
        const weight = this._weights[rangeIdx] ?? 0;
        const ranges = this._ranges[rangeIdx];
        for (let i = 0; i < ranges.length; i += 2) {
          weightsStart[ranges[i] - d.min] += weight;
          weightsEnd[ranges[i + 1] - d.min] += weight;
          countsStart[ranges[i] - d.min] += 1;
          countsEnd[ranges[i + 1] - d.min] += 1;
        }
      }

      const result = new Map<T, { sum: number, count: number }>();
      
      let sum = 0;
      let count = 0;
      const toAdd = isFull(ranges) ? undefined : new Set(Ranges.toSortedList(ranges));
      for (let value = 0 as T; value < dsize; ++value) {
        sum += weightsStart[value];
        count += countsStart[value]
        if (!toAdd || toAdd.has((value + d.min) as T))
          result.set(value + d.min as T, { sum , count });
        sum -= weightsEnd[value];
        count -= countsEnd[value];
      }

      return result;
    }
  }
}
