import { Ranges } from "../ranges.js";
import { WireTypes } from "../wireTypes.js";
import { Stats } from "./stats.js";

export namespace TestOutcomes {
  export type TestRanges = Ranges.Ranges<Stats.TestIndex>;
  export type TestOutcomes = {
    regressed: TestRanges,
    unexpected: TestRanges,
    expected: TestRanges,
    skipped: TestRanges,
    flaked: TestRanges,
  };

  export type TestOutcomeCounts = {
    regressed: number,
    expected: number,
    unexpected: number,
    skipped: number,
    flaked: number,
  }

  export function newOutcomeCounts(outcomes?: TestOutcomes): TestOutcomeCounts {
    return {
      regressed: outcomes ? Ranges.cardinality(outcomes.regressed) : 0, 
      expected: outcomes ? Ranges.cardinality(outcomes.expected) : 0,
      unexpected: outcomes ? Ranges.cardinality(outcomes.unexpected) : 0,
      skipped: outcomes ? Ranges.cardinality(outcomes.skipped) : 0,
      flaked: outcomes ? Ranges.cardinality(outcomes.flaked) : 0,
    };
  }

  export function accumulateOutcomeCounts(acc: TestOutcomeCounts, other: TestOutcomeCounts) {
    acc.regressed += other.regressed;
    acc.expected += other.expected;
    acc.unexpected += other.unexpected;
    acc.skipped += other.skipped;
    acc.flaked += other.flaked;
  }

  export function newTestOutcomes(): TestOutcomes {
    return {
      regressed: Ranges.EMPTY as TestRanges,
      expected: Ranges.EMPTY as TestRanges,
      unexpected: Ranges.EMPTY as TestRanges,
      skipped: Ranges.EMPTY as TestRanges,
      flaked: Ranges.EMPTY as TestRanges,
    }
  }

  export function getTestOutcome(outcomes: TestOutcomes, testIndex: Stats.TestIndex): WireTypes.Outcome|undefined {
    if (Ranges.contains(outcomes.regressed, testIndex))
      return 'regressed';
    if (Ranges.contains(outcomes.unexpected, testIndex))
      return 'unexpected';
    if (Ranges.contains(outcomes.expected, testIndex))
      return 'expected';
    if (Ranges.contains(outcomes.skipped, testIndex))
      return 'skipped';
    if (Ranges.contains(outcomes.flaked, testIndex))
      return 'flaked';
  }

  export function normalizeInplace(outcomes: TestOutcomes) {
    let union = outcomes.regressed;
    outcomes.unexpected = Ranges.subtract(outcomes.unexpected, union);
    union = Ranges.union(union, outcomes.unexpected);
    outcomes.flaked = Ranges.subtract(outcomes.flaked, union);
    union = Ranges.union(union, outcomes.flaked);
    outcomes.expected = Ranges.subtract(outcomes.expected, union);
    union = Ranges.union(union, outcomes.expected);
    outcomes.skipped = Ranges.subtract(outcomes.skipped, union);
  }

  export function addOverlapAsFlakyInplace(outcomes: TestOutcomes) {
    // After a bunch of union's, we have tests in different sets.
    // The normalization process should make sure that each test is exactly in one set.
    // The "overlap-as-flaky" normalization considers a test that exists in unexpected
    // and either in flaked/expected as flaked.
    const allExpected = Ranges.union(outcomes.flaked, outcomes.expected);
    const allUnexpected = Ranges.union(outcomes.unexpected, outcomes.regressed);
    const unexpectedToFlaked = Ranges.intersect(allExpected, allUnexpected);
    outcomes.regressed = Ranges.subtract(outcomes.regressed, unexpectedToFlaked);
    outcomes.unexpected = Ranges.subtract(outcomes.unexpected, unexpectedToFlaked);
    outcomes.expected = Ranges.subtract(outcomes.expected, unexpectedToFlaked);
    outcomes.flaked = Ranges.union(outcomes.flaked, unexpectedToFlaked);
  }

  export function unionInplace(acc: TestOutcomes, other: TestOutcomes) {
    acc.regressed = Ranges.union(acc.regressed, other.regressed); 
    acc.expected = Ranges.union(acc.expected, other.expected);
    acc.unexpected = Ranges.union(acc.unexpected, other.unexpected);
    acc.flaked = Ranges.union(acc.flaked, other.flaked);
    acc.skipped = Ranges.union(acc.skipped, other.skipped);
    return acc;
  }

  export function intersectWithRanges(outcomes: TestOutcomes, ranges: TestRanges): TestOutcomes {
    return {
      regressed: Ranges.intersect(outcomes.regressed, ranges),
      unexpected: Ranges.intersect(outcomes.unexpected, ranges),
      expected: Ranges.intersect(outcomes.expected, ranges),
      skipped: Ranges.intersect(outcomes.skipped, ranges),
      flaked: Ranges.intersect(outcomes.flaked, ranges),
    };
  }

  export function intersectWithOutcomes(a: TestOutcomes, b: TestOutcomes): TestOutcomes {
    return {
      regressed: Ranges.intersect(a.regressed, b.regressed),
      unexpected: Ranges.intersect(a.unexpected, b.unexpected),
      expected: Ranges.intersect(a.expected, b.expected),
      skipped: Ranges.intersect(a.skipped, b.skipped),
      flaked: Ranges.intersect(a.flaked, b.flaked),
    };
  }

  export function subtractOutcomes(a: TestOutcomes, b: TestOutcomes): TestOutcomes {
    return {
      regressed: Ranges.subtract(a.regressed, b.regressed),
      unexpected: Ranges.subtract(a.unexpected, b.unexpected),
      expected: Ranges.subtract(a.expected, b.expected),
      skipped: Ranges.subtract(a.skipped, b.skipped),
      flaked: Ranges.subtract(a.flaked, b.flaked),
    };
  }

  export function unionOutcomeRanges(outcome: TestOutcomes): TestRanges {
    return Ranges.unionAll([outcome.expected, outcome.flaked, outcome.skipped, outcome.unexpected, outcome.regressed]);
  }

  export function isEmptyOutcomes(x: TestOutcomes) {
    return x.regressed.length === 0 &&
      x.expected.length === 0 &&
      x.flaked.length === 0 &&
      x.skipped.length === 0 &&
      x.unexpected.length === 0
    ;
  }

  export function isEmptyOutcomeCounts(x: TestOutcomeCounts) {
    return x.regressed === 0 &&
      x.expected === 0 &&
      x.flaked === 0 &&
      x.skipped === 0 &&
      x.unexpected === 0
    ;
  }
}