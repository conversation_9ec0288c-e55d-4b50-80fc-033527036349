import { FlakinessReport } from '@flakiness/report';
import { Bijection } from '@flakiness/shared/common/bijection.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { Ranges } from '../ranges.js';
import { Stats as S } from './stats.js';
import { TestIndex } from './testIndex.js';

export class StatsBuilder {

  private _reportIdToCommitId = new Map<S.ReportId, S.CommitId>();
  private _commitIdToIndex = new Bijection<S.CommitId, S.CommitIndex>();
  private _envIdToIndex = new Bijection<S.EnvId, S.TimelineIndex>();
  private _errorIdToIndex = new Bijection<S.ErrorId, S.ErrorIndex>();
  private _annotationIdToIndex = new Bijection<S.AnnotationId, S.AnnotationIndex>();
  private _annotationsProfileIdToIndex = new Bijection<S.AnnotationsProfileId, S.AnnotationsProfileIndex>();

  static create(testIndex: TestIndex, jsonStats?: S.JSONData) {
    if (!jsonStats || jsonStats.version < S.STATS_VERSION)
      jsonStats = S.createEmptyStats();

    const builder = new StatsBuilder(testIndex, jsonStats);

    for (let i = 0 as S.ErrorIndex; i < jsonStats.errors.length; ++i) {
      const errorId = S.computeErrorId(jsonStats.errors[i]);
      builder._errorIdToIndex.set(errorId, i);
    }
    for (let i = 0 as S.AnnotationIndex; i < jsonStats.annotations.length; ++i) {
      const annotationId = S.computeAnnotationId(jsonStats.annotations[i]);
      builder._annotationIdToIndex.set(annotationId, i);
    }
    for (let i = 0 as S.AnnotationsProfileIndex; i < jsonStats.annotationProfiles.length; ++i) {
      const annotationProfileId = S.computeAnnotationsProfileId(jsonStats.annotationProfiles[i]);
      builder._annotationsProfileIdToIndex.set(annotationProfileId, i);
    }

    for (let i = 0 as S.CommitIndex; i < jsonStats.commits.length; ++i) {
      const commitId = jsonStats.commits[i].commitId as S.CommitId;
      builder._commitIdToIndex.set(commitId, i);

      for (const report of jsonStats.commits[i].reports)
        builder._reportIdToCommitId.set(report.reportId as S.ReportId, commitId);
    }

    for (let i = 0 as S.TimelineIndex; i < jsonStats.environments.length; ++i) {
      const envId = S.computeEnvId(builder._json.environments[i]);
      builder._envIdToIndex.set(envId, i);
    }
    return builder;
  }

  constructor(private _testIndex: TestIndex, private _json: S.JSONData) {
  }

  jsonStats(): S.JSONData {
    return {
      ...this._json,
      tests: this._testIndex.serialize(),
    };
  }

  hasReport(reportId: S.ReportId) {
    return this._reportIdToCommitId.has(reportId);
  }

  reportIds(): Iterable<S.ReportId> {
    return this._reportIdToCommitId.keys();
  }

  removeReport(reportId: S.ReportId) {
    const commitId = this._reportIdToCommitId.get(reportId);
    if (!commitId)
      return;
    this._reportIdToCommitId.delete(reportId);
    let commitIndex = this._commitIdToIndex.get(commitId)!;
    const jsonCommit = this._json.commits[commitIndex];
    jsonCommit.reports = jsonCommit.reports.filter(report => report.reportId !== reportId);
  }

  addReport(reportId: S.ReportId, report: FlakinessReport.Report) {
    if (this._reportIdToCommitId.has(reportId))
      return;
    this._reportIdToCommitId.set(reportId, report.commitId);

    // Add tests that haven't been seen before to the data.
    const test2index = new Map<FlakinessReport.Test, S.TestIndex>();
    const testLineDeltas = new Multimap<number, S.TestIndex>();
    const tag2TestIndex = new Multimap<string, S.TestIndex>();
    FlakinessReport.visitTests(report, (test, parentSuites) => {
      const wireTest = S.flakinessTestToWireTypesTest(test, report.commitId, parentSuites);
      const testIdx = this._testIndex.testIndex(wireTest.testId)!;
      console.assert(testIdx !== undefined);

      test2index.set(test, testIdx);

      const delta = test.location.line - this._testIndex.lineNumber(testIdx);
      if (delta !== 0)
        testLineDeltas.set(delta, testIdx);
      for (const tag of test.tags ?? [])
        tag2TestIndex.set(tag, testIdx);
    });

    // Add missing commit, if any.
    const commitId = report.commitId;
    let commitIndex = this._commitIdToIndex.get(commitId);
    if (commitIndex === undefined) {
      commitIndex = this._json.commits.length as S.CommitIndex;
      this._json.commits.push({
        commitId,
        changedLineNumbers: undefined,
        tags: [],
        reports: [],
      });
      this._commitIdToIndex.set(commitId, commitIndex);
    }
    const commit = this._json.commits[commitIndex]!;

    // 1. Add in changedLineNumbers.
    {
      const changedLineNumbers = new Map<number, Ranges.CompressedRanges<S.TestIndex>>();
      for (const { lineDelta, affectedTests } of commit.changedLineNumbers ?? [])
        changedLineNumbers.set(lineDelta, affectedTests);
      for (const [delta, testIndexes] of testLineDeltas) {
        const compressed = changedLineNumbers.get(delta);
        const ranges = compressed ? Ranges.decompress(compressed) : Ranges.EMPTY as Ranges.Ranges<S.TestIndex>;
        changedLineNumbers.set(delta, Ranges.compress(Ranges.union(ranges, Ranges.fromList([...testIndexes]))));
      }
      commit.changedLineNumbers = [...changedLineNumbers].map(([lineDelta, affectedTests]) => ({
        lineDelta,
        affectedTests,
      }));
    }

    // Add tags in 2 steps.
    // 2. Update all existing tags
    for (const tagInfo of commit.tags) {
      if (!tag2TestIndex.hasAny(tagInfo.tag))
        continue;
      const newRanges = Ranges.fromList([...tag2TestIndex.getAll(tagInfo.tag)]);
      const savedRanges = Ranges.decompress<S.TestIndex>(tagInfo.tests);
      tagInfo.tests = Ranges.compress(Ranges.union(newRanges, savedRanges));
      tag2TestIndex.deleteAll(tagInfo.tag);
    }
    // 3. Add all missing tags
    for (const [tag, testIndexes] of tag2TestIndex) {
      const range = Ranges.fromList([...testIndexes]);
      commit.tags.push({
        tag,
        tests: Ranges.compress(range),
      });
    }

    // Add environments that haven't been seen before to the data.
    const env2index = new Map<FlakinessReport.Environment, S.TimelineIndex>();
    for (const env of report.environments) {
      const jsonEnv: S.JSONEnvironment = S.flakinessEnvToJSONEnv(report, env);
      const envId = S.computeEnvId(jsonEnv);
      let index = this._envIdToIndex.get(envId);
      if (index === undefined) {
        index = this._json.environments.length as S.TimelineIndex;
        this._envIdToIndex.set(envId, index);
        this._json.environments.push(jsonEnv);
      }
      env2index.set(env, index);
    }

    // Add runs
    const reportEnvStats: {
      expectedTests: Set<S.TestIndex>,
      unexpectedTests: Set<S.TestIndex>,
      flakedTests: Set<S.TestIndex>,
      skippedTests: Set<S.TestIndex>,

      testsWithVideo: Set<S.TestIndex>,
      testsWithTrace: Set<S.TestIndex>,
      testsWithImage: Set<S.TestIndex>,

      errors: Multimap<S.ErrorIndex, S.TestIndex>,
      timings: Map<S.TestIndex, FlakinessReport.DurationMS>,
      annotations: Multimap<S.AnnotationIndex, S.TestIndex>,
    }[] = report.environments.map(env => ({
      expectedTests: new Set(),
      unexpectedTests: new Set(),
      flakedTests: new Set(),
      skippedTests: new Set(),

      testsWithVideo: new Set(),
      testsWithTrace: new Set(),
      testsWithImage: new Set(),
  
      timings: new Map(),
      errors: new Multimap(),
      annotations: new Multimap(),
    }));
    for (const [test, testIdx] of test2index) {
      for (const run of test.runs) {
        const envStats = reportEnvStats[run.environmentIdx];

        // Add test outcome
        if (run.outcome === 'expected')
          envStats.expectedTests.add(testIdx);
        else if (run.outcome === 'unexpected')
          envStats.unexpectedTests.add(testIdx);
        else if (run.outcome === 'flaky')
          envStats.flakedTests.add(testIdx);
        else if (run.outcome === 'skipped')
          envStats.skippedTests.add(testIdx);
        else
          throw new Error(`Unknown test outcome - ${run.outcome}`);

        // Classify artifacts
        for (const attempt of run.attempts) {
          for (const attachment of attempt.attachments ?? []) {
            if (attachment.contentType.startsWith('image/'))
              envStats.testsWithImage.add(testIdx);
            else if (attachment.contentType.startsWith('video/'))
              envStats.testsWithVideo.add(testIdx);
            else if (attachment.name === 'trace')
              envStats.testsWithTrace.add(testIdx);
          }
        }

        // Add test avg. duration
        let sum = 0;
        for (const attempt of run.attempts)
          sum += attempt.duration;
        const avg = Math.round(sum / run.attempts.length) as FlakinessReport.DurationMS;
        envStats.timings.set(testIdx, avg);

        // Process errors
        const allErrors = run.attempts.map(attempt => attempt.errors ?? []).flat();
        for (const error of allErrors) {
          const jsonError: S.JSONError = {
            message: error.message,
            value: error.value,
          };
          const errorId = S.computeErrorId(jsonError);
          let errorIdx = this._errorIdToIndex.get(errorId);
          if (errorIdx === undefined) {
            errorIdx = this._json.errors.push(jsonError) - 1 as S.ErrorIndex;
            this._errorIdToIndex.set(errorId, errorIdx);
          }

          envStats.errors.set(errorIdx, testIdx);
        }

        // Process annotations
        for (const annotation of run.annotations) {
          const jsonAnnotation: S.JSONAnnotation = {
            type: annotation.type,
            description: annotation.description,
          };
          const annotationId = S.computeAnnotationId(jsonAnnotation);
          let annotationIdx = this._annotationIdToIndex.get(annotationId);
          if (annotationIdx === undefined) {
            annotationIdx = this._json.annotations.push(jsonAnnotation) - 1 as S.AnnotationIndex;
            this._annotationIdToIndex.set(annotationId, annotationIdx);
          }
          envStats.annotations.set(annotationIdx, testIdx);
        }
      }
    }

    const runs: S.JSONRun[] = [];
    for (let localEnvIndex = 0; localEnvIndex < report.environments.length; ++localEnvIndex) {
      const env = report.environments[localEnvIndex];
      const envIndex = env2index.get(env)!;
      const envStats = reportEnvStats[localEnvIndex];
      const durationBucketsToTests = computeTimeBucketsAdvanced(envStats.timings, 0.05, 50);
      const durationBuckets = [...durationBucketsToTests.keys()].sort((a, b) => a - b);

      const annotationsProfile: S.JSONRunAnnotation[] = envStats.annotations.map((annotationIdx, testIndexes) => ({
        annotationIdx,
        tests: Ranges.compressValues(testIndexes),
      }));
      const annotationsProfileId = S.computeAnnotationsProfileId(annotationsProfile);
      let annotationsProfileIdx = this._annotationsProfileIdToIndex.get(annotationsProfileId);
      if (annotationsProfileIdx === undefined) {
        annotationsProfileIdx = this._json.annotationProfiles.push(annotationsProfile) - 1 as S.AnnotationsProfileIndex;
        this._annotationsProfileIdToIndex.set(annotationsProfileId, annotationsProfileIdx);
      }

      runs.push({
        environmentIdx: envIndex,
        expectedTests: Ranges.compressValues(envStats.expectedTests) || undefined,
        unexpectedTests: Ranges.compressValues(envStats.unexpectedTests) || undefined,
        skippedTests: Ranges.compressValues(envStats.skippedTests) || undefined,
        flakedTests: Ranges.compressValues(envStats.flakedTests) || undefined,

        testsWithImage: Ranges.compressValues(envStats.testsWithImage) || undefined,
        testsWithVideo: Ranges.compressValues(envStats.testsWithVideo) || undefined,
        testsWithTrace: Ranges.compressValues(envStats.testsWithTrace) || undefined,

        durationBuckets,
        durationBucketTests: durationBuckets.map(bucket => Ranges.compressValues(durationBucketsToTests.getAll(bucket))),
        annotationsProfileIdx,
        errors: envStats.errors.size ? envStats.errors.map((errorIdx, testIndexes) => ({
          errorIdx,
          tests: Ranges.compressValues(testIndexes),
        })) : undefined,
      });
    }

    commit.reports.push({
      url: report.url ?? '',
      reportId: reportId,
      startTimestamp: report.startTimestamp,
      duration: report.duration,
      runs,
    });
  }
}

/*

Some Definitions:

0. We have 2 fundamental constants: MAX_ERR = 0.05, MIN_R = 50.
1. For the segment [Ai, Bi], we introduce the following concepts:
   - The center Ci = (Ai + Bi) / 2
   - The radius Ri = (Bi - Ai) / 2.
2. A segment [Ai, Bi] is called "good" if Ri <= Max(MAX_ERR * Ci, MIN_R)
3. A point X belongs to [Ai, Bi] if Ai <= x <= Bi.
4. For the set of points Xi that belong to the segment [Ai, Bi], MSE (Mean Squared Error) is defined as MSE = Sum (Xi - Ci)^2.

Task:

Given an array of non-negative integers, N ~ 100,000, where all numbers range from 0 to 10,000,000:
Cover these numbers with "good" segments [ai, bi] such that:
1. The number of segments is minimized.
2. The total MSE for all segments is minimized.

Logical sense: this is a "compression" task - encode all timings with a 5% error rate.
With this encoding and MAX_ERR=0.05 & MIN_R = 0, we can encode all numbers from 0 to 10,000,000 into 151 segments.

*/
function computeTimeBucketsNaive(timings: Map<S.TestIndex, FlakinessReport.DurationMS>): Multimap<FlakinessReport.DurationMS, S.TestIndex> {
  // TODO: this is a naive solution. Would something else work nicer?
  const result = new Multimap<FlakinessReport.DurationMS, S.TestIndex>();
  for (const [testIdx, timing] of timings) {
    result.set(variableDurationRounding(timing), testIdx);
  }
  return result;
}

function roundDuration(duration: FlakinessReport.DurationMS, precision: number) {
  return Math.ceil(duration / precision) * precision as FlakinessReport.DurationMS;
}

function variableDurationRounding(duration: FlakinessReport.DurationMS) {
  if (duration < 100)
    return roundDuration(duration, 15);
  if (duration < 1000)
    return roundDuration(duration, 75);
  if (duration < 10000)
    return roundDuration(duration, 150);
  return roundDuration(duration, 500);
}

function computeTimeBucketsAdvanced(timings: Map<S.TestIndex, FlakinessReport.DurationMS>, precision: number, minRadius: number): Multimap<FlakinessReport.DurationMS, S.TestIndex> {
  // TODO: this is a naive solution. Would something else work nicer?
  const timing2testId = new Multimap<FlakinessReport.DurationMS, S.TestIndex>();
  for (const [testIdx, timing] of timings)
    timing2testId.set(timing, testIdx);

  const clusters: { timings: FlakinessReport.DurationMS[], timingsSum: FlakinessReport.DurationMS }[] = [];

  for (const t of [...timing2testId.keys()].sort((a, b) => a - b)) {
    const lastCluster = clusters.at(-1);
    if (!lastCluster) {
      clusters.push({
        timings: [t],
        timingsSum: t,
      });
      continue;
    }

    const clusterPos = (lastCluster.timingsSum + t) / (lastCluster.timings.length + 1);
    const clusterR = Math.max(minRadius, precision * clusterPos);

    if ((t - lastCluster.timings[0]) > 2 * clusterR) {
      clusters.push({
        timings: [t],
        timingsSum: t,
      });
    } else {
      lastCluster.timings.push(t);
      lastCluster.timingsSum = (lastCluster.timingsSum + t) as FlakinessReport.DurationMS;
    }
  }

  const result = new Multimap<FlakinessReport.DurationMS, S.TestIndex>();
  for (const cluster of clusters) {
    const clusterPos = Math.round(cluster.timingsSum / cluster.timings.length) as FlakinessReport.DurationMS;
    for (const timing of cluster.timings)
      result.setAll(clusterPos, timing2testId.getAll(timing))
  }

  return result;
}
