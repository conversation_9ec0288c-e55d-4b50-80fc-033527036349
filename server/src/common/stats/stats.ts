import { FlakinessReport } from '@flakiness/report';
import { Brand, sha256, sha256Object } from '@flakiness/shared/common/utils.js';
import { Ranges } from '../ranges.js';
import { WireTypes } from '../wireTypes.js';

export namespace Stats {
  export const STATS_VERSION = 6;

  export type CommitId = FlakinessReport.CommitId;
  export type CommitIndex = Brand<number, 'Stats.CommitIndex'>;

  export type ReportId = Brand<number, 'Stats.ReportId'>;
  export type ReportIndex = Brand<number, 'Stats.ReportIndex'>;

  export type TestId = Brand<string, 'Stats.TestId'>;
  export type TestIndex = Brand<number, 'Stats.TestIndex'>;

  export type EnvId = Brand<string, 'Stats.EnvId'>;
  export type EnvIndex = Brand<number, 'Stats.EnvIndex'>;

  export type TimelineIndex = Brand<number, 'Stats.TimelineIndex'>;

  export type ErrorId = Brand<string, 'Stats.ErrorId'>;
  export type ErrorIndex = Brand<number, 'Stats.ErrorIndex'>;

  export type AnnotationId = Brand<string, 'Stats.AnnotationId'>;
  export type AnnotationIndex = Brand<number, 'Stats.AnnotationIndex'>;

  export type AnnotationsProfileId = Brand<string, 'Stats.AnnotationsProfileId'>;
  export type AnnotationsProfileIndex = Brand<number, 'Stats.AnnotationsProfileIndex'>;

  export type RunIndex = Brand<number, 'Stats.RunIndex'>;

  export type JSONTests = {
    // Laying out test parts separately improves compression of a 72 reports in a single commit on 13%.
    testTitles: string[][],
    testFilePaths: FlakinessReport.GitFilePath[],
    testLines: FlakinessReport.Number1Based[],
  }

  export type JSONEnvironment = {
    name?: string,
    configPath?: FlakinessReport.GitFilePath;
    systemData: {
      osName: string,
      osVersion: string,
      osArch: string,
    },
    userSuppliedData: Record<string, string|boolean|number>,
  }

  export type JSONError = {
    message?: string,
    value?: string,
  }

  export type JSONAnnotation = {
    type: string,
    description?: string,
  }

  // For each report, the set of annotations for different tests has a tendency to repeat itself.
  // So we store a set of annotations-for-tests as "profiles".
  export type JSONAnnotationsProfile = JSONRunAnnotation[];

  export type JSONData = {
    // schema version
    version: number,

    tests: JSONTests,

    environments: JSONEnvironment[],

    errors: JSONError[],

    annotations: JSONAnnotation[],
    annotationProfiles: JSONRunAnnotation[][],

    commits: JSONCommit[],
  }

  export type JSONCommit = {
    commitId: CommitId,
    changedLineNumbers?: {
      lineDelta: number,
      affectedTests: Ranges.CompressedRanges<TestIndex>,
    }[],
    tags: {
      tag: string,
      tests: Ranges.CompressedRanges<TestIndex>,
    }[],
    reports: JSONReport[];
  }

  export type JSONReport = {
    url: string,
    reportId: ReportId,
    startTimestamp?: FlakinessReport.UnixTimestampMS,
    duration?: FlakinessReport.DurationMS,

    runs: JSONRun[],
  };

  export type JSONRun = {
    environmentIdx: TimelineIndex,

    expectedTests?: Ranges.CompressedRanges<TestIndex>,
    unexpectedTests?: Ranges.CompressedRanges<TestIndex>,
    skippedTests?: Ranges.CompressedRanges<TestIndex>,
    flakedTests?: Ranges.CompressedRanges<TestIndex>,

    // Artifacts
    testsWithVideo?: Ranges.CompressedRanges<TestIndex>,
    testsWithTrace?: Ranges.CompressedRanges<TestIndex>,
    testsWithImage?: Ranges.CompressedRanges<TestIndex>,

    errors?: JSONRunError[],
    annotationsProfileIdx: AnnotationsProfileIndex,

    durationBuckets: FlakinessReport.DurationMS[],
    durationBucketTests: Ranges.CompressedRanges<TestIndex>[],
  }

  export type JSONRunError = {
    errorIdx: ErrorIndex,
    tests: Ranges.CompressedRanges<TestIndex>,
  };

  export type JSONRunAnnotation = {
    annotationIdx: AnnotationIndex,
    tests: Ranges.CompressedRanges<TestIndex>,
  };

  export function createEmptyStats(): JSONData {
    return {
      version: STATS_VERSION,
      annotations: [],
      annotationProfiles: [],
      environments: [],
      errors: [],
      tests: createEmptyTests(),
      commits: [],
    };
  }

  export function createEmptyTests(): JSONTests {
    return {
      testTitles: [],
      testFilePaths: [],
      testLines: [],
    };
  }

  export function flakinessEnvToJSONEnv(report: FlakinessReport.Report, env: FlakinessReport.Environment): JSONEnvironment {
    return {
      name: env.name,
      configPath: report.configPath,
      // Explicitly copy nested object fields: if report.environment gets extended,
      // then we have to explicitly extend the JSONEnvironment type to support it.
      systemData: {
        osArch: env.systemData.osArch ?? 'unknown',
        osName: env.systemData.osName ?? 'unknown',
        osVersion: env.systemData.osVersion ?? 'unknown',
      },
      // Currently, we only support a limited set of values for the `userSuppliedData`.
      userSuppliedData: Object.fromEntries(Object.entries(env.userSuppliedData).filter(([key, value]) => ['string', 'number', 'boolean'].includes(typeof value))),
    };
  }

  export function jsonEnvToWireEnv(jsonEnv: JSONEnvironment): WireTypes.RunEnvironment {
    return {
      envId: computeEnvId(jsonEnv),
      name: jsonEnv.name,
      configPath: jsonEnv.configPath,
      systemData: {
        osArch: jsonEnv.systemData.osArch,
        osName: jsonEnv.systemData.osName,
        osVersion: jsonEnv.systemData.osVersion,
      },
      userSuppliedData: jsonEnv.userSuppliedData,
    };
  }

  export function computeTestId(options: { file: string, titles: string[] }): TestId {
    return sha256([
      options.file, 
      ...options.titles
    ]) as TestId;
  }

  export function flakinessTestToWireTypesTest(test: FlakinessReport.Test, commitId: FlakinessReport.CommitId, parentSuites: FlakinessReport.Suite[]): WireTypes.Test {
    const suiteTitles = parentSuites.filter(suite => suite.type !== 'file').map(suite => suite.title);
    const titles = [...suiteTitles, test.title];
    const testId = computeTestId({
      file: test.location.file,
      titles: titles,
    });
    return {
      filePath: test.location.file,
      lineNumber: test.location.line,
      testId,
      titles,
      lastExecutionCommitId: commitId,
    };
  }

  export function computeEnvId(env: JSONEnvironment): EnvId {
    return sha256Object(env) as EnvId;
  }

  export function computeErrorId(error: JSONError): ErrorId {
    return sha256Object(error) as ErrorId;
  }

  export function computeAnnotationId(annotation: JSONAnnotation): AnnotationId {
    return sha256Object(annotation) as AnnotationId;
  }

  export function computeAnnotationsProfileId(profile: JSONAnnotationsProfile): AnnotationsProfileId {
    profile.sort((a, b) => a.annotationIdx - b.annotationIdx);
    return sha256(profile.map(sha256Object)) as AnnotationsProfileId;
  }

  export function computeDataSignature(s: JSONData) {
    const reportIds = s.commits.map(commit => commit.reports.map(report => report.reportId)).flat();
    reportIds.sort();
    return sha256(reportIds.map(report => String(report)));
  }
}
