import { Ranges } from '../ranges.js';

import { Multimap } from '@flakiness/shared/common/multimap.js';
import { Brand, measure } from '@flakiness/shared/common/utils.js';

import { WireTypes } from '../wireTypes.js';
import { Stats as S } from './stats.js';
import { StatsFilter } from './statsFilter.js';
import { TestOutcomes } from './testOutcomes.js';

const INSTANCEID_TEST_BITS = 20;
const INSTANCEIC_TEST_MASK = (1 << INSTANCEID_TEST_BITS) - 1;

// "C" stands for "Compound"
type CInstanceId = Brand<number, 'StatsPager.InstanceId'>;
function toInstanceId(timelineIdx: S.TimelineIndex, testIdx: S.TestIndex): CInstanceId {
  return ((timelineIdx << INSTANCEID_TEST_BITS) | testIdx) as CInstanceId;
}

function fromInstanceId(instanceIdx: CInstanceId): [S.TimelineIndex, S.TestIndex] {
  const timelineIdx = (instanceIdx >> INSTANCEID_TEST_BITS) as S.TimelineIndex;
  const testIdx = (instanceIdx & INSTANCEIC_TEST_MASK) as S.TestIndex;
  return [timelineIdx, testIdx];
}

function pageToIndexes(pageNumber: number, pageSize: number, elementsCount: number) {
  return {
    from: Math.min(Math.max(pageNumber * pageSize, 0), elementsCount),
    to: Math.min(Math.max(pageNumber * pageSize + pageSize, 0), elementsCount),
  }
}

// "C" stands for "Compound"
type CReportId = Brand<number, 'StatsPager.ReportId'>;
const REPORTID_REPORT_BITS = 16;
const REPORTID_REPORT_MASK = (1 << REPORTID_REPORT_BITS) - 1;

function toReportId(commitIdx: S.CommitIndex, reportIdx: S.ReportIndex): CReportId {
  return ((commitIdx << REPORTID_REPORT_BITS) | reportIdx) as CReportId;
}

function fromReportId(reportId: CReportId): [S.CommitIndex, S.ReportIndex] {
  const commitIdx = (reportId >>> REPORTID_REPORT_BITS) as S.CommitIndex;
  const reportIdx = (reportId & REPORTID_REPORT_MASK) as S.ReportIndex;
  return [commitIdx, reportIdx];
}

export class StatsReport {
  private _orderTestInstances: Uint32Array;
  private _orderCommits: Uint32Array;
  private _orderErrors: Uint32Array;
  private _orderTimelines: Uint32Array;
  private _orderReports: Uint32Array;

  private _testNames: string[] = [];

  private _annotations: Map<S.TimelineIndex, Multimap<S.TestIndex, WireTypes.TestAnnotation>>;
  private _errorTimelines: Uint32Array;
  private _errorTests: Uint32Array;

  private _totalOutcomes: TestOutcomes.TestOutcomeCounts;
  private _commitResults: WireTypes.CommitStats[];

  constructor(private _result: StatsFilter.TestsReport, commits: WireTypes.Commit[]) {
    const m = measure();
    m.disableLog();
    this._totalOutcomes = TestOutcomes.newOutcomeCounts();
    for (const timelineResult of this._result.timelineResults) {
      TestOutcomes.accumulateOutcomeCounts(this._totalOutcomes, TestOutcomes.newOutcomeCounts(timelineResult.chronologicalOutcomes));
    }

    const testInstanceCount = this._result.timelineResults.reduce((acc, timeline) => acc + Ranges.cardinality(TestOutcomes.unionOutcomeRanges(timeline.testOutcomes)), 0);
    this._orderTestInstances = new Uint32Array(testInstanceCount);

    const reportCount = this._result.commitResults.reduce((acc, commitResult) => acc + commitResult.reports.length, 0);
    this._orderReports = new Uint32Array(reportCount);
    let reportPointer = 0;
    for (let commitIdx = 0 as S.CommitIndex; commitIdx < this._result.commitResults.length; ++commitIdx) {
      const commitResult = this._result.commitResults[commitIdx];
      for (let reportIdx = 0 as S.ReportIndex; reportIdx < commitResult.reports.length; ++reportIdx)
        this._orderReports[reportPointer++] = toReportId(commitIdx, reportIdx);
    }

    this._errorTimelines = new Uint32Array(this._result.errors.length);
    this._errorTests = new Uint32Array(this._result.errors.length);
    this._annotations = new Map();
    let instancePointer = 0;
    for (let timelineIdx = 0 as S.TimelineIndex; timelineIdx < this._result.timelineResults.length; ++timelineIdx) {
      const timelineResult = this._result.timelineResults[timelineIdx];
      // Fill in instances
      for (const testIdx of Ranges.toSortedList(TestOutcomes.unionOutcomeRanges(timelineResult.testOutcomes)))
        this._orderTestInstances[instancePointer++] = toInstanceId(timelineIdx, testIdx);
      // Reverse annotations
      const annotations = new Multimap<S.TestIndex, WireTypes.TestAnnotation>();
      for (const annotationResult of timelineResult.annotationResults) {
        const annotation = this._result.annotations[annotationResult.annotationIdx];
        for (const testIdx of Ranges.toSortedList(annotationResult.tests))
          annotations.set(testIdx, annotation);
      }
      this._annotations.set(timelineIdx, annotations);

      for (const errorResult of timelineResult.errorResults) {
        this._errorTimelines[errorResult.errorIdx] += 1;
        this._errorTests[errorResult.errorIdx] += Ranges.cardinality(errorResult.tests);
      }
    }

    const commitIdToStats = new Map(this._result.commitResults.map(result => [result.commitId, result]));
    this._commitResults = commits.map(commit => {
      const stats = commitIdToStats.get(commit.commitId);
      return {
        commit,
        durationMs: stats?.duration ?? { count: 0, sum: 0 },
        impactedTests: stats?.errorImpactedTests ?? 0,
        impactedTimelines: stats?.errorImpactedTimelines ?? 0,
        runs: stats?.reports.map(reportResult => ({
          run: reportResult.report,
          testStatsOutcomes: reportResult.testOutcomes,
          hasImages: reportResult.hasImages,
          hasTraces: reportResult.hasTraces,
          hasVideos: reportResult.hasVideos,
        })) ?? [],
        testStats: stats?.testInstanceOutcomes ?? TestOutcomes.newOutcomeCounts(),
      } satisfies WireTypes.CommitStats;
    });

    this._orderCommits = new Uint32Array(this._commitResults.length);
    for (let i = 0; i < this._orderCommits.length; ++i)
      this._orderCommits[i] = i;

    this._orderErrors = new Uint32Array(this._result.errors.length);
    for (let i = 0; i < this._orderErrors.length; ++i)
      this._orderErrors[i] = i;

    this._orderTimelines = new Uint32Array(this._result.timelineResults.length);
    for (let i = 0; i < this._orderTimelines.length; ++i)
      this._orderTimelines[i] = i;
    m('StatsPager.init');
  }

  totalOutcomes(): WireTypes.OutcomesCount {
    return this._totalOutcomes;
  }

  counters() {
    return {
      testOutcomes: this._totalOutcomes,
      errors: this._orderErrors.length,
      commits: this._orderCommits.length,
      timelines: this._orderTimelines.length,
      tests: this._orderTestInstances.length,
      reports: this._orderReports.length,
    }
  }

  private _testSortAxisName?: WireTypes.TestStatsSortAxis;
  private _testSortDirection?: WireTypes.SortDirection;

  private _sortTests(axisName?: WireTypes.TestStatsSortAxis, direction?: WireTypes.SortDirection) {
    if (!axisName)
      return;
    if (axisName === this._testSortAxisName && direction === this._testSortDirection)
      return;

    let axis: undefined|((instanceId: CInstanceId) => (number|string));
    if (axisName === 'avg_duration') {
      axis = (instanceId) => {
        const [timelineIdx, testIdx] = fromInstanceId(instanceId);
        return this._result.timelineResults[timelineIdx].testDurationSum[testIdx] / this._result.timelineResults[timelineIdx].testDurationCount[testIdx];  
      }
    } else if (axisName === 'name') {
      if (!this._testNames.length) {
        for (const test of this._result.testSources.values())
          this._testNames.push([test.filePath, ...test.titles].join(' '));  
      }
      axis = (instanceId) => {
        const [timelineIdx, testIdx] = fromInstanceId(instanceId);
        return this._testNames[testIdx];
      }
    } else if (axisName === 'outcome') {
      axis = (instanceId) => {
        const [timelineIdx, testIdx] = fromInstanceId(instanceId);
        const testOutcomes = this._result.timelineResults[timelineIdx].chronologicalOutcomes;
        if (Ranges.contains(testOutcomes.regressed, testIdx))
          return 5;
        if (Ranges.contains(testOutcomes.unexpected, testIdx))
          return 4;
        if (Ranges.contains(testOutcomes.flaked, testIdx))
          return 3;
        if (Ranges.contains(testOutcomes.expected, testIdx))
          return 2;
        if (Ranges.contains(testOutcomes.skipped, testIdx))
          return 1;
        return 0;
      }
    }
    if (!axis)
      return;

    this._testSortAxisName = axisName;
    this._testSortDirection = direction;

    const inverse = direction === 'desc' ? -1 : 1;
    this._orderTestInstances.sort((instanceId1, instanceId2) => {
      const axis1 = axis(instanceId1 as CInstanceId);
      const axis2 = axis(instanceId2 as CInstanceId);
      if (axis1 === axis2)
        return 0;
      return (axis1 < axis2 ? -1 : 1) * inverse;
    });
  }

  pageTestStats(pageOptions: WireTypes.PageOptions, sortAxis?: WireTypes.TestStatsSortAxis, sortDirection?: WireTypes.SortDirection): WireTypes.PagedResponse<WireTypes.TestStats> {    
    this._sortTests(sortAxis, sortDirection);
    const pageNumber = pageOptions.number;
    const pageSize = pageOptions.size;
    const testStats: WireTypes.TestStats[] = [];
    const { from, to } = pageToIndexes(pageNumber, pageSize, this._orderTestInstances.length);
    for (let i = from; i < to; ++i) {
      const [timelineIdx, testIdx] = fromInstanceId(this._orderTestInstances[i] as CInstanceId);
      const s = this._result.timelineResults[timelineIdx].statusReports.find(sr => Ranges.contains(sr.tests, testIdx))!;
      testStats.push({
        durationMs: {
          sum: this._result.timelineResults[timelineIdx].testDurationSum[testIdx],
          count: this._result.timelineResults[timelineIdx].testDurationCount[testIdx],
        },
        timeline: this._result.timelineResults[timelineIdx].timeline,
        outcome: TestOutcomes.getTestOutcome(this._result.timelineResults[timelineIdx].chronologicalOutcomes, testIdx)!,

        hasImage: Ranges.contains(s.testsWithImage, testIdx),
        hasVideo: Ranges.contains(s.testsWithVideo, testIdx),
        hasTrace: Ranges.contains(s.testsWithTrace, testIdx),

        test: this._result.testSources.get(testIdx)!,
        annotations: this._annotations.get(timelineIdx)!.getAll(testIdx),
        commitId: s.commitId,
        reportId: s.reportId,
      });
    }
    return {
      elements: testStats,
      pageNumber,
      pageSize,
      totalElements: this._orderTestInstances.length,
      totalPages: Math.ceil(this._orderTestInstances.length / pageSize),
    }
  }

  private _sortTimelines(sortAxis?: WireTypes.TimelineStatsSortAxis, sortDirection?: WireTypes.SortDirection) {
    let axis: undefined|((timelineIdx: S.TimelineIndex) => (number|string));
    if (sortAxis === 'total_time') {
      axis = timelineIdx => {
        const timelineResult = this._result.timelineResults[timelineIdx];
        return timelineResult.duration.sum;
      };
    }
    if (!axis)
      return;
    const inverse = sortDirection === 'desc' ? -1 : 1;
    this._orderTimelines.sort((timelineIdx1, timelineIdx2) => {
      const axis1 = axis(timelineIdx1 as S.TimelineIndex);
      const axis2 = axis(timelineIdx2 as S.TimelineIndex);
      if (axis1 === axis2)
        return 0;
      return (axis1 < axis2 ? -1 : 1) * inverse;
    });
  }

  pageTimelineStats(pageOptions: WireTypes.PageOptions, sortAxis?: WireTypes.TimelineStatsSortAxis, sortDirection?: WireTypes.SortDirection): WireTypes.PagedResponse<WireTypes.TimelineStats> {
    this._sortTimelines(sortAxis, sortDirection);
    const pageNumber = pageOptions.number;
    const pageSize = pageOptions.size;
    const timelineStats: WireTypes.TimelineStats[] = [];
    const { from, to } = pageToIndexes(pageNumber, pageSize, this._orderTimelines.length);
    for (let i = from; i < to; ++i) {
      const timelineResult = this._result.timelineResults[this._orderTimelines[i]];
      timelineStats.push({
        timeline: timelineResult.timeline,
        durationMs: timelineResult.duration,
      });
    }
    return {
      elements: timelineStats,
      pageNumber,
      pageSize,
      totalElements: this._orderTimelines.length,
      totalPages: Math.ceil(this._orderTimelines.length / pageSize),
    };
  }

  private _sortCommits(sortAxis?: WireTypes.CommitStatsSortAxis, sortDirection?: WireTypes.SortDirection) {
    let axis: undefined|((commitIdx: S.CommitIndex) => (number|string));
    if (sortAxis === 'chrono') {
      axis = commitIdx => -commitIdx;
    } else if (sortAxis === 'regressed') {
      axis = commitIdx => this._commitResults[commitIdx].testStats.regressed
    } else if (sortAxis === 'unexpected') {
      axis = commitIdx => this._commitResults[commitIdx].testStats.unexpected
    } else if (sortAxis === 'expected') {
      axis = commitIdx => this._commitResults[commitIdx].testStats.expected
    } else if (sortAxis === 'skipped') {
      axis = commitIdx => this._commitResults[commitIdx].testStats.skipped
    } else if (sortAxis === 'flaked') {
      axis = commitIdx => this._commitResults[commitIdx].testStats.flaked
    }
    if (!axis)
      return;

    const inverse = sortDirection === 'desc' ? -1 : 1;
    this._orderCommits.sort((commitIdx1, commitIdx2) => {
      const axis1 = axis(commitIdx1 as S.CommitIndex);
      const axis2 = axis(commitIdx2 as S.CommitIndex);
      if (axis1 === axis2)
        return 0;
      return (axis1 < axis2 ? -1 : 1) * inverse;
    });
  }

  pageCommitStats(pageOptions: WireTypes.PageOptions, sortAxis?: WireTypes.CommitStatsSortAxis, sortDirection?: WireTypes.SortDirection): WireTypes.PagedResponse<WireTypes.CommitStats> {
    this._sortCommits(sortAxis, sortDirection);

    const pageNumber = pageOptions.number;
    const pageSize = pageOptions.size;
    const commitStats: WireTypes.CommitStats[] = [];
    const { from, to } = pageToIndexes(pageNumber, pageSize, this._orderCommits.length);
    for (let i = from; i < to; ++i) {
      const commitResult = this._commitResults[this._orderCommits[i]];
      commitStats.push(commitResult);
    }
    return {
      elements: commitStats,
      pageNumber,
      pageSize,
      totalElements: this._orderCommits.length,
      totalPages: Math.ceil(this._orderCommits.length / pageSize),
    };
  }

  private _sortReports(sortAxis?: WireTypes.ReportStatsSortAxis, sortDirection?: WireTypes.SortDirection) {
    let axis: undefined|((commitIdx: S.CommitIndex, reportIdx: S.ReportIndex) => (number|string));
    if (sortAxis === 'chrono') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].report.startTimestamp ?? 0;
    } else if (sortAxis === 'regressed') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].testOutcomes.regressed;
    } else if (sortAxis === 'unexpected') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].testOutcomes.unexpected;
    } else if (sortAxis === 'expected') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].testOutcomes.expected;
    } else if (sortAxis === 'skipped') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].testOutcomes.skipped;
    } else if (sortAxis === 'flaked') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].testOutcomes.flaked;
    } else if (sortAxis === 'factual_duration') {
      axis = (commitIdx, reportIdx) => this._result.commitResults[commitIdx].reports[reportIdx].report.duration ?? 0;
    }
    if (!axis)
      return;

    const inverse = sortDirection === 'desc' ? -1 : 1;
    this._orderReports.sort((reportId1, reportId2) => {
      const axis1 = axis(...fromReportId(reportId1 as CReportId));
      const axis2 = axis(...fromReportId(reportId2 as CReportId));
      if (axis1 === axis2)
        return 0;
      return (axis1 < axis2 ? -1 : 1) * inverse;
    });
  }

  pageRunStats(pageOptions: WireTypes.PageOptions, sortAxis?: WireTypes.ReportStatsSortAxis, sortDirection?: WireTypes.SortDirection): WireTypes.PagedResponse<WireTypes.RunStats> {
    this._sortReports(sortAxis, sortDirection);

    const pageNumber = pageOptions.number;
    const pageSize = pageOptions.size;
    const reportStats: WireTypes.RunStats[] = [];
    const { from, to } = pageToIndexes(pageNumber, pageSize, this._orderReports.length);
    for (let i = from; i < to; ++i) {
      const [commitIdx, reportIdx] = fromReportId(this._orderReports[i] as CReportId);
      const reportResult = this._result.commitResults[commitIdx].reports[reportIdx];
      reportStats.push({
        run: reportResult.report,
        testStatsOutcomes: reportResult.testOutcomes,
        hasImages: reportResult.hasImages,
        hasTraces: reportResult.hasTraces,
        hasVideos: reportResult.hasVideos,
      });
    }
    return {
      elements: reportStats,
      pageNumber,
      pageSize,
      totalElements: this._orderReports.length,
      totalPages: Math.ceil(this._orderReports.length / pageSize),
    }
  }

  private _sortErrors(sortAxis?: WireTypes.ErrorStatsSortAxis, sortDirection?: WireTypes.SortDirection) {
    let axis: undefined|((envIdx: S.ErrorIndex) => (number|string));
    if (sortAxis === 'name') {
      axis = errIdx => this._result.errors[errIdx].message ?? this._result.errors[errIdx].value ?? ''
    } else if (sortAxis === 'timelines') {
      axis = errIdx => this._errorTimelines[errIdx];
    } else if (sortAxis === 'tests') {
      axis = errIdx => this._errorTests[errIdx];
    }
    if (!axis)
      return;
    const inverse = sortDirection === 'desc' ? -1 : 1;
    this._orderErrors.sort((errIdx1, errIdx2) => {
      const axis1 = axis(errIdx1 as S.ErrorIndex);
      const axis2 = axis(errIdx2 as S.ErrorIndex);
      if (axis1 === axis2)
        return 0;
      return (axis1 < axis2 ? -1 : 1) * inverse;
    });
  }

  pageErrorStats(pageOptions: WireTypes.PageOptions, sortAxis?: WireTypes.ErrorStatsSortAxis, sortDirection?: WireTypes.SortDirection): WireTypes.PagedResponse<WireTypes.ErrorStats> {
    this._sortErrors(sortAxis, sortDirection);

    const pageNumber = pageOptions.number;
    const pageSize = pageOptions.size;

    const errorStats: WireTypes.ErrorStats[] = [];
    const { from, to } = pageToIndexes(pageNumber, pageSize, this._orderErrors.length);
    for (let i = from; i < to; ++i) {
      errorStats.push({
        error: this._result.errors[this._orderErrors[i]],
        impactedTimelines: this._errorTimelines[this._orderErrors[i]],
        impactedTests: this._errorTests[this._orderErrors[i]],
      });
    }
    return {
      elements: errorStats,
      pageNumber,
      pageSize,
      totalElements: this._orderErrors.length,
      totalPages: Math.ceil(this._orderErrors.length / pageSize),
    };
  }
}
