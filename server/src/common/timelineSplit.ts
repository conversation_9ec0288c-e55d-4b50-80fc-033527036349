import { Field } from "./fql/fields.js";
import { Filter } from "./fql/filter.js";
import { Operator } from "./fql/operators.js";
import { Query } from "./fql/query.js";
import { WireTypes } from "./wireTypes.js";

/*

In report, we're interested in a set of `timelines`. These could be
all timelines for Playwright Projects, or max granularity timelines, or some
custom-defined set of timelines. Internally, timeline is just an FQL filter that
merges all matching environments.

`TimelineSplit` defines rules to filter & produce the group of timelines, or a single
timeline that is a union of all the timelines.

Internally, TimelineSplit consists of:
- list of system-defined fields that should "split" timelines
- list of user-defined fields that should "split" timelines
- additional filter for the timelines.

There are certain pre-defined timeline sets:
- all Playwright Projects
- maximum granularity timelines
- unified timeline

*/

const G_BROWSER_FIELD = Field.createUserSuppliedDataField('browser');

export class TimelineSplit {
  static someWithBrowserField(envs: WireTypes.RunEnvironment[]) {
    return envs.some(env => !!G_BROWSER_FIELD.extract(env));
  }

  static unifiedTimeline() {
    return TimelineSplit.parse({});
  }

  static playwrightProjects() {
    return TimelineSplit.parse({
      filter: '',
      system: [Field.envToString.ENV_NAME.defaultName, Field.envToString.ENV_PATH.defaultName],
    });
  }

  static playwrightProjectsWithBrowsers() {
    return TimelineSplit.parse({
      filter: '',
      system: [Field.envToString.ENV_NAME.defaultName, Field.envToString.ENV_PATH.defaultName],
      user: [G_BROWSER_FIELD.defaultName],
    });
  }

  static fromTimeline(timeline: string) {
    return new TimelineSplit(new Map(), Query.parse(timeline));
  }

  static parse(json: WireTypes.JSONTimelineSplit) {
    const split: Map<string, Field<WireTypes.RunEnvironment, string>> = new Map();  
    for (const usr of json.user ?? []) {
        // We receive user fields prefixed with '@', but we create them with the JSON property name.
      const field = Field.createUserSuppliedDataField(usr.substring(1));
      split.set(field.hash(), field);
    }
    const systemFields = [...new Set(json.system ?? [])].map(f => [...Object.values(Field.envToString)].find(field => field.defaultName === f)).filter(f => !!f);
    for (const sys of systemFields) {
      split.set(sys.hash(), sys);
    }
    const envFilter = Query.parse(json.filter ?? '').normalize();
    return new TimelineSplit(split, envFilter);
  }

  private _envFilter: Query;
  private _split: Map<string, Field<WireTypes.RunEnvironment, string>>;

  constructor(split: Map<string, Field<WireTypes.RunEnvironment, string>>, envFilter: Query) {
    this._envFilter = envFilter;
    this._split = split;
    for (const filter of envFilter.filters())
      this._split.set(filter.field.hash(), filter.field);
  }

  isEqual(other: TimelineSplit) {
    if (this._envFilter.serialize() !== other._envFilter.serialize())
      return false;
    if (this._split.size !== other._split.size)
      return false;
    for (const key of this._split.keys()) {
      if (!other._split.has(key))
        return false;
    }
    return true;
  }

  isUnifiedTimeline() {
    return this._envFilter.filters().length === 0 && !this._split.size;
  }

  isPlaywrightProject() {
    const filters = new Map(this._envFilter.filters().map(f => [f.field.hash(), f]));
    if (filters.size !== 2)
      return false;
    return filters.get(Field.envToString.ENV_NAME.hash())?.values.length === 1 &&
           filters.get(Field.envToString.ENV_PATH.hash())?.values.length === 1 &&
           this._split.size === 2;
  }

  isPlaywrightProjectWithBrowser() {
    const filters = new Map(this._envFilter.filters().map(f => [f.field.hash(), f]));
    if (filters.size !== 3)
      return false;
    return filters.get(Field.envToString.ENV_NAME.hash())?.values.length === 1 &&
           filters.get(Field.envToString.ENV_PATH.hash())?.values.length === 1 &&
           filters.get(G_BROWSER_FIELD.hash())?.values.length === 1 &&
           this._split.size === 3;
  }

  acceptsEnvironment(env: WireTypes.RunEnvironment): boolean {
    return this._envFilter.acceptsEnvironment(env);
  }

  splitBy(field: Field<WireTypes.RunEnvironment, any>) {
    if (this._split.has(field.hash()))
      return this;
    const newSplit = new Map(this._split);
    newSplit.set(field.hash(), field);
    return new TimelineSplit(newSplit, this._envFilter);
  }

  // This will collapse and clear all field filters as well.
  clearAndCollapse(field: Field<WireTypes.RunEnvironment, any>) {
    const newSplit = new Map(this._split);
    newSplit.delete(field.hash());
    const newEnvFilter = this._envFilter.clearFieldFilters(field);
    return new TimelineSplit(newSplit, newEnvFilter);
  }

  isSplitBy(field: Field<WireTypes.RunEnvironment, any>) {
    return this._split.has(field.hash());
  }

  // This will toggle value in a TimelineSplit
  toggleValue(field: Field<WireTypes.RunEnvironment, any>, value: string) {
    const newFql = this._envFilter.toggleFilter(Filter.envFilter(field, Operator.stringOperators.EQ, [value]));
    const newSplit = new Map(this._split);
    return new TimelineSplit(newSplit, newFql);
  }

  hasValue(field: Field<WireTypes.RunEnvironment, any>, value: string) {
    return this._envFilter.isMatchingFilter(Filter.envFilter(field, Operator.stringOperators.EQ, [value]))  
  }

  toggleEnsureNonEmptySelection(field: Field<WireTypes.RunEnvironment, any>, value: string, envs: WireTypes.RunEnvironment[]) {
    const filter = Filter.envFilter(field, Operator.stringOperators.EQ, [value]);
    let newFql = this._envFilter.toggleFilter(filter);
    const newSplit = new Map(this._split);

    if (!envs.some(env => newFql.acceptsEnvironment(env))) {
      // list all envs that match just the clicked value.
      const e = Query.parse('').toggleFilter(filter);
      const simpleEnvs = envs.filter(env => e.acceptsEnvironment(env));
      // Drop all filters that don't match any of the simple envs.
      for (const filter of newFql.filters()) {
        const s = Query.parse('').toggleFilter(filter);
        if (!simpleEnvs.some(env => s.acceptsEnvironment(env)))
          newFql = newFql.clearFieldFilters(filter.field);
      }
    }
    return new TimelineSplit(newSplit, newFql);
  }

  serialize(): WireTypes.JSONTimelineSplit {
    return {
      filter: this._envFilter.serialize(),
      system: [...this._split.values()].filter(field => !field.isUserSupplied).map(field => field.defaultName),
      user: [...this._split.values()].filter(field => field.isUserSupplied).map(field => field.defaultName),
    };
  }

  timelines(envs: WireTypes.RunEnvironment[]): string[] {
    const timelines = new Set<string>();
    for (const env of envs) {
      if (!this._envFilter.acceptsEnvironment(env))
        continue;
      const filters: Filter<WireTypes.RunEnvironment, string>[] = [];
      for (const field of this._split.values()) {
        const value = field.extract(env);
        if (!value)
          continue;
        filters.push(new Filter('env', field, Operator.stringOperators.EQ, [value], false));
      }
      const timeline = filters.map(f => f.serialize()).join(' ');
      timelines.add(timeline);
    }
    return [...timelines].sort((a, b) => a < b ? -1 : 1);
  }

  timeline(): string {
    return this._envFilter.serialize();
  }
}