import { FlakinessReport } from "@flakiness/report";
import ms from "ms";

const utcMonthToAbbr = [
  `jan`, `feb`, `mar`, `apr`, `may`, `jun`, `jul`, `aug`, `sep`, `oct`, `nov`, `dec`
];

export const DateTime = {
  daystart(timestamp: FlakinessReport.UnixTimestampMS): FlakinessReport.UnixTimestampMS {
    const date = new Date(timestamp);
    //TODO: validate
    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()) as FlakinessReport.UnixTimestampMS;
  },

  monthstart(timestamp: FlakinessReport.UnixTimestampMS): FlakinessReport.UnixTimestampMS {
    const date = new Date(timestamp);
    //TODO: validate
    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), 1) as FlakinessReport.UnixTimestampMS;
  },

  yearMonthDayTokens(timestamp: FlakinessReport.UnixTimestampMS): string[] {
    const date = new Date(timestamp);
    return [
      String(date.getUTCFullYear()),
      DateTime.shortMonth(date.getUTCMonth()),
      String(date.getUTCDate()).padStart(2, '0')
    ];
  },

  weekNumber(timestamp: FlakinessReport.UnixTimestampMS) {
    const point = new Date(timestamp);
    // Jan, 4 is always in the first week
    let yearStart = new Date(point.getFullYear(), 0, 4);
    if (yearStart.getDay() !== 1)
      yearStart = new Date(yearStart.getFullYear(), yearStart.getMonth(), yearStart.getDate() - yearStart.getDay() + 1);
    if (point < yearStart) {
      yearStart = new Date(point.getFullYear() - 1, 0, 4);
      if (yearStart.getDay() !== 1)
        yearStart = new Date(yearStart.getFullYear(), yearStart.getMonth(), yearStart.getDate() - yearStart.getDay() + 1);
    }
    return { year: yearStart.getFullYear(), week: (Math.floor(((+point) - (+yearStart)) / ms('7 days'))) + 1 + 1 };
  },

  getUTCTokens(timestamp: number) {
    const date = new Date(timestamp);
    const dayOfMonth_1based = date.getUTCDate();
    const weekOfMonth_0based = Math.floor((dayOfMonth_1based - 1) / 7);
    return {
      year: date.getUTCFullYear(),
      month_0based: date.getUTCMonth(),
      weekOfMonth_0based,
      dayOfMonth_1based,
    };
  },

  shortMonth(month_0based: number): string {
    return utcMonthToAbbr[month_0based];
  },

  fromShortMonth(shortMonth: string): number {
    return utcMonthToAbbr.indexOf(shortMonth.toLowerCase());
  },

  daysOfWeek(timestamp: number): number[] {
    const dateParts = DateTime.getUTCTokens(timestamp);
    const lastDay_1based = (new Date(Date.UTC(dateParts.year, dateParts.month_0based + 1, 0))).getUTCDate();
    const fromDay_1based = dateParts.weekOfMonth_0based * 7 + 1;
    const toDay_1based = Math.min(dateParts.weekOfMonth_0based * 7 + 7, lastDay_1based);
    const result: number[] = [];
    for (let day_1based = fromDay_1based; day_1based <= toDay_1based; ++day_1based)
      result.push(Date.UTC(dateParts.year, dateParts.month_0based, day_1based));
    return result;
  },

  weeksOfMonth(timestamp: number): number[] {
    const dateParts = DateTime.getUTCTokens(timestamp);
    const lastDay_0based = (new Date(Date.UTC(dateParts.year, dateParts.month_0based + 1, 0))).getUTCDate() - 1;
    const result: number[] = [];
    for (let day_0based = 0; day_0based <= lastDay_0based; day_0based += 7)
      result.push(Date.UTC(dateParts.year, dateParts.month_0based, day_0based + 1));
    return result;
  },
}

