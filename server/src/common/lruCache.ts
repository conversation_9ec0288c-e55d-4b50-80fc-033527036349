function createAbortPromise(signal: AbortSignal): { promise: Promise<void>, dispose: () => void } {
  if (signal.aborted)
    return { promise: Promise.resolve(), dispose: () => {} };
  let abortListener: (this: AbortSignal, ev: Event) => any;
  const promise = new Promise<void>(resolve => {
    abortListener = () => resolve();
    signal.addEventListener('abort', abortListener, { once: true });
  });
  return {
    promise,
    dispose: () => signal.removeEventListener('abort', abortListener),
  }
}

/**
 * A simple class to represent the value fetching process.
 * The process can be "invalidated" - meaning that the value
 * it'll fetch will be stale.
 * 
 * This class provides the `value()` promise that never throws:
 * it's either the value returned by the fetch function
 * or `undefined` if the fetch function threw an error.
 */
class BackgroundFetcher<OUTPUT> {
  private _valuePromise: Promise<OUTPUT|undefined>;
  private _invalidated: boolean = false;

  /**
   * @param fetch a fetching method
   * @param signal a signal to abort fetching operation
   * @param log stderr logger (if any)
   */
  constructor(
    fetch: (signal: AbortSignal) => Promise<OUTPUT|undefined>,
    signal: AbortSignal,
    log: (reason?: any) => void,
  ) {
    this._valuePromise = Promise.resolve().then(async () => {
      if (signal.aborted)
        return undefined;
      const abortPromise = createAbortPromise(signal);
      const value = await Promise.race([
        fetch(signal),
        abortPromise.promise.then(() => undefined),
      ]).catch(e => {
        // Log error only if we were not aborted.
        if (!signal.aborted)
          log(e);
        return undefined;
      });
      abortPromise.dispose();
      return signal.aborted ? undefined : value;
    });
  }

  /**
   * Marks this fetching process as invalidated.
   */
  markAsInvalidated() {
    this._invalidated = true;
  }

  invalidated() {
    return this._invalidated;
  }

  /**
   * Returns the value promise that never throws.
   */
  value(): Promise<OUTPUT|undefined> {
    return this._valuePromise;
  }
}

/**
 * Cache entry invariant:
 * - `get()` and `invalidate()` could be called multiple times, in any order
 * - `dispose()` can be called only once; no other calls could be done to the entry after the `dispose()` call.
 */
interface CacheEntry<OUTPUT> {
  get(): Promise<OUTPUT|undefined>;
  invalidate(): void;
  dispose(): void;
}

/**
 * Stale-while-revalidate entry implementation.
 * - `get()` will return an entry if it has any. If the value is stale, it'll
 *   fetch a new one (and will keep fetching until it gets a non-stale one).
 * - `invalidate()` will mark the current or being-fetched value as stale.
 */
class StaleWhileRevalidateCacheEntry<OUTPUT> implements CacheEntry<OUTPUT> {
  private _valueDeadline = 0;
  private _value: OUTPUT|undefined;

  // On-going background fetch.
  private _backgroundFetcher?: BackgroundFetcher<OUTPUT>;
  // Dispose controller for the entry.
  private _disposeController = new AbortController();

  constructor(private _options: {
    ttl: number,
    currentTime: () => number,
    log: (error?: any) => void,
    compute: (stale: OUTPUT|undefined, signal: AbortSignal) => Promise<OUTPUT|undefined>,
  }) {
  }

  private _ensureBackgroundFetcher(): BackgroundFetcher<OUTPUT> {
    if (!this._backgroundFetcher) {
      const bf = new BackgroundFetcher(
        (signal: AbortSignal) => this._options.compute(this._value, signal),
        this._disposeController.signal,
        this._options.log,
      );
      this._backgroundFetcher = bf;
      // When the fetch concludes, we set our value and value's deadline.
      bf.value().then(value => {
        // Aborted? Do nothing.
        if (this._disposeController.signal.aborted)
          return;
        this._backgroundFetcher = undefined;
        this._value = value;
        this._valueDeadline = bf.invalidated() ? 0 : this._options.currentTime() + this._options.ttl;
        // If the fetch was invalidated, schedule another one right away.
        if (bf.invalidated())
          this._ensureBackgroundFetcher();
      });
    }
    return this._backgroundFetcher;
  }

  get(): Promise<OUTPUT|undefined> {
    // If there's a value and it's up-to-date, then returns right away.
    if (this._value && this._options.currentTime() < this._valueDeadline)
      return Promise.resolve(this._value);

    // Otherwise, ensure a background fetch for a new value is running.
    const bgFetcher = this._ensureBackgroundFetcher();
    // Return either a cached stale value, or the value from the background fetcher.
    return this._value ? Promise.resolve(this._value) : bgFetcher.value();
  }

  invalidate() {
    // If there's a running background fetcher, then mark it as invalidated.
    // Otherwise, kick off a new fetch.
    if (this._backgroundFetcher)
      this._backgroundFetcher.markAsInvalidated();
    else
      this._ensureBackgroundFetcher();
  }

  dispose() {
    this._disposeController.abort();
  }
}

/**
 * Always-up-to-date entry implementation.
 * If the entry is invalidated during fetching, then a new entry will be fetched.
 */
class AlwaysUpToDateCacheEntry<OUTPUT> implements CacheEntry<OUTPUT> {
  private _valueDeadline = 0;
  private _value: OUTPUT|undefined;
  private _valuePromise: Promise<OUTPUT|undefined>|undefined;

  // On-going background fetch.
  private _backgroundFetcher?: BackgroundFetcher<OUTPUT>;
  // A fetch-specific abort controller to cancel fetches if they
  // were invalidated.
  private _bgFetchController?: AbortController;
  private _disposeController = new AbortController();

  constructor(private _options: {
    ttl: number,
    currentTime: () => number,
    log: (error?: any) => void,
    compute: (stale: OUTPUT|undefined, signal: AbortSignal) => Promise<OUTPUT|undefined>,
  }) {
  }

  private _ensureValuePromise() {
    if (!this._valuePromise) {
      this._valuePromise = Promise.resolve().then(async () => {
        let newValue: OUTPUT|undefined;
        do {
          this._bgFetchController = new AbortController();
          this._backgroundFetcher = new BackgroundFetcher(
            (signal: AbortSignal) => this._options.compute(this._value, signal),
            AbortSignal.any([this._bgFetchController.signal, this._disposeController.signal]),
            this._options.log,
          );
          newValue = await this._backgroundFetcher.value();
          // Keep fetching values while they've been invalidated.
        } while (!this._disposeController.signal.aborted && this._backgroundFetcher.invalidated());
        this._backgroundFetcher = undefined;
        this._bgFetchController = undefined;

        this._value = newValue;
        this._valueDeadline = this._options.currentTime() + this._options.ttl;

        this._valuePromise = undefined;
        return this._value;
      });
    }
    return this._valuePromise;
  }

  get(): Promise<OUTPUT|undefined> {
    if (this._value && this._options.currentTime() < this._valueDeadline)
      return Promise.resolve(this._value);
    return this._ensureValuePromise();
  }

  invalidate() {
    // If there's a value - make it stale.
    this._valueDeadline = -Infinity;
    // If there's a background fetcher & value promise - mark it as invalidated.
    this._backgroundFetcher?.markAsInvalidated();
    // And also - abort fetching value
    this._bgFetchController?.abort();
    // Kick off value fetching.
    this._ensureValuePromise();
  }

  dispose() {
    this._disposeController.abort();
  }
}

type FetchCallback<INPUT, OUTPUT> = (input: INPUT, cacheKey: string, stale: OUTPUT|undefined, signal: AbortSignal) => Promise<OUTPUT|undefined>;

export class LRUCache<INPUT, OUTPUT> {
  private _cached: Map<string, CacheEntry<OUTPUT>> = new Map();

  constructor(private _options: {
    max: number,
    ttl: number,
    allowStale: boolean,
    currentTime?: () => number,
    log?: (text: string) => void,
    key: (input: INPUT) => string,
    compute: FetchCallback<INPUT, OUTPUT>,
  }) {
  }

  // Make sure "get" is not async to ensure that
  // entry creation happens only once.
  get(key: INPUT): Promise<OUTPUT|undefined> {
    const cacheKey = this._options.key(key);
    let cacheEntry = this._cached.get(cacheKey);
    if (!cacheEntry) {
      cacheEntry = this._options.allowStale ? new StaleWhileRevalidateCacheEntry({
        compute: (stale, signal) => this._options.compute(key, cacheKey, stale, signal),
        currentTime: this._options.currentTime ?? (() => Date.now()),
        log: this._options.log ?? console.error.bind(console),
        ttl: this._options.ttl,
      }) : new AlwaysUpToDateCacheEntry({
        compute: (stale, signal) => this._options.compute(key, cacheKey, stale, signal),
        currentTime: this._options.currentTime ?? (() => Date.now()),
        log: this._options.log ?? console.error.bind(console),
        ttl: this._options.ttl,
      });
      this._cached.set(cacheKey, cacheEntry);
      this._gc();
    } else {
      // Since maps in EcmaScript preserve insertion order,
      // we will (ab)use them to update the order.
      this._cached.delete(cacheKey);
      this._cached.set(cacheKey, cacheEntry);
    }
    return cacheEntry.get();
  }

  invalidate(key: INPUT) {
    const cacheKey = this._options.key(key);
    const cacheEntry = this._cached.get(cacheKey);
    cacheEntry?.invalidate();
  }

  private _gc() {
    // we rely on the insertion order of the cached entries. NOTE that
    // we update the order on every access to the cached entry,
    // so this order is basically the LRU order.
    for (const [cacheKey, cacheEntry] of this._cached) {
      if (this._cached.size <= this._options.max)
        break;
      cacheEntry.dispose();
      this._cached.delete(cacheKey);
    }
  }

  refetchIfExists(input: INPUT) {
    if (this.has(input)) {
      this.delete(input);
      this.get(input);
    }
  }

  delete(key: INPUT) {
    const cacheKey = this._options.key(key);
    const entry = this._cached.get(cacheKey);
    entry?.dispose();
    this._cached.delete(cacheKey);
  }

  clear() {
    for (const value of this._cached.values())
      value.dispose();
    this._cached.clear();
  }

  has(input: INPUT) {
    const key = this._options.key(input);
    return this._cached.has(key);
  }
}