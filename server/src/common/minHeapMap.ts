
//           0
//     1           2
//  3    4      5      6 
// 7 8  9 10  11 12  13 14
function parent(idx: number) {
  return ((idx - 1) / 2)|0;
}

function children(idx: number): [number, number] {
  const left = idx * 2 + 1;
  const right = idx * 2 + 2;
  return [left, right];
}

export class MinHeapMap<VALUE> {
  private _heap: {
    key: number,
    value: VALUE,
  }[] = [];

  private _indexes: Map<VALUE, number> = new Map();

  private _up(idx: number) {
    while (idx > 0) {
      const parentIdx = parent(idx);
      if (this._heap[parentIdx].key <= this._heap[idx].key)
        break;
      const tmp = this._heap[idx];
      this._heap[idx] = this._heap[parentIdx];
      this._heap[parentIdx] = tmp;

      this._indexes.set(this._heap[idx].value, idx);
      this._indexes.set(this._heap[parentIdx].value, parentIdx);

      idx = parentIdx;
    }
  }

  private _down(idx: number) {
    const N = this._heap.length;
    while (true) {
      const [leftIdx, rightIdx] = children(idx);
      let smallestIdx = idx;
      if (leftIdx < N && this._heap[leftIdx].key < this._heap[smallestIdx].key)
        smallestIdx = leftIdx;
      if (rightIdx < N && this._heap[rightIdx].key < this._heap[smallestIdx].key)
        smallestIdx = rightIdx;
      if (smallestIdx === idx)
        break;
      const tmp = this._heap[idx];
      this._heap[idx] = this._heap[smallestIdx];
      this._heap[smallestIdx] = tmp;

      this._indexes.set(this._heap[idx].value, idx);
      this._indexes.set(this._heap[smallestIdx].value, smallestIdx);

      idx = smallestIdx;
    }
  }

  set(value: VALUE, key: number) {
    const index = this._indexes.get(value);
    if (index === undefined) {
      const e = { key, value };
      this._heap.push(e);
      this._indexes.set(value, this._heap.length - 1);
      this._up(this._heap.length - 1);
    } else {
      const oldKey = this._heap[index].key;
      this._heap[index].key = key;
      if (key < oldKey)
        this._up(index);
      else
        this._down(index);
    }
  }

  delete(value: VALUE) {
    const index = this._indexes.get(value);
    if (index === undefined)
      return;
    this._indexes.delete(value);

    const last = this._heap.pop()!;
    if (last.value === value)
      return;

    const oldKey = this._heap[index].key;

    this._heap[index] = last;
    this._indexes.set(last.value, index);

    if (last.key < oldKey)
      this._up(index);
    else
      this._down(index);
  }

  min(): VALUE|undefined {
    return this._heap.length ? this._heap[0].value : undefined;
  }
}