/*
import { FlakinessReport } from '@flakiness/report';
import { expect, test } from '@playwright/test';
import { randomUUID } from 'crypto';
import { Query } from '../src/common/fql/query.js';
import { Stats } from '../src/common/stats/stats.js';
import { StatsBuilder } from '../src/common/stats/statsBuilder.js';
import { StatsFilter } from '../src/common/stats/statsFilter.js';

//TODO: re-write these tests using the ReportBuilder.



test('commit stats: DIFF reports, SAME envs: pass/fail = flaky', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A'],
  });
  const commitId = 'commit-a' as FlakinessReport.CommitId;
  const env = createEnvironment('Ubuntu 22.04 arm64');
  const builder = StatsBuilder.create();
  builder.addReport(createReportWithRuns(commitId, suites, env, {
    'A': 'failed',
  }));
  builder.addReport(createReportWithRuns(commitId, suites, env, {
    'A': 'passed',
  }));
  builder.addReport(createReportWithRuns(commitId, suites, env, {
    'A': 'skipped',
  }));
  const result = StatsFilter.mergeResults([new StatsFilter.StatsFilter(builder.toJSON()).filter([commitId], Query.parse(''))]);
  expect(result.commitResults.length).toBe(1);
  expect(result.commitResults[0].testInstanceOutcomes).toMatchObject({
    expected: 0,
    unexpected: 0,
    flaked: 1,
    skipped: 0,
  });
});

test('commit stats: DIFF reports, DIFF envs: pass/fail = flaky', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A'],
  });
  const commitId = 'commit-a' as FlakinessReport.CommitId;
  const builder = StatsBuilder.create();
  builder.addReport(createReportWithRuns(commitId, suites, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'failed',
  }));
  builder.addReport(createReportWithRuns(commitId, suites, createEnvironment('Windows 10 arm64'), {
    'A': 'passed',
  }));
  const result = StatsFilter.mergeResults([new StatsFilter.StatsFilter(builder.toJSON()).filter([commitId], Query.parse(''))]);
  expect(result.commitResults.length).toBe(1);
  const commitTestStats = result.commitResults[0].testInstanceOutcomes;
  expect(commitTestStats.expected + commitTestStats.unexpected + commitTestStats.flaked + commitTestStats.skipped).toBe(1);
  expect(commitTestStats).toMatchObject({
    expected: 0,
    unexpected: 0,
    flaked: 1,
    skipped: 0,
  });
});

test('report stats: DIFF envs: pass/fail = flake', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A'],
  });
  const commitId = 'commit-a' as FlakinessReport.CommitId;
  const report = createReport(commitId, suites);
  addRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'passed',
  });
  addRun(report, createEnvironment('Windows 10 arm64'), {
    'A': 'failed',
  });
  const builder = StatsBuilder.create();
  builder.addReport(report);
  const result = StatsFilter.mergeResults([new StatsFilter.StatsFilter(builder.toJSON()).filter([commitId], Query.parse(''))]);
  expect(result.commitResults.length).toBe(1);
  expect(result.reportResults.length).toBe(1);
  expect(result.envResults.length).toBe(2);
  const reportTestStats = result.reportResults[0].testInstanceOutcomes;
  expect(reportTestStats.expected + reportTestStats.unexpected + reportTestStats.flaked + reportTestStats.skipped).toBe(1);
  expect(reportTestStats).toMatchObject({
    expected: 0,
    unexpected: 0,
    flaked: 1,
    skipped: 0,
  });
});

test('env commit stats', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A', 'B'],
  });
  const commit1 = 'commit-a' as FlakinessReport.CommitId;
  const commit2 = 'commit-b' as FlakinessReport.CommitId;

  const env = createEnvironment('Ubuntu 22.04 arm64');

  const builder = StatsBuilder.create();
  builder.addReport(createReportWithRuns(commit1, suites, env, {
    'A': 'passed',
  }));
  builder.addReport(createReportWithRuns(commit2, suites, env, {
    'A': 'failed',
  }));
  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter([commit1, commit2], Query.parse(''))]);

  expect(result.commits.length).toBe(2);
  expect(result.envs.length).toBe(1);
  expect(result.commits[0].testStats).toEqual({
    expected: 1,
    unexpected: 1,
    skipped: 0,
    flaked: 0,
  })
});

test('status filtering', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A'],
  });
  const commit1 = 'commit-a' as FlakinessReport.CommitId;

  const env = createEnvironment('Ubuntu 22.04 arm64');

  const builder = StatsBuilder.create();
  const report = createReport(commit1, suites);
  addRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'passed',
  });
  addRun(report, createEnvironment('Ubuntu 22.04 x86_64'), {
    'A': 'failed',
  });
  builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter([commit1], Query.parse('s:flaked'))]);
  expect(result.tests.length).toBe(1);
});

test('status filtering + test filtering', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A', 'B'],
  });
  const commit1 = 'commit-a' as FlakinessReport.CommitId;

  const env = createEnvironment('Ubuntu 22.04 arm64');

  const builder = StatsBuilder.create();
  const report = createReport(commit1, suites);
  addRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'passed',
    'B': 'passed',
  });
  addRun(report, createEnvironment('Ubuntu 22.04 x86_64'), {
    'A': 'failed',
    'B': 'failed',
  });
  builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter([commit1], Query.parse('s:flaked a'))]);
  expect(result.tests.length).toBe(1);
});

test('error filtering', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A', 'B'],
  });
  const commit1 = 'commit-a' as FlakinessReport.CommitId;

  const builder = StatsBuilder.create();
  const report = createReport(commit1, suites);
  addErrorRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'wooo',
    'B': 'undefined is not a null',
  });
  builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter([commit1], Query.parse('$undefined'))]);
  expect(result.tests.length).toBe(1);
  expect(result.tests[0].test.titles[0]).toBe('B');
});


test('error stats', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A', 'B'],
  });
  const commit1 = 'commit-a' as FlakinessReport.CommitId;

  const builder = StatsBuilder.create();
  const report = createReport(commit1, suites);
  addErrorRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'wooo',
    'B': 'undefined is not a null',
  });
  addRun(report, createEnvironment('Ubuntu 22.04 x86_64'), {
    'A': 'passed',
    'C': 'passed',
  });
  builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter([commit1], Query.parse('@x86 C'))]);
  expect(result.commits.length).toBe(1);
  expect(result.commits[0].impactedTests).toBe(0);
  expect(result.commits[0].impactedEnvs).toBe(0);
});

test('should filter based on both error text and test text', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A', 'B'],
  });
  const builder = StatsBuilder.create();

  const commits = ['commit-a' as FlakinessReport.CommitId];

  const report = createReport(commits[0], suites);
  addErrorRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'undefined is not a null',
    'B': 'foo',
  });
  builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter(commits, Query.parse('B $undefined'))]);
  expect(result.commits.length).toBe(0);
});

test('should filter out empty commits and reports', async () => {  
  const suites = createSuitesWithTests({
    'foo.spec.ts': ['A', 'B'],
  });
  const commit1 = 'commit-a' as FlakinessReport.CommitId;

  const env = createEnvironment('Ubuntu 22.04 arm64');

  const builder = StatsBuilder.create();
  const report = createReport(commit1, suites);
  addErrorRun(report, createEnvironment('Ubuntu 22.04 arm64'), {
    'A': 'wooo',
    'B': 'undefined is not a null',
  });
  builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter([commit1], Query.parse('foobarbaz'))]);
  expect(result.tests.length).toBe(0);
  expect(result.envs.length).toBe(0);
  expect(result.errors.length).toBe(0);
  expect(result.commits.length).toBe(0);
});


function createReportWithRuns(commitId: FlakinessReport.CommitId, suites: FlakinessReport.Suite[], env: FlakinessReport.Environment, runs: Record<string, FlakinessReport.TestStatus>) {
  const report = createReport(commitId, suites);
  addRun(report, env, runs);
  return report;
}

function addRun(report: FlakinessReport.Report, env: FlakinessReport.Environment, runs: Record<string, FlakinessReport.TestStatus>) {
  let envIndex = report.environments.findIndex(e => Stats.computeEnvId(e) === Stats.computeEnvId(env));
  if (envIndex === -1)
    envIndex = report.environments.push(env) - 1;
  FlakinessReport.visitTests(report, test => {
    const run = createTestRun(envIndex);
    const status = runs[test.title] ?? 'passed';
    if (status === 'skipped')
      run.outcome = 'skipped';
    else
      run.outcome = run.expectedStatus === status ? 'expected' : 'unexpected';
    run.attempts.push(createRunAttempt(status));
    test.runs.push(run);  
  });
  return report;
}

function addErrorRun(report: FlakinessReport.Report, env: FlakinessReport.Environment, runs: Record<string, string>) {
  let envIndex = report.environments.findIndex(e => Stats.computeEnvId(e) === Stats.computeEnvId(env));
  if (envIndex === -1)
    envIndex = report.environments.push(env) - 1;
  FlakinessReport.visitTests(report, test => {
    if (!runs[test.title])
      return;
    const run = createTestRun(envIndex);
    run.outcome = 'unexpected';
    const attempt = createRunAttempt('failed');
    attempt.errors = [
      {
        message: runs[test.title],
      }
    ]
    run.attempts.push(attempt);
    test.runs.push(run);  
  });
  return report;
}

function createSuitesWithTests(obj: Record<string, string[]>) {
  const suites: FlakinessReport.Suite[] = [];
  for (const [fileName, testNames] of Object.entries(obj)) {
    const tests = testNames.map((testName, index) => createTest(fileName, testName, index + 1));
    const suite = createFileSuite(fileName);
    suite.tests = tests;
    suites.push(suite);
  }
  return suites;
}

function createEnvironment(nameVersionArch: string): FlakinessReport.Environment {
  const [name, version, arch] = nameVersionArch.split(/\s+/);
  return {
    systemData: {
      osName: name,
      osVersion: version,
      osArch: arch,
    },
    userSuppliedData: {},
  };
}

function createLocation(file: string, line?: number, column?: number): FlakinessReport.Location {
  return {
    file: file as FlakinessReport.GitFilePath,
    line: (line ?? 1) as FlakinessReport.Number1Based,
    column: (column ?? 1) as FlakinessReport.Number1Based,
  };
}

function createFileSuite(file: string): FlakinessReport.Suite {
  return {
    type: 'file',
    location: createLocation(file),
    title: file,
    tests: [],
    suites: [],
  };
}

function createTest(file: string, title: string, line: number): FlakinessReport.Test {
  return {
    location: createLocation(file, line),
    title: title,
    runs: [],
    tags: [],
  };
}


function createTestRun(envIdx: number): FlakinessReport.TestRun {
  return {
    annotations: [],
    environmentIdx: envIdx,
    expectedStatus: 'passed',
    outcome: 'expected',
    attempts: [],
    timeout: 10000 as FlakinessReport.DurationMS,
  };
}

function createRunAttempt(status: FlakinessReport.TestStatus): FlakinessReport.RunAttempt {
  return {
    duration: 1000 as FlakinessReport.DurationMS,
    retry: 0,
    startTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
    status,
    workerIndex: 0,
  };
}


function createReport(commitId: FlakinessReport.CommitId, suites: FlakinessReport.Suite[]): FlakinessReport.Report {
  return {
    commitDate: (+new Date('Fri Sep 13 2024 15:27:18 GMT-0700')) as FlakinessReport.UnixTimestampMS,
    commitId: commitId,
    reportId: randomUUID() as FlakinessReport.ReportId,
    environments: [],
    unattributedErrors: [],
    suites: structuredClone(suites),
    startTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
    duration: 1000 as FlakinessReport.DurationMS,
  }
}

*/