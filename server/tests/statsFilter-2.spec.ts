/* 

import { FlakinessReport } from '@flakiness/report';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { sha256Object } from '@flakiness/shared/common/utils.js';
import { expect, test } from '@playwright/test';
import assert from 'assert';
import { randomUUID } from 'crypto';
import { Query } from '../src/common/fql/query.js';
import { StatsBuilder } from '../src/common/stats/statsBuilder.js';
import { StatsFilter } from '../src/common/stats/statsFilter.js';
import { WireTypes } from '../src/common/wireTypes.js';

const commitA = 'commit-A' as FlakinessReport.CommitId;
const commitB = 'commit-B' as FlakinessReport.CommitId;
const commitC = 'commit-C' as FlakinessReport.CommitId;
const commitD = 'commit-D' as FlakinessReport.CommitId;
const envUbuntu: FlakinessReport.Environment = {
  systemData: {
    osName: 'Ubuntu',
    osVersion: '22.04',
    osArch: 'x86_64',
  },
  userSuppliedData: {},
};
const envMacOS: FlakinessReport.Environment = {
  systemData: {
    osName: 'macOS',
    osVersion: 'Sonoma',
    osArch: 'aarch64',
  },
  userSuppliedData: {},
};
const envWindows: FlakinessReport.Environment = {
  systemData: {
    osName: 'Windows',
    osVersion: '10',
    osArch: 'x86_64',
  },
  userSuppliedData: {},
};

test('should filter tests by tag names', { tag: '@regression' }, async () => {
  const commits = [commitA, commitB, commitC, commitD];
  const suites = ReportBuilder.createSuites({
    'foo.spec.ts': [
      'test-A #smoke',
      'test-B',
    ],
  });

  const reports = ReportBuilder.createReports(suites, {
    // This one always passes everywhere
    'test-A': commits.map(commitId => [commitId, { env: envUbuntu }, [{ status: 'passed' }]]),
    'test-B': commits.map(commitId => [commitId, { env: envUbuntu }, [{ status: 'passed' }]]),
  });

  const builder = StatsBuilder.create();
  for (const report of reports)
    builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter(commits, Query.parse('#smoke'))]);
  expect(result.tests.length).toBe(1);
  expect(result.tests[0].test.tags).toEqual(['smoke']);
});

test('make sure s:failed works across commits to select all tests that have non-zero failure rate', async () => {
  const commits = [commitA, commitB, commitC, commitD];
  const suites = ReportBuilder.createSuites({
    'foo.spec.ts': [
      'test-A', // this one always passes everywhere
      'test-B', // this one fails 50%
    ],
  });

  const reports = ReportBuilder.createReports(suites, {
    // This one always passes everywhere
    'test-A': commits.map(commitId => [commitId, { env: envUbuntu }, [{ status: 'passed' }]]),
    // this one fails 50% of times on Windows, works otherwise
    'test-B': commits.map((commitId, idx) =>
      idx % 2 === 0 ?
        [commitId, { env: envUbuntu }, [{ error: 'undefined is not a function' }]] :
        [commitId, { env: envUbuntu }, [{ status: 'passed' }]]
    ),
  });

  const builder = StatsBuilder.create();
  for (const report of reports)
    builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter(commits, Query.parse('s:failed'))]);
  expect(result.tests.length).toBe(1);
});

test('make sure ReportBuilder works as expected', async () => {
  const commits = [commitA, commitB, commitC, commitD];
  const suites = ReportBuilder.createSuites({
    'foo.spec.ts': [
      'test-A', // this one always passes everywhere
      'test-B', // this one fails 50% of times on Windows, flakes 25% on MacOS, passes on linux
      'test-C', // this one flakes 100% of times on Linux only
      'test-D', // this one is just permanently skipped everywhere
    ],
  });

  const reports = ReportBuilder.createReports(suites, {
    // This one always passes everywhere
    'test-A': commits.map(commitId => [
      [commitId, { env: envUbuntu }, [{ status: 'passed' }]],
      [commitId, { env: envMacOS }, [{ status: 'passed' }]],
      [commitId, { env: envWindows }, [{ status: 'passed' }]],
    ]).flat() as TestConfigEntry[],
    // this one fails 50% of times on Windows, works otherwise
    'test-B': commits.map((commitId, idx) => [
      [commitId, { env: envUbuntu }, [{ status: 'passed' }]],
      [commitId, { env: envMacOS }, [{ status: 'passed' }]],
      idx % 2 === 0 ?
        [commitId, { env: envWindows }, [{ error: 'undefined is not a function' }]] :
        [commitId, { env: envWindows }, [{ status: 'passed' }]]
      ,
    ]).flat() as TestConfigEntry[],
    // this one flakes 100% of times on Windows, works otherwise
    'test-C': commits.map((commitId, idx) => [
      [commitId, { env: envUbuntu }, [{ status: 'passed' }]],
      [commitId, { env: envMacOS }, [{ status: 'passed' }]],
      [commitId, { env: envWindows }, [{ status: 'timedOut' }, { status: 'passed' }]],
    ]).flat() as TestConfigEntry[],
    // this one is skipped on Windows, passes everywhere else.
    'test-D': commits.map((commitId, idx) => [
      [commitId, { env: envUbuntu }, [{ status: 'skipped' }]],
      [commitId, { env: envMacOS }, [{ status: 'skipped' }]],
      [commitId, { env: envWindows }, [{ status: 'skipped' }]],
    ]).flat() as TestConfigEntry[],
  });

  const builder = StatsBuilder.create();
  for (const report of reports)
    builder.addReport(report);

  const result = StatsFilter.toWireTypes([new StatsFilter.StatsFilter(builder.toJSON()).filter(commits, Query.parse(''))]);
  expect(result.tests.length).toBe(4);
  expect(result.envs.length).toBe(3);
  expect(result.errors.length).toBe(1);
  expect(result.commits.length).toBe(4);

  // Check test stats
  const testStats: Record<string, WireTypes.Outcomes> = Object.fromEntries(result.tests.map(test => [test.test.titles.at(-1), test.commitsCount]));
  expect(testStats['test-A']).toEqual({
    expected: 4,
    flaked: 0,
    unexpected: 0,
    skipped: 0,
  });
  expect(testStats['test-B']).toEqual({
    expected: 2,
    flaked: 2,
    unexpected: 0,
    skipped: 0,
  });
  expect(testStats['test-C']).toEqual({
    expected: 0,
    flaked: 4,
    unexpected: 0,
    skipped: 0,
  });
  expect(testStats['test-D']).toEqual({
    expected: 0,
    flaked: 0,
    unexpected: 0,
    skipped: 4,
  });
  
  // Check error stats
  expect(result.errors[0].impactedEnvs).toBe(1);
  expect(result.errors[0].impactedTests).toBe(1);
});

type TestConfigEntry = [FlakinessReport.CommitId, RunConfig, RunAttemptConfig[]];
type TestsConfig = Record<string, TestConfigEntry[]>

type RunConfig = {
  env: FlakinessReport.Environment,
  timeout?: FlakinessReport.DurationMS;
  expectedStatus?: FlakinessReport.TestStatus,
  annotations?: FlakinessReport.Annotation[],
};

type RunAttemptConfig = {
  duration?: FlakinessReport.DurationMS,
  status?: FlakinessReport.TestStatus,
  error?: string,
  workerIndex?: number,
}

class ReportBuilder {
  private _report: FlakinessReport.Report;
  private _testRuns = new Multimap<string, FlakinessReport.TestRun>();

  static createReports(suites: FlakinessReport.Suite[], testConfig: TestsConfig): FlakinessReport.Report[] {
    const reportBuilders = new Map<string, ReportBuilder>();
    // We have report per operating system.
    for (const [testName, values] of Object.entries(testConfig)) {
      for (const [ commitId, runConfig, attemptsConfig ] of values) {
        const key = sha256Object({ commitId, env: runConfig.env });
        let builder = reportBuilders.get(key);
        if (!builder) {
          builder = new ReportBuilder(commitId, suites);
          reportBuilders.set(key, builder);
        }
        builder.addTestRun(testName, runConfig, attemptsConfig);
      }
    }
    return [...reportBuilders.values()].map(builder => builder.report());
  }

  static createSuites(suitesConfig: Record<string, string[]>): FlakinessReport.Suite[] {
    const suites: FlakinessReport.Suite[] = [];
    for (const [fileName, testNames] of Object.entries(suitesConfig)) {
      const tests: FlakinessReport.Test[] = testNames.map((testName, index) => {
        const tokens = testName.split(' ');
        const tags: string[] = tokens.filter(token => token.startsWith('#')).map(token => token.substring(1));
        const title = tokens.filter(token => !token.startsWith('#')).join(' ');
        return {
          location: createLocation(fileName, index + 1),
          title: title,
          runs: [],
          tags: tags,
        };
      });
      const suite: FlakinessReport.Suite = {
        type: 'file',
        location: createLocation(fileName),
        title: fileName,
        tests: [],
        suites: [],
      };
      suite.tests = tests;
      suites.push(suite);
    }
    return suites;
  }

  constructor(commitId: FlakinessReport.CommitId, suites: FlakinessReport.Suite[]) {
    this._report = {
      commitDate: Date.now() as FlakinessReport.UnixTimestampMS,
      commitId: commitId,
      reportId: randomUUID() as FlakinessReport.ReportId,
      environments: [],
      unattributedErrors: [],
      suites,
      startTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
      duration: 0  as FlakinessReport.DurationMS,
    };
  }

  addTestRun(testName: string, runConfig: RunConfig, attemptConfigs: RunAttemptConfig[]) {
    const envIdx = this._report.environments.push(runConfig.env) - 1;
    const runTimeout = runConfig.timeout ?? 30000 as FlakinessReport.DurationMS;
    const attempts: FlakinessReport.RunAttempt[] = attemptConfigs.map((attemptConfig, idx) => ({
      retry: idx,
      duration: attemptConfig.status === 'timedOut' ? runTimeout : (attemptConfig.duration ?? 1000 as FlakinessReport.DurationMS),
      errors: attemptConfig.error ? [{ message: attemptConfig.error }] : undefined,
      startTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
      status: attemptConfig.error && !attemptConfig.status ? 'failed' : (attemptConfig.status ?? 'passed'),
      workerIndex: attemptConfig.workerIndex ?? 0,
    }));
    assert(attempts.length > 0);

    let expectedStatus = runConfig.expectedStatus ?? 'passed';
    let outcome: FlakinessReport.TestOutcome;
    if (attempts.length === 1 && attempts[0].status === 'skipped') {
      outcome = 'skipped';
      expectedStatus = 'skipped';
    } else if (attempts.length === 1 && attempts[0].status === expectedStatus) {
      outcome = 'expected';
    } else if (attempts.some(attempt => attempt.status === expectedStatus) && attempts.some(attempt => attempt.status !== expectedStatus)) {
      outcome = 'flaky';
    } else {
      outcome = 'unexpected';
    }

    this._testRuns.set(testName, {
      annotations: runConfig.annotations ?? [],
      environmentIdx: envIdx,
      expectedStatus,
      outcome,
      attempts,
      timeout: runTimeout,
    } as FlakinessReport.TestRun);
    return this;
  }

  report() {
    const dupe = structuredClone(this._report);
    FlakinessReport.visitTests(dupe, test => {
      test.runs = this._testRuns.getAll(test.title);
    });
    // console.log(JSON.stringify(dupe, null, 2));
    const result = FlakinessReport.dedupeSuitesTestsEnvironments(dupe);
    // console.log(JSON.stringify(result, null, 2));
    return result;
  }
}

function createLocation(file: string, line?: number, column?: number): FlakinessReport.Location {
  return {
    file: file as FlakinessReport.GitFilePath,
    line: (line ?? 1) as FlakinessReport.Number1Based,
    column: (column ?? 1) as FlakinessReport.Number1Based,
  };
}

*/