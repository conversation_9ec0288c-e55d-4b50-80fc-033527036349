import { expect, test } from '@playwright/test';
import { Ranges } from '../src/common/ranges.js';

test('Ranges.intersect', async () => {
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3, 5,6,7,8,9, 12]), 
    Ranges.fromList([  2,3,     7        ])
  ))).toBe(`[ 2-3, 7 ]`);
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,  5, 12]), 
    Ranges.fromList([  2      ])
  ))).toBe(`[]`);
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4      ]), 
    Ranges.fromList([      4,5,6,7])
  ))).toBe(`[ 4 ]`);
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4,    7,8,9,10    ]), 
    Ranges.fromList([    3,4,5,6,7,8,9,   11 ])
  ))).toBe(`[ 3-4, 7-9 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4,5,6,7,8,9,10,11,12,]), 
    Ranges.fromList([  2,  4,5,6,  8,  10,   12,])
  ))).toBe(`[ 2, 4-6, 8, 10, 12 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4,5,6,7,8,9,10,11,12,]), 
    Ranges.EMPTY,
  ))).toBe(`[]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,  5,  8]),
    Ranges.complement(Ranges.EMPTY),
  ))).toBe(`[ 1-2, 5, 8 ]`);
});

test('Ranges.union', async () => {
  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,3,     ]), 
    Ranges.fromList([      4,5,6])
  ))).toBe(`[ 1-6 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,  3,  5, ]), 
    Ranges.fromList([  2,  4,  6]), 
  ))).toBe(`[ 1-6 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,     7,8]), 
    Ranges.fromList([      5     ]), 
  ))).toBe(`[ 1-2, 5, 7-8 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,       7,8]), 
    Ranges.fromList([    3,4,5,]), 
  ))).toBe(`[ 1-5, 7-8 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,       7,8]), 
    Ranges.fromList([    ]), 
  ))).toBe(`[ 1-2, 7-8 ]`);
});

test('Ranges.iterate', async () => {
  expect.soft([...Ranges.iterate(Ranges.fromList([-1,2,3]))]).toEqual([-1,2,3]);
  expect.soft([...Ranges.iterate(Ranges.fromList([]))]).toEqual([]);
  expect.soft([...Ranges.iterate(Ranges.fromList([0]))]).toEqual([0]);
});

test('Ranges.pop', async () => {
  {
    const r = Ranges.fromList([-1,-2,-3,3,4,5]);
    expect(Ranges.popInplace(r)).toBe(5);
    expect(Ranges.popInplace(r)).toBe(4);
    expect(Ranges.popInplace(r)).toBe(3);
    expect(Ranges.popInplace(r)).toBe(-1);
    expect(Ranges.popInplace(r)).toBe(-2);
    expect(Ranges.popInplace(r)).toBe(-3);
    expect(Ranges.popInplace(r)).toBe(undefined);
    expect(Ranges.popInplace(r)).toBe(undefined);
  }
  {
    const r = Ranges.fromList([1]);
    expect(Ranges.popInplace(r)).toBe(1);
    expect(Ranges.popInplace(r)).toBe(undefined);
    expect(Ranges.popInplace(r)).toBe(undefined);
  }
  {
    const r = Ranges.fromList([]);
    expect(Ranges.popInplace(r)).toBe(undefined);
    expect(Ranges.popInplace(r)).toBe(undefined);
  }
});

test('Ranges.subtract', async () => {
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1,2,3,4,5,6]), 
    Ranges.fromList([  2,  4,5, ])
  ))).toBe(`[ 1, 3, 6 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1          ]), 
    Ranges.fromList([  2,  4,5, ])
  ))).toBe(`[ 1 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1]), 
    Ranges.fromList([1, 2,  4,5, ])
  ))).toBe(`[]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1,2,3,4,5  ]), 
    Ranges.fromList([1,2,  4,5, ])
  ))).toBe(`[ 3 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1,2,3,4,5      ]), 
    Ranges.fromList([      4,5,6,7,8])
  ))).toBe(`[ 1-3 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([ ]), 
    Ranges.fromList([      4])
  ))).toBe(`[]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([3]), 
    Ranges.fromList([])
  ))).toBe(`[ 3 ]`);
});

test('Ranges.cardinality', async () => {
  expect.soft(Ranges.cardinality(
    Ranges.fromList([1,2,3,4,5,6]), 
  )).toBe(6);
  expect.soft(Ranges.cardinality(
    Ranges.fromList([-1,0,1,2,3,   5,6, 100]), 
  )).toBe(8);
});

test('Ranges.toSortedList', async () => {
  expect.soft(Ranges.toSortedList(
    [1,1,5,8] as Ranges.Ranges<number>
  )).toEqual([1,5,6,7,8]);

  expect.soft(() => Ranges.toSortedList(Ranges.FULL)).toThrowError('cannot convert infinite ranges!');
});

test('Ranges.fromSortedList', async () => {
  expect.soft(Ranges.fromSortedList(
    [1,1,1,1,1,1]
  )).toEqual([1,1]);
  expect.soft(Ranges.fromSortedList(
    [1,1,5,6,7,8]
  )).toEqual([1,1,5,8]);
});

test('Ranges.fromList', async () => {
  expect.soft(Ranges.fromList(
    [1,1,1,1,1,1]
  )).toEqual([1,1]);
  expect.soft(Ranges.fromList(
    [8,1,5,6,1,7]
  )).toEqual([1,1,5,8]);
});

test('Ranges.isInfinite', async () => {
  expect.soft(Ranges.isInfinite(Ranges.FULL)).toBe(true);
  expect.soft(Ranges.isInfinite(Ranges.EMPTY)).toBe(false);
  expect.soft(Ranges.isInfinite(Ranges.fromList([1,2,3]))).toBe(false);
  expect.soft(Ranges.isInfinite(Ranges.complement(Ranges.fromList([1,2,3])))).toBe(true);
});

test('Ranges.contains', async () => {
  // Empty
  expect.soft(Ranges.contains(Ranges.EMPTY, -Infinity)).toBe(false);
  expect.soft(Ranges.contains(Ranges.EMPTY, 0)).toBe(false);

  // Full
  expect.soft(Ranges.contains(Ranges.FULL, 0)).toBe(true);
  expect.soft(Ranges.contains(Ranges.FULL, Infinity)).toBe(true);
  expect.soft(Ranges.contains(Ranges.FULL, -0)).toBe(true);

  // Half interval
  expect.soft(Ranges.contains([-Infinity, 0] as Ranges.Ranges<number>, 1)).toBe(false);
  expect.soft(Ranges.contains([-Infinity, 0] as Ranges.Ranges<number>, -1)).toBe(true);
  expect.soft(Ranges.contains([-Infinity, 0] as Ranges.Ranges<number>, Infinity)).toBe(false);
  expect.soft(Ranges.contains([-Infinity, 0] as Ranges.Ranges<number>, -Infinity)).toBe(true);

  // Custom ranges
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, 0)).toBe(true);
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, 200)).toBe(true);
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, 201)).toBe(false);
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, -100)).toBe(true);
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, -101)).toBe(false);
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, 101)).toBe(false);
  expect.soft(Ranges.contains([-100, 100, 200, 200] as Ranges.Ranges<number>, 100)).toBe(true);
});

test('Ranges.complement', async () => {
  expect.soft(Ranges.complement(Ranges.FULL)).toEqual(Ranges.EMPTY);
  expect.soft(Ranges.complement(Ranges.EMPTY)).toEqual(Ranges.FULL);

  expect.soft(Ranges.complement([-Infinity, 0] as Ranges.Ranges<number>)).toEqual([1, Infinity]);
  expect.soft(Ranges.complement([-100, 100] as Ranges.Ranges<number>)).toEqual([-Infinity, -101, 101, Infinity]);
  expect.soft(Ranges.complement([0, 0, 10, 20] as Ranges.Ranges<number>)).toEqual([-Infinity, -1, 1, 9, 21, Infinity]);
});

test('Ranges.compress and Ranges.decompress', async () => {
  expect.soft(Ranges.decompress(Ranges.compress(Ranges.fromList([1])))).toEqual([1, 1]);
  expect.soft(Ranges.decompress(Ranges.compress(Ranges.fromList([-2, -1])))).toEqual([-2, -1]);
  expect.soft(Ranges.decompress(Ranges.compress(Ranges.fromList([1, 2, 3, 5, 7,8])))).toEqual([1, 3, 5, 5, 7, 8]);
});


test('Ranges.domain', async () => {
  expect.soft(Ranges.domain(Ranges.fromList([-1, 200, 3, 1]))).toEqual({ min: -1, max: 200, size: 202 });
});

test('Ranges.WeightedSum', async () => {
  expect.soft(new Ranges.WeightedSum<number>()
    .addRange(Ranges.fromList([-1,  0, 1, 2,    4,          9]), 2)
    .addRange(Ranges.fromList([-1,        2, 3, 4, 5, 6      ]), 6)
    .list([-1, 6, 9, 9] as Ranges.Ranges<number>)
  ).toEqual(new Map([
    [-1, { count: 2, sum: 8, }],
    [0, { count: 1, sum: 2, }],
    [1, { count: 1, sum: 2, }],
    [2, { count: 2, sum: 8, }],
    [3, { count: 1, sum: 6, }],
    [4, { count: 2, sum: 8, }],
    [5, { count: 1, sum: 6, }],
    [6, { count: 1, sum: 6, }],
    [9, { count: 1, sum: 2, }],
  ]));

  expect.soft(new Ranges.WeightedSum<number>()
    .addRange(Ranges.fromList([-1,  0, 1, 2,    4,          9]), 1)
    .addRange(Ranges.fromList([-1,        2, 3, 4, 5, 6]), 1)
    .list([-1, 6, 9, 9] as Ranges.Ranges<number>)
  ).toEqual(new Map([
    [-1, { count: 2, sum: 2, }],
    [0, { count: 1, sum: 1, }],
    [1, { count: 1, sum: 1, }],
    [2, { count: 2, sum: 2, }],
    [3, { count: 1, sum: 1, }],
    [4, { count: 2, sum: 2, }],
    [5, { count: 1, sum: 1, }],
    [6, { count: 1, sum: 1, }],
    [9, { count: 1, sum: 1, }],
  ]));

  expect.soft(new Ranges.WeightedSum<number>()
    .addRange(Ranges.fromList([]), 100)
    .addRange(Ranges.fromList([-100]), 100)
    .list([-100, -100] as Ranges.Ranges<number>)
  ).toEqual(new Map([
    [-100, { count: 1, sum: 100 }],
  ]));

  expect.soft(new Ranges.WeightedSum<number>()
    .addRange(Ranges.fromList([]), 1000)
    .list([] as number[] as Ranges.Ranges<number>)
  ).toEqual(new Map([]));
});