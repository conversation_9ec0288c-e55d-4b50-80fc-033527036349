import { expect, test } from '@playwright/test';
import { MinHeapMap } from '../src/common/minHeapMap.js';

test('should initialize empty', () => {
  const heap = new MinHeapMap<string>();
  expect(heap.min()).toBeUndefined();
});

test('should set and retrieve minimum value', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 10);
  heap.set('b', 5);
  heap.set('c', 15);
  
  expect(heap.min()).toBe('b');
});

test('should update existing values', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 10);
  heap.set('b', 5);
  heap.set('c', 15);
  
  // Update 'a' to be the minimum
  heap.set('a', 1);
  expect(heap.min()).toBe('a');
  
  // Update 'b' to be higher
  heap.set('b', 20);
  heap.set('a', 20);
  expect(heap.min()).toBe('c');
});

test('should delete values correctly', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 10);
  heap.set('b', 5);
  heap.set('c', 15);
  
  expect(heap.min()).toBe('b');

  heap.delete('b');
  expect(heap.min()).toBe('a');
  
  heap.delete('a');
  expect(heap.min()).toBe('c');
  
  heap.delete('c');
  expect(heap.min()).toBeUndefined();
});

test('should handle deleting non-existent values', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 10);
  
  // Should not throw
  heap.delete('non-existent');
  expect(heap.min()).toBe('a');
});

test('should maintain heap property with many operations', () => {
  const heap = new MinHeapMap<number>();
  
  // Add values in non-sorted order
  for (let i = 20; i >= 1; i--) {
    heap.set(i, i);
  }
  
  // Min should be 1
  expect(heap.min()).toBe(1);
  
  // Delete minimum
  heap.delete(1);
  expect(heap.min()).toBe(2);
  
  // Update a middle value to be the minimum
  heap.set(15, 0);
  expect(heap.min()).toBe(15);
  
  // Update the minimum to be higher
  heap.set(15, 30);
  expect(heap.min()).toBe(2);
});

test('should handle duplicate keys', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 10);
  heap.set('b', 10);
  heap.set('c', 10);
  
  // Any of these could be the minimum since they have the same key
  const min = heap.min()!;
  expect(['a', 'b', 'c']).toContain(min);

  heap.delete(min);
  const newMin = heap.min();
  console.log(min, newMin);
  expect(['a', 'b', 'c']).toContain(newMin);
  expect(newMin).not.toBe(min);
});

test('should handle negative keys', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 5);
  heap.set('b', -10);
  heap.set('c', 0);
  
  expect(heap.min()).toBe('b');
  
  heap.delete('b');
  expect(heap.min()).toBe('c');
});

test('should handle edge case with one element', () => {
  const heap = new MinHeapMap<string>();
  heap.set('a', 5);
  
  expect(heap.min()).toBe('a');
  
  heap.delete('a');
  expect(heap.min()).toBeUndefined();
  
  heap.set('a', 5);
  expect(heap.min()).toBe('a');
});
