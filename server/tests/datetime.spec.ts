import { expect, test } from '@playwright/test';
import { DateTime } from '../src/common/datetime.js';

const formatter = new Intl.DateTimeFormat('en-US', {
  timeZone: 'UTC',
  year: 'numeric',
  month: 'short',
  day: '2-digit'
});

const format = formatter.format.bind(formatter);

test('weeksOfMonth', async () => {
  const timestamp = Date.UTC(2024, 1); // Feb 01, 2024.
  expect(format(timestamp)).toBe('Feb 01, 2024');
  expect(DateTime.weeksOfMonth(timestamp).map(format)).toEqual([
    'Feb 01, 2024',
    'Feb 08, 2024',
    'Feb 15, 2024',
    'Feb 22, 2024',
    'Feb 29, 2024'
  ]);
});

test('daysOfWeek', async () => {
  const timestamp = Date.UTC(2024, 1); // Feb 01, 2024.
  expect(format(timestamp)).toBe('Feb 01, 2024');
  const weeks = DateTime.weeksOfMonth(timestamp);
  expect(DateTime.daysOfWeek(weeks[0]).map(format)).toEqual([
    'Feb 01, 2024',
    'Feb 02, 2024',
    'Feb 03, 2024',
    'Feb 04, 2024',
    'Feb 05, 2024',
    'Feb 06, 2024',
    'Feb 07, 2024',
  ]);
  expect(DateTime.daysOfWeek(weeks[1]).map(format)).toEqual([
    'Feb 08, 2024',
    'Feb 09, 2024',
    'Feb 10, 2024',
    'Feb 11, 2024',
    'Feb 12, 2024',
    'Feb 13, 2024',
    'Feb 14, 2024',
  ]);
  expect(DateTime.daysOfWeek(weeks[4]).map(format)).toEqual([
    'Feb 29, 2024',
  ]);
});