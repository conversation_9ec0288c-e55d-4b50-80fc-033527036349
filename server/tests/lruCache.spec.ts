import { ManualPromise } from '@flakiness/shared/common/manualPromise.js';
import { expect, test } from '@playwright/test';
import { LRUCache } from '../src/common/lruCache.js';

for (const allowStale of [false, true]) {
  test.describe(allowStale ? 'stale cache' : 'non-stale cache', () => {
    test('should initialize empty', async () => {
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async () => 'computed',
      });

      expect(cache.has('nonexistent')).toBe(false);
    });

    test('should cache values', async () => {
      let computeCount = 0;
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async () => {
          computeCount++;
          return 'computed';
        },
      });
      
      // First call should compute
      expect(await cache.get('key')).toBe('computed');
      expect(computeCount).toBe(1);
      
      // Second call should use cache
      expect(await cache.get('key')).toBe('computed');
      expect(computeCount).toBe(1);
    });

    test('should respect max size and evict least recently used items', async () => {
      let computed = 0;
      const cache = new LRUCache<string, string>({
        max: 3,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async (key) => {
          return `${key}-${++computed}`;
        },
      });
      
      // Fill the cache
      expect(await cache.get('a')).toBe('a-1');
      expect(await cache.get('b')).toBe('b-2');
      expect(await cache.get('c')).toBe('c-3');
      // Least recent -> more recent: a, b, c
      
      // Access 'a' to make it most recently used
      expect(await cache.get('a')).toBe('a-1');
      // Least recent -> more recent: b, c, a
      
      // Add a new item, should evict 'b' (least recently used)
      expect(await cache.get('d')).toBe('d-4');
      expect(cache.has('b')).toBe(false);
      // Least recent -> more recent: c, a, d
        
      expect(await cache.get('a')).toBe('a-1');
      // Least recent -> more recent: c, d, a
      expect(await cache.get('b')).toBe('b-5');
      // Least recent -> more recent: d, a, b
      expect(await cache.get('c')).toBe('c-6');
      // Least recent -> more recent: a, b, c
      expect(await cache.get('d')).toBe('d-7');
      // Least recent -> more recent: b, c, d
    });

    test('should respect TTL and recompute stale values', async () => {
      let computeCount = 0;

      let time = Date.now();
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 100, // Very short TTL
        key: (input) => input,
        allowStale,
        currentTime: () => time,
        compute: async () => {
          computeCount++;
          return `computed-${computeCount}`;
        },
      });
      
      // First call should compute
      expect(await cache.get('key')).toBe('computed-1');
      expect(computeCount).toBe(1);
      
      // Second call within TTL should use cache
      expect(await cache.get('key')).toBe('computed-1');
      expect(computeCount).toBe(1);

      // Wait for TTL to expire
      time += 1000;
      
      // Call after TTL should either return stale or actual value
      expect(await cache.get('key')).toBe(allowStale ? 'computed-1' : 'computed-2');
      // But eventually, it must return the non-stale value.
      await expect.poll(() => cache.get('key')).toBe('computed-2');
    });

    test('should return undefined if the key was deleted during fetch', async () => {
      const computePromises: ManualPromise<string>[] = [];
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 100,
        allowStale,
        key: (input) => input,
        compute: async (key) => {
          const promise = new ManualPromise<string>();
          computePromises.push(promise);
          return promise.promise;
        },
      });

      // Start first computation
      const getPromise = cache.get('key');

      // Wait for the computation to start
      await expect.poll(() => computePromises.length).toBe(1);
      computePromises[0].resolve('computed-1');
      cache.delete('key');
      // Resolve the computation
      expect(await getPromise).toBeUndefined();
    });

    test('should return undefined if the first computation throws', async () => {
      const computePromises: ManualPromise<string>[] = [];
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 100,
        allowStale,
        key: (input) => input,
        compute: async (key) => {
          const promise = new ManualPromise<string>();
          computePromises.push(promise);
          return promise.promise;
        },
      });
    
      // Start first computation
      const getPromise = cache.get('key');
      
      // Wait for the computation to start
      await expect.poll(() => computePromises.length).toBe(1);
      computePromises[0].resolve('computed-1');
      cache.delete('key');
      // Resolve the computation
      expect(await getPromise).toBeUndefined();
    });

    test('cache.invalidate when at rest', async () => {
      let computeCount = 0;
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async () => {
          computeCount++;
          return `computed-${computeCount}`;
        },
      });

      // First call should compute
      expect(await cache.get('key')).toBe('computed-1');
      expect(computeCount).toBe(1);

      // Invalidate the key
      cache.invalidate('key');

      // Next call should recompute
      expect(await cache.get('key')).toBe(allowStale ? 'computed-1' : 'computed-2');
      // Eventually should converge on the latest.
      await expect.poll(() => cache.get('key')).toBe('computed-2');
    });

    test('cache.invalidate when background fetch is running', async () => {
      const computePromises: ManualPromise<string>[] = [];
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000, // Short TTL to trigger revalidation
        allowStale,
        key: (input) => input,
        compute: async (key, cacheKey, stale) => {
          const promise = new ManualPromise<string>();
          computePromises.push(promise);
          return promise.promise;
        },
      });

      // Initial request
      const getPromise = cache.get('key');
      await expect.poll(() => computePromises.length).toBe(1);
      cache.invalidate('key');
      computePromises[0].resolve('value-1');
      await expect.poll(() => computePromises.length).toBe(2);
      computePromises[1].resolve('value-2');

      expect(await getPromise).toBe(allowStale ? 'value-1' : 'value-2');
      await expect.poll(() => cache.get('key')).toBe('value-2');
    });

    test('cache.invalidate when background fetch is running and then rejects', async () => {
      const computePromises: ManualPromise<string>[] = [];
      const logs: string[] = [];
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000, // Short TTL to trigger revalidation
        allowStale,
        log: (e: any) => logs.push(e.message),
        key: (input) => input,
        compute: async (key, cacheKey, stale) => {
          const promise = new ManualPromise<string>();
          computePromises.push(promise);
          return promise.promise;
        },
      });

      // Initial request
      const getPromise = cache.get('key');
      await expect.poll(() => computePromises.length).toBe(1);
      cache.invalidate('key');
      computePromises[0].resolve('value-1');
      await expect.poll(() => computePromises.length).toBe(2);
      computePromises[1].reject(new Error('oops! something went wrong!'));

      expect(await getPromise).toBe(allowStale ? 'value-1' : undefined);
      await expect.poll(() => logs.at(0)).toEqual('oops! something went wrong!');
    });

    test('should handle removal of keys', async () => {
      let computeCount = 0;
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async () => {
          computeCount++;
          return `computed-${computeCount}`;
        },
      });

      // First call should compute
      expect(await cache.get('key')).toBe('computed-1');
      expect(computeCount).toBe(1);

      // delete the key
      cache.delete('key');
      expect(cache.has('key')).toBe(false);
      
      // Next call should recompute
      expect(await cache.get('key')).toBe('computed-2');
      expect(computeCount).toBe(2);
    });

    test('should clear all entries', async () => {
      let computeCount = 0;
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async (key) => {
          computeCount++;
          return `computed-${key}-${computeCount}`;
        },
      });

      // Add multiple entries
      await cache.get('a');
      await cache.get('b');
      await cache.get('c');
      expect(computeCount).toBe(3);
      
      // Clear the cache
      cache.clear();
      
      // All entries should be recomputed
      await cache.get('a');
      await cache.get('b');
      await cache.get('c');
      expect(computeCount).toBe(6);
    });

    test('should handle concurrent requests for the same key', async () => {
      const computePromise = new ManualPromise<string>();
      let computeCount = 0;
      
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async () => {
          computeCount++;
          return computePromise.promise;
        },
      });

      // Start multiple concurrent requests
      const promise1 = cache.get('key');
      const promise2 = cache.get('key');
      const promise3 = cache.get('key');
      
      // Resolve the computation
      computePromise.resolve('computed');
      
      // All promises should resolve to the same value
      expect(await promise1).toBe('computed');
      expect(await promise2).toBe('computed');
      expect(await promise3).toBe('computed');
      // Still only one computation should have occurred
      expect(computeCount).toBe(1);
    });

    test('should handle errors in compute function', async () => {
      let computeCount = 0;
      const cache = new LRUCache<string, string>({
        max: 10,
        ttl: 1000,
        allowStale,
        key: (input) => input,
        compute: async () => {
          computeCount++;
          if (computeCount === 1) {
            throw new Error('Compute failed');
          }
          return 'computed';
        },
      });

      // First call should return undefined
      await expect(await cache.get('key')).toBeUndefined();
    
      // Second call should retry and succeed
      expect(await cache.get('key')).toBe('computed');
      expect(computeCount).toBe(2);
    });
  });
}

