import { FlakinessReport } from '@flakiness/report';
import { expect, test } from '@playwright/test';
import { Query } from '../src/common/fql/query.js';
import { Token } from '../src/common/fql/tokens.js';
import { Stats } from '../src/common/stats/stats.js';
import { WireTypes } from '../src/common/wireTypes.js';

function simplify(token: InstanceType<typeof Token>) {
  return {
    type: token.type,
    value: token.value,
    index: token.from,
  }
}

function tokenize(text: string): Token[] {
  return Token.tokenize(text).tokens;
}

test.describe('Token.tokenize', () => {
  test('from-to indexes are correct', () => {
    expect(tokenize('"1 1"foo@')).toEqual([
      expect.objectContaining({ type: 'word', value: '1 1', from: 0, to: 5 }),
      expect.objectContaining({ type: 'word', value: 'foo', from: 5, to: 8 }),
      expect.objectContaining({ type: 'punctuation', value: '@', from: 8, to: 9 }),
    ]);
  });

  test('escaping', () => {
    expect.soft(tokenize(`'the \\'St. Petersburg\\' city'`).map(simplify)).toEqual([
      { type: 'word', value: `the 'St. Petersburg' city` , index: 0 }
    ]);

    expect.soft(tokenize(`"the \\"Moscow\\" city"`).map(simplify)).toEqual([
      { type: 'word', value: 'the "Moscow" city' , index: 0 }
    ]);
  
    expect.soft(tokenize(`geo:"the \\"Moscow\\" city"`).map(simplify)).toEqual([
      { type: 'word', value: 'geo', index: 0 },
      { type: 'punctuation', value: ':', index: 3 },
      { type: 'word', value: 'the "Moscow" city' , index: 4 }
    ]);

    expect.soft(Token.tokenize(`'bad \\"escape'`).error).toEqual({
      position: 6,
      message: 'bad escape sequence'
    });
  });

  test('general tokenization', async () => {
    expect.soft(tokenize('server/tests/ranges.spec.ts').map(simplify)).toEqual([
      { type: 'word', value: 'server/tests/ranges.spec.ts', index: 0 },
    ])
    expect.soft(tokenize('-foo').map(simplify)).toEqual([
      { type: 'punctuation', value: '-', index: 0 },
      { type: 'word', value: 'foo', index: 1 },
    ]);
    expect.soft(tokenize('-$foo').map(simplify)).toEqual([
      { type: 'punctuation', value: '-', index: 0 },
      { type: 'punctuation', value: '$', index: 1 },
      { type: 'word', value: 'foo', index: 2 },
    ]);
    expect.soft(tokenize('-@foo').map(simplify)).toEqual([
      { type: 'punctuation', value: '-', index: 0 },
      { type: 'punctuation', value: '@', index: 1 },
      { type: 'word', value: 'foo', index: 2 },
    ]);
    expect.soft(tokenize('bar-foo').map(simplify)).toEqual([
      { type: 'word', value: 'bar-foo', index: 0 },
    ]);
    expect.soft(tokenize('b-r -wo').map(simplify)).toEqual([
      { type: 'word', value: 'b-r', index: 0 },
      { type: 'punctuation', value: '-', index: 4 },
      { type: 'word', value: 'wo', index: 5 },
    ]);
    expect.soft(tokenize('(foo @bar)').map(simplify)).toEqual([
      { type: 'punctuation', value: '(', index: 0 },
      { type: 'word', value: 'foo', index: 1 },
      { type: 'punctuation', value: '@', index: 5 },
      { type: 'word', value: 'bar', index: 6 },
      { type: 'punctuation', value: ')', index: 9 },
    ]);

    expect.soft(tokenize('#smoke').map(simplify)).toEqual([
      { type: 'punctuation', value: '#', index: 0 },
      { type: 'word', value: 'smoke', index: 1 },
    ]);
  
    expect.soft(tokenize('foo').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
    ]);
    expect.soft(tokenize('foo.bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo.bar', index: 0 },
    ]);
    expect.soft(tokenize('foo @bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '@', index: 4 },
      { type: 'word', value: 'bar', index: 5 },
    ]);
    expect.soft(tokenize('foo $bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '$', index: 4 },
      { type: 'word', value: 'bar', index: 5 },
    ]);
  
    expect.soft(tokenize('foo=bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 3 },
      { type: 'word', value: 'bar', index: 4 }
    ]);
    expect.soft(tokenize(`foo="wtf dUdE"`).map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 3 },
      { type: 'word', value: 'wtf dUdE' , index: 4 }
    ]);
  
    expect.soft(tokenize(`foo=bar OR baz=bad`).map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 3 },
      { type: 'word', value: 'bar', index: 4 },
      { type: 'word', value: 'OR', index: 8 },
      { type: 'word', value: 'baz', index: 11 },
      { type: 'punctuation', value: '=', index: 14 },
      { type: 'word', value: 'bad', index: 15 }
    ]);
    expect.soft(tokenize(`@E)!OR __F`).map(simplify)).toEqual([
      { type: 'punctuation', value: '@', index: 0 },
      { type: 'word', value: 'E', index: 1 },
      { type: 'punctuation', value: ')', index: 2 },
      { type: 'punctuation', value: '!', index: 3 },
      { type: 'word', value: 'OR', index: 4 },
      { type: 'word', value: '__F', index: 7 }
    ]);
    expect.soft(tokenize(`foo = bar OR foo ≠ (a, b, 'c d')`).map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 4 },
      { type: 'word', value: 'bar', index: 6 },
      { type: 'word', value: 'OR', index: 10 },
      { type: 'word', value: 'foo', index: 13 },
      { type: 'punctuation', value: '≠', index: 17 },
      { type: 'punctuation', value: '(', index: 19 },
      { type: 'word', value: 'a', index: 20 },
      { type: 'punctuation', value: ',', index: 21 },
      { type: 'word', value: 'b', index: 23 },
      { type: 'punctuation', value: ',', index: 24 },
      { type: 'word', index: 26, value: 'c d' },
      { type: 'punctuation', value: ')', index: 31 }
    ]);
  });
});

test('Query.serialize', async () => {
  expect.soft(Query.parse(`
    y   x  z)
  `).serialize()).toBe('y x z');
  
  expect.soft(Query.parse(`#smoke`).serialize()).toBe('#smoke');

  expect.soft(Query.parse(`
    @zzz≠qQq @aAA!=1
  `).serialize()).toBe('@zzz≠qQq @aAA!=1');
  expect.soft(Query.parse(`
    zzz @aAA!=  (e,D  ,a  )
  `).serialize()).toBe('zzz @aAA!=(e, D, a)');
  expect.soft(Query.parse(`
    @foo ≠ (bar) @foo ≠ baz
  `).serialize()).toBe('@foo≠bar @foo≠baz');
  expect.soft(Query.parse(`
    @foo ≠ (bar) @foo = baz
  `).serialize()).toBe('@foo≠bar @foo=baz');
  expect.soft(Query.parse(`
    @foo ≠ (bar) @foo = baz @foo = q
  `).serialize()).toBe('@foo≠bar @foo=baz @foo=q');
  expect.soft(Query.parse(`
    s:failed s:flaked
  `).serialize()).toBe('s:failed s:flaked');

  //NOTE: serialization DOES NOT RETAIN unparsed tokens!
  expect.soft(Query.parse(`
    @@@@click
  `).serialize()).toBe('@click');
  expect.soft(Query.parse(`
    @@@@click @env
  `).serialize()).toBe('@click @env');
});

test('Query.normalize', async () => {
  expect.soft(Query.parse(`
    y   x  z
  `).normalize().serialize()).toBe('x y z');
  expect.soft(Query.parse(`
    @zzz≠qQq @aAA!=1
  `).normalize().serialize()).toBe('@aAA≠1 @zzz≠qqq');
  expect.soft(Query.parse(`
    zzz @aAA!=  (e,D,"A"  )
  `).normalize().serialize()).toBe('zzz @aAA≠(a, d, e)');
  expect.soft(Query.parse(`
    @foo ≠ (bar) @foo ≠ baz
  `).normalize().serialize()).toBe('@foo≠(bar, baz)');
  expect.soft(Query.parse(`
    @foo ≠ (bar) @foo ≠ baz
  `).normalize().serialize()).toBe('@foo≠(bar, baz)');
  expect.soft(Query.parse(`
    @foo ≠ 1 @foo = (1, 2, 3)
  `).normalize().serialize()).toBe('@foo:(2, 3)');
  expect.soft(Query.parse(`
    @foo ≠ (bar) @foo = baz @foo = q
  `).normalize().serialize()).toBe('@foo:(baz, q)');
  expect.soft(Query.parse(`
    @foo ≠ 1 @foo = (1, 2, 3)
  `).normalize().serialize()).toBe('@foo:(2, 3)');
  expect.soft(Query.parse(`
    @x = (1,2,3) @x = (1,3,4,5)
  `).normalize().serialize()).toBe('@x:(1, 2, 3, 4, 5)');
  expect.soft(Query.parse(`
    status = (passed, missed)
  `).normalize().serialize()).toBe('status:(missed, passed)');
  expect.soft(Query.parse(`
    s:failed s:flaked s:fire
  `).normalize().serialize()).toBe('status:(failed, fire, flaked)');
});

test('Query.toggleQuery', async () => {
  expect(Query.parse('').toggleQuery(Query.parse('@foo:bar')).serialize()).toBe('@foo:bar');
  expect(Query.parse('@foo=bar').toggleQuery(Query.parse('@foo:bar')).serialize()).toBe('');
  expect(Query.parse('@foo=(bar,baz)').toggleQuery(Query.parse('@foo:(foo,bar)')).serialize()).toBe('@foo=baz @foo:foo');
  expect(Query.parse('@foo=(foo, bar, baz)').toggleQuery(Query.parse('@foo:bar')).serialize()).toBe('@foo=(foo, baz)');
  expect(Query.parse('$"undefined@foo"').toggleQuery(Query.parse('@foo:bar')).serialize()).toBe('$"undefined@foo" @foo:bar');
});

test('Query.acceptsTest', async () => {
  const test: WireTypes.Test = {
    filePath: 'tests/library/capabilities.spec.ts' as FlakinessReport.GitFilePath,
    lineNumber: 67 as FlakinessReport.Number1Based,
    testId: 'ff' as Stats.TestId,
    titles: [`should play video @smoke`],
    tags: ['ReGrEsSiOn'],
    lastExecutionCommitId: '' as FlakinessReport.CommitId,
  };
  expect.soft(Query.parse(``).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    (page-click @smoke)
  `).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`#regression`).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`#regre`).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`#smoke`).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`
    should
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    play should
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    "play should"
  `).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`
    "should play"
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    -"should play"
  `).acceptsTest(test)).toBe(false);
});

test('Query.acceptsEnvironment', async () => {
  const env: WireTypes.RunEnvironment = {
    systemData: {
      osArch: 'arm',
      osName: 'Ubuntu',
      osVersion: 'focal',
    },
    envId: 'ff' as Stats.EnvId,
  };
  expect.soft(Query.parse(`foo @os.name= ubuntu`).acceptsEnvironment(env)).toBe(true);
  expect.soft(Query.parse(``).acceptsEnvironment(env)).toBe(true);
  expect.soft(Query.parse(`@os.arch:arm`).acceptsEnvironment(env)).toBe(true);
  expect.soft(Query.parse(`should @os.arch:x86_64 parse`).acceptsEnvironment(env)).toBe(false);
  expect.soft(Query.parse(`@windows`).acceptsEnvironment(env)).toBe(false);
  expect.soft(Query.parse(`@ubuntu`).acceptsEnvironment(env)).toBe(true);
  expect.soft(Query.parse(`-@ubuntu`).acceptsEnvironment(env)).toBe(false);
});

test('Query.acceptsError', async () => {
  const error: WireTypes.TestError = {
    errorId: 'fff' as Stats.ErrorId,
    message: 'undefined is not a function'
  };
  expect.soft(Query.parse(``).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$jjj`).acceptsError(error)).toBe(false);
  expect.soft(Query.parse(`$undefined`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$undefined $foo`).acceptsError(error)).toBe(false);
  expect.soft(Query.parse(`$is $undefined`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$text:'undefined is not a function'`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$text:'undefined is not a'`).acceptsError(error)).toBe(false);
  expect.soft(Query.parse(`-$text:'undefined is not a'`).acceptsError(error)).toBe(true);
});

test('Query.acceptsOutcome', async () => {
  expect.soft(Query.parse(``).acceptsOutcome('expected')).toBe(true);
  expect.soft(Query.parse(``).acceptsOutcome('unexpected')).toBe(true);
  expect.soft(Query.parse(`s:failed`).acceptsOutcome('unexpected')).toBe(true);
  expect.soft(Query.parse(`s:fire`).acceptsOutcome('unexpected')).toBe(false);
  expect.soft(Query.parse(`s:fire`).acceptsOutcome('regressed')).toBe(true);
  expect.soft(Query.parse(`s:PASSED`).acceptsOutcome('unexpected')).toBe(false);
  expect.soft(Query.parse(`-s:PASSED`).acceptsOutcome('unexpected')).toBe(true);
  expect.soft(Query.parse(`s ≠  PASSED`).acceptsOutcome('unexpected')).toBe(true);
});
