#!/usr/bin/env bash

set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

APP_VERSION=$(node -e 'console.log(require("../package.json").version)')
SHA=$(git rev-parse HEAD)
TAG_COMMIT_X86="cr.flakiness.io/app:${SHA}-amd64"
TAG_APP_VERSION="cr.flakiness.io/app:v${APP_VERSION}"
TAG_LATEST="cr.flakiness.io/app:latest"

echo "publishing: ${TAG_COMMIT_X86}"
echo "publishing: ${TAG_APP_VERSION}"
echo "publishing: ${TAG_LATEST}"

if ! command -v regctl 2>/dev/null; then
  echo "ERROR: regclient must be installed to push to registry"
  exit 1
fi

# Build & push x86 image
docker system prune -fa
./build.sh "--x86" "${TAG_COMMIT_X86}"

# Remove old image, if any
rm -rf /tmp/oci-image.tar

echo ">>> Exporting OCI image"
docker save "${TAG_COMMIT_X86}" -o /tmp/oci-image.tar

# We use regclient instead of docker to push images to the registry. This is
# to workaround the limitation of the serverles-registry, see:
# https://github.com/cloudflare/serverless-registry/issues/42
echo ">>> Exporting OCI image layers"
regctl image import ocidir://dist/"${TAG_COMMIT_X86}" /tmp/oci-image.tar
echo ">>> Copying to registry"
regctl image copy ocidir://dist/"${TAG_COMMIT_X86}" "${TAG_COMMIT_X86}"
echo ">>> Tagging"
regctl image copy "${TAG_COMMIT_X86}" "${TAG_APP_VERSION}"
regctl image copy "${TAG_COMMIT_X86}" "${TAG_LATEST}"

rm -rf /tmp/oci-image.tar
rm -rf ./dist
