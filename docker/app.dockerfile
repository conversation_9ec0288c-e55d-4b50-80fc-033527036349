FROM node:22-bookworm as builder

# Prepare www-data home dir
RUN mkdir -p /var/www && chown www-data:www-data /var/www

# Copy only .git folder; we will restore index from it later.
COPY --chown=www-data:www-data ./.git /flakiness/.git
WORKDIR /flakiness
USER www-data
ENV BUILD_TYPE=production
ARG FLAKINESS_LICENSE_PUBLIC_KEY
ENV FLAKINESS_LICENSE_PUBLIC_KEY=${FLAKINESS_LICENSE_PUBLIC_KEY}
RUN git reset --hard && npm ci && npx kubik ./server/build.mts ./web/build.mts && \
    find . -name "node_modules" -type d -prune -exec rm -rf '{}' +
# Copy build artifacts
FROM node:22-bookworm-slim

# Prepare www-data homedir
RUN mkdir -p /var/www && chown www-data:www-data /var/www

COPY --chown=www-data:www-data --from=builder /flakiness /var/www/flakiness
WORKDIR /var/www/flakiness
USER www-data
RUN rm -rf /var/www/flakiness/.git && \
    npm ci --omit=dev --workspace=server

ENV FLAKINESS_WEB_PATH="/var/www/flakiness/web/dist"
