import { defineConfig } from '@playwright/test';
import dotenv from 'dotenv';
import path from 'path';
import url from 'url';
const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, '..', '.env.test') });

export default defineConfig({
  reporter: [
    ['list'],
    ['@flakiness/report/playwright-test'],
  ],
});
