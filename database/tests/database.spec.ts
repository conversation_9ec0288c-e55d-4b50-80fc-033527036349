import { expect } from '@playwright/test';
import ms from 'ms';
import { Database, NewOrganization, NewProductPlan, NewProject, NewUser, OrgPublicId, ProductPlanPublicId, ProjectPublicId, UserPublicId } from '../src/database.js';
import { dbTest } from './fixtures.js';

// <PERSON> owns 1 organization: flakiness. This organization has a project 'flakiness'.
// <PERSON> has a friend Alice.

const NewJohn = {
  user_public_id: 'user-1' as UserPublicId,
  user_login: 'johndoe',
  user_name: '<PERSON>',
  github_id: 123,
  user_avatar_url: null,
} satisfies NewUser;

const NewAlice = {
  user_public_id: 'user-2' as UserPublicId,
  user_login: 'aliceroe',
  user_name: '<PERSON>',
  github_id: 321,
  user_avatar_url: null,
} satisfies NewUser;

const NewOrgFlakiness = {
  org_name: 'flakiness',
  org_slug: 'flakiness',
  org_public_id: 'org-1' as OrgPublicId,
} satisfies Omit<NewOrganization, 'owner_id'>;

const NewProjFlakiness = {
  flakiness_access_token: 'someflakinessaccesstoken',
  project_public_id: 'random-public-uuid-1' as ProjectPublicId,
  project_name: 'flakiness',
  project_slug: 'flakiness',
  source_auth_type: Database.sourceAuthType.githubPat,
  source_owner_name: 'degulabs',
  source_repo_name: 'flakiness',
} satisfies Omit<NewProject, 'org_id'>;

const personalAccessToken1 = 'some-personal-access-token';
const personalAccessToken2 = 'some-other-persoanl-access-token';

dbTest('user management', async ({ db }) => {
  expect(await db.users.findUsers('john')).toEqual([]);
  
  const john = await db.users.create(NewJohn);
  
  expect(await db.users.findUsers('john')).toEqual([john]);
  
  const alice = await db.users.create(NewAlice);
  expect(await db.users.findUsers('john')).toEqual([john]);
  expect(await db.users.findUsers('alice')).toEqual([alice]);
  expect(await db.users.findUsers('')).toEqual([john, alice]);
  expect(await db.users.get(john.user_id)).toEqual(john);
  expect(await db.users.getByPublicId(john.user_public_id)).toEqual(john);
  expect(await db.users.getByPublicId('nonexistinguserid' as UserPublicId)).toEqual(undefined);
  expect(await db.users.getByGithubId(NewJohn.github_id)).toEqual(john);
  expect(await db.users.getByGithubId(NewAlice.github_id)).toEqual(alice);
});

dbTest('github oauth tokens', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  await db.githubOauthUserTokens.set(john.user_id, {
    accessExpirationMs: Date.now() + ms('1 day'),
    accessToken: 'gh-access-token',
    refreshExpirationMs: Date.now() + ms('1 day'),
    refreshToken: 'gh-refresh-token',
  });
  expect(await db.githubOauthUserTokens.get(john.user_id)).toEqual(expect.objectContaining({
    accessToken: 'gh-access-token',
    refreshToken: 'gh-refresh-token',
  }));

  await db.githubOauthUserTokens.refresh(john.user_id, async refreshToken => Promise.reject('failed to update'));
  expect(await db.githubOauthUserTokens.get(john.user_id)).toEqual(expect.objectContaining({
    accessToken: 'gh-access-token',
    refreshToken: 'gh-refresh-token',
  }));

  await db.githubOauthUserTokens.refresh(john.user_id, async refreshToken => ({
    accessExpirationMs: Date.now() + ms('1 day'),
    accessToken: 'new-access-token-from-' + refreshToken,
    refreshExpirationMs: Date.now() + ms('1 day'),
    refreshToken: 'new-refresh-token-from-' + refreshToken,
  }));
  expect(await db.githubOauthUserTokens.get(john.user_id)).toEqual(expect.objectContaining({
    accessToken: 'new-access-token-from-gh-refresh-token',
    refreshToken: 'new-refresh-token-from-gh-refresh-token',
  }));
});

dbTest('org management', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  expect(await db.orgSharing.getOrganizations(john.user_id)).toEqual([]);
  const org = await db.orgs.create({ ...NewOrgFlakiness, owner_id: john.user_id });

  expect(await db.orgSharing.getOrganizations(john.user_id)).toEqual([]);

  expect(await db.orgs.get(org.org_id)).toEqual(expect.objectContaining(NewOrgFlakiness));
  expect(await db.orgs.getBySlug(NewOrgFlakiness.org_slug)).toEqual(org);
  expect(await db.orgs.getBySlug('nonexistingorg')).toEqual(undefined);

  await db.orgs.delete(org.org_id);
  expect(await db.orgSharing.getOrganizations(john.user_id)).toEqual([]);
});

dbTest('org sharing', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  const alice = await db.users.create(NewAlice);
  const flakiness = await db.orgs.create({...NewOrgFlakiness, owner_id: john.user_id });

  expect(await db.orgSharing.getUsers(flakiness.org_id)).toEqual([]);
  expect(await db.orgSharing.getOrganizations(alice.user_id)).toEqual([]);

  await db.orgSharing.setAccess(flakiness.org_id, alice.user_id, Database.orgAccessRole.member);
  expect(await db.orgSharing.getUsers(flakiness.org_id)).toEqual([{
    userId: alice.user_id,
    accessRole: Database.orgAccessRole.member,
  }]);
  expect(await db.orgSharing.getOrganizations(alice.user_id)).toEqual([{
    orgId: flakiness.org_id,
    accessRole: Database.orgAccessRole.member,
  }]);

  await db.orgSharing.setAccess(flakiness.org_id, alice.user_id, Database.orgAccessRole.admin);
  expect(await db.orgSharing.getUsers(flakiness.org_id)).toEqual([{
    userId: alice.user_id,
    accessRole: Database.orgAccessRole.admin,
  }]);
  expect(await db.orgSharing.getOrganizations(alice.user_id)).toEqual([{
    orgId: flakiness.org_id,
    accessRole: Database.orgAccessRole.admin,
  }]);

  await db.orgSharing.setAccess(flakiness.org_id, alice.user_id, undefined);
  expect(await db.orgSharing.getUsers(flakiness.org_id)).toEqual([]);
  expect(await db.orgSharing.getOrganizations(alice.user_id)).toEqual([]);
});

dbTest('project create/delete', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  const org = await db.orgs.create({...NewOrgFlakiness, owner_id: john.user_id });

  expect(await db.orgs.getProjects(org.org_id)).toEqual([]);
  expect(await db.projects.getBySlug(org.org_id, NewProjFlakiness.project_slug)).toEqual(undefined);
  
  const p = await db.projects.create({...NewProjFlakiness, org_id: org.org_id }, { pat: personalAccessToken1 });
  
  expect(await db.projects.getByAccessToken(NewProjFlakiness.flakiness_access_token)).toEqual(p);
  expect(await db.orgs.getProjects(org.org_id)).toEqual([p.project_id]);
  expect(await db.projects.get(p.project_id)).toEqual(expect.objectContaining(NewProjFlakiness));
  expect(await db.projects.getBySlug(org.org_id, NewProjFlakiness.project_slug)).toEqual(p);
  
  const deleted = await db.projects.delete(p.project_id);
  expect(deleted).toEqual(expect.objectContaining(NewProjFlakiness));
  expect(await db.projects.get(p.project_id)).toBe(undefined);
  expect(await db.orgs.getProjects(org.org_id)).toEqual([]);
  expect(await db.projects.getBySlug(org.org_id, NewProjFlakiness.project_slug)).toEqual(undefined);
});

dbTest('project source', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  const org = await db.orgs.create({ ...NewOrgFlakiness, owner_id: john.user_id });
  const p = await db.projects.create({ ...NewProjFlakiness, org_id: org.org_id }, { pat: personalAccessToken1 });

  await db.projectSource.setProjectSource({
    projectId: p.project_id,
    sourceOwnerName: 'foobar',
    sourceRepo: 'baz',
    installationId: 'some-installation-id',
  });
  expect(await db.projectSource.getPersonalAccessToken(p.project_id)).toBe(undefined);
  expect(await db.projectSource.getInstallationId(p.project_id)).toBe('some-installation-id');

  await db.projectSource.setProjectSource({
    projectId: p.project_id,
    sourceOwnerName: 'foobar',
    sourceRepo: 'baz',
    personalAccessToken: personalAccessToken2
  });
  expect(await db.projectSource.getPersonalAccessToken(p.project_id)).toBe(personalAccessToken2);
  expect(await db.projectSource.getInstallationId(p.project_id)).toBe(undefined);
});

dbTest('project reports count', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  const org = await db.orgs.create({ ...NewOrgFlakiness, owner_id: john.user_id });
  const p = await db.projects.create({ ...NewProjFlakiness, org_id: org.org_id }, { pat: personalAccessToken1 });
  
  expect((await db.projects.get(p.project_id))?.reports_count).toBe(0);
  // Issue 100 updates to the database!
  const values = await Promise.all(Array(100).fill(0).map(() => db.projects.incReportCount(p.project_id)));
  values.sort((a, b) => a - b);
  expect(values).toEqual(Array(100).fill(0).map((z,index) => index + 1));

  expect((await db.projects.get(p.project_id))?.reports_count).toBe(100);
});

dbTest('project sharing', async ({ db }) => {
  const john = await db.users.create(NewJohn);
  const alice = await db.users.create(NewAlice);
  const org = await db.orgs.create({ ...NewOrgFlakiness, owner_id: john.user_id });
  const p = await db.projects.create({ ...NewProjFlakiness, org_id: org.org_id }, { pat: personalAccessToken1 });

  expect(await db.projectSharing.getUsers(p.project_id)).toEqual([]);
  expect(await db.orgSharing.getUsers(org.org_id)).toEqual([]);

  await db.projectSharing.setAccess(p.project_id, alice.user_id, Database.projectAccessRole.editor);
  expect(await db.orgSharing.getUsers(org.org_id)).toEqual([]);
  expect(await db.projectSharing.getUsers(p.project_id)).toEqual([{
    userId: alice.user_id,
    accessRole: Database.projectAccessRole.editor,
  }]);

  await db.projectSharing.setAccess(p.project_id, alice.user_id, Database.projectAccessRole.viewer);
  expect(await db.orgSharing.getUsers(org.org_id)).toEqual([]);
  expect(await db.projectSharing.getUsers(p.project_id)).toEqual([{
    userId: alice.user_id,
    accessRole: Database.projectAccessRole.viewer,
  }]);

  await db.projectSharing.setAccess(p.project_id, alice.user_id, undefined);
  expect(await db.orgSharing.getUsers(org.org_id)).toEqual([]);
  expect(await db.projectSharing.getUsers(p.project_id)).toEqual([]);
});

dbTest('product plans', async ({ db }) => {
  // Initially there should be no plans
  expect(await db.productPlans.list()).toEqual([]);
  
  // Create a new plan
  const planPublicId = 'plan-1' as ProductPlanPublicId;
  const newPlan: NewProductPlan = {
    plan_public_id: planPublicId,
    name: 'Basic Plan',
    org_public_id: null,
    seats: 5,
    seats_price_id: 'price_seats_basic',
    storage_price_id: 'price_storage_basic',
    testruns_price_id: 'price_testruns_basic',
    max_data_retention_days: 365,
  };
  
  const createdPlan = await db.productPlans.create(newPlan);
  expect(createdPlan).toEqual(expect.objectContaining(newPlan));
  
  // Get the plan by ID
  const retrievedPlan = await db.productPlans.get(createdPlan.plan_id);
  expect(retrievedPlan).toEqual(createdPlan);
  
  // Get the plan by public ID
  const retrievedByPublicId = await db.productPlans.getByPublicId(planPublicId);
  expect(retrievedByPublicId).toEqual(createdPlan);
  
  // List all plans
  expect(await db.productPlans.list()).toEqual([createdPlan]);
  
  // Update the plan
  const updatedPlan = await db.productPlans.update(createdPlan.plan_id, {
    name: 'Updated Basic Plan',
    seats: 10,
  });
  expect(updatedPlan).toEqual(createdPlan); // update returns the old plan
  
  // Verify the update worked
  const afterUpdate = await db.productPlans.get(createdPlan.plan_id);
  expect(afterUpdate).toEqual(expect.objectContaining({
    ...createdPlan,
    name: 'Updated Basic Plan',
    seats: 10,
  }));
  
  // Delete the plan
  const deletedPlan = await db.productPlans.delete(createdPlan.plan_id);
  expect(deletedPlan).toEqual(afterUpdate);
  
  // Verify it's gone
  expect(await db.productPlans.get(createdPlan.plan_id)).toBeUndefined();
  expect(await db.productPlans.list()).toEqual([]);
  
  // Deleting non-existent plan should return undefined
  expect(await db.productPlans.delete(999999 as any)).toBeUndefined();
});

