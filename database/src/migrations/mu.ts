import { SMap, SMapSelector } from '@flakiness/shared/common/smap.js';
import { sha256Object } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { ColumnDataType, ColumnDefinitionBuilder, Insertable, Kysely, Selectable } from 'kysely';

function indexName(table: string, columns: string[]) {
  return [`index`, table, ...columns].join('__');
}

function uniqueName(table: string, columns: string[]) {
  return [`unique`, table, ...columns].join('__');
}

function fkName(table: string, columns: string[]) {
  return [`foreign_key`, table, ...columns].join('__');
}

type SchemaColumnAttribute = 'not-null'|'primary'|'auto-inc';

type SchemaColumn = {
  type: ColumnDataType,
  is?: SchemaColumnAttribute[],
  default?: any,
};

type SchemaColumnFK = {
  ref: string,
  is?: SchemaColumnAttribute[],
  onDelete: 'cascade',
  customName?: string,
}

type SchemaUniqueConstraint = {
  columns: string[],
  customName?: string,
}

type SchemaIndex = {
  customName?: string,
  columns: string[],
}

type SchemaTable = {
  columns: Record<string, SchemaColumn|SchemaColumnFK|undefined>,
  uniques?: Record<string, SchemaUniqueConstraint|undefined>,
  indexes?: Record<string, SchemaIndex|undefined>,
}

type DBSchema<T> = {
  [K in keyof T]: SchemaTable;
}

type ResolvedTable = {
  columns: Map<string, SchemaColumn|undefined>,
  uniques: Map<string, GlobalUniqueConstraint>,
  indexes: Map<string, GlobalIndex>,
  fk: Map<string, GlobalFK>,
}

function resolveScheme(scheme: DBSchema<any>): Map<string, ResolvedTable> {
  const result = new Map<string, ResolvedTable>();
  const tables = new Map(Object.entries(scheme));
  for (const [tableName, table] of tables) {
    if (!table)
      continue;
    const uniques = extractUnique(tableName, table);
    const indexes = extractIndex(tableName, table);
    const fk = extractFK(tableName, table);
    const columns = new Map<string, SchemaColumn|undefined>(Object.entries(table.columns).map(([columnName, column]) => {
      if (!column)
        return [columnName, undefined];
      if ('ref' in column) {
        const [toTable, toColumn] = column.ref.split('.');
        const refColumn = tables.get(toTable)?.columns[toColumn];
        assert(refColumn);
        assert('type' in refColumn);

        let type = refColumn.type;
        if (type === 'serial')
          type = 'integer';
        return [columnName, {
          type,
          is: column.is,
        }];
      }
      return [columnName, column];
    }));
    result.set(tableName, {
      uniques,
      indexes,
      fk,
      columns,
    });
  }
  return result;
}

type GlobalIndex = {
  table: string,
  columns: string[],
  customName?: string,
};

type GlobalUniqueConstraint = {
  table: string,
  columns: string[],
  customName?: string,
};

type GlobalFK = {
  customName?: string,
  fk: {
    table: string,
    columns: string[],
  },
  pk: {
    table: string,
    columns: string[],
  },
};

async function addIndexes(db: Kysely<any>, indexes: GlobalUniqueConstraint[]) {
  for (const index of indexes) {
    await db.schema.createIndex(index.customName ?? indexName(index.table, index.columns))
      .on(index.table)
      .columns(index.columns)
      .execute();
  }
}

async function dropIndexes(db: Kysely<any>, indexes: GlobalUniqueConstraint[]) {
  for (const index of indexes) {
    await db.schema.dropIndex(index.customName ?? indexName(index.table, index.columns))
      .execute();
  }
}

async function addUniques(db: Kysely<any>, uniques: GlobalUniqueConstraint[]) {
  for (const unique of uniques) {
    await db.schema
      .alterTable(unique.table)
      .addUniqueConstraint(unique.customName ?? uniqueName(unique.table, unique.columns), unique.columns)
      .execute();
  }
}

async function dropUniques(db: Kysely<any>, uniques: GlobalUniqueConstraint[]) {
  for (const unique of uniques) {
    await db.schema
      .alterTable(unique.table)
      .dropConstraint(unique.customName ?? uniqueName(unique.table, unique.columns))
      .execute();
  }
}

async function addForeignKeys(db: Kysely<any>, foreignKeys: GlobalFK[]) {
  for (const foreignKey of foreignKeys) {
    await db.schema
      .alterTable(foreignKey.fk.table)
      .addForeignKeyConstraint(foreignKey.customName ?? fkName(foreignKey.fk.table, foreignKey.fk.columns), foreignKey.fk.columns, foreignKey.pk.table, foreignKey.pk.columns)
      .onDelete('cascade')
      .execute();
  }
}

async function dropForeignKeys(db: Kysely<any>, foreignKeys: GlobalFK[]) {
  for (const foreignKey of foreignKeys) {
    await db.schema
      .alterTable(foreignKey.fk.table)
      .dropConstraint(foreignKey.customName ?? fkName(foreignKey.fk.table, foreignKey.fk.columns))
      .execute();
  }
}

type ColumnDiff = {
  columnName: string,
  fromScheme: SchemaColumn,
  toScheme: SchemaColumn,
  addAttributes?: Set<SchemaColumnAttribute>,
  dropAttributes?: Set<SchemaColumnAttribute>,
  changeDefault?: { from: any, to: any },
}

type TableDiff = {
  tableName: string,
  columns: CreateAndDestroy<[string, SchemaColumn]>,
  changes: ColumnDiff[],
};

type CreateAndDestroy<T> = {
  toAdd: T[],
  toDrop: T[],
}

type SchemaDiff = {
  tables: CreateAndDestroy<[string, ResolvedTable]>,
  uniques: CreateAndDestroy<GlobalUniqueConstraint>,
  indexes: CreateAndDestroy<GlobalIndex>,
  fk: CreateAndDestroy<GlobalFK>,
  changes: TableDiff[],
}

function mapDifference<K, V>(from: Map<K, V>, to: Map<K, V>) {
  const fromKeys = new Set(from.keys());
  const toKeys = new Set(to.keys());
  const add = [...toKeys.difference(fromKeys)].map(key => [key, to.get(key)!] as [K, V]);
  const drop = [...fromKeys.difference(toKeys)].map(key => [key, from.get(key)!] as [K, V]);
  const same = [...fromKeys.intersection(toKeys)].map(key => [key, from.get(key)!, to.get(key)!] as [K, V, V]);
  return [add, drop, same] as [[K,V][], [K, V][], [K,V,V][]];
}

function extractFK(tableName: string, schema: SchemaTable): Map<string, GlobalFK> {
  const fk = [...Object.entries(schema.columns)].map(([columnName, column]) => {
    if (!column)
      return undefined;
    if (!('ref' in column))
      return undefined;
    const [toTable, toColumn] = column.ref.split('.');
    return {
      customName: column.customName,
      fk: {
        table: tableName,
        columns: [columnName],
      },
      pk: {
        table: toTable,
        columns: [toColumn],
      },
    } satisfies GlobalFK;
  }).filter(e => e !== undefined);
  return new Map(fk.map(fk => [sha256Object(fk), fk]));
}

function extractUnique(tableName: string, table: SchemaTable): Map<string, GlobalUniqueConstraint> {
  return new Map([...Object.entries(table.uniques ?? {})].map(([name, unique]) => unique ? [name, {
    table: tableName,
    columns: unique.columns,
    customName: unique.customName,
  }] : undefined).filter(e => e !== undefined) as [string, GlobalUniqueConstraint][]);
}

function extractIndex(tableName: string, table: SchemaTable): Map<string, GlobalIndex> {
  return new Map([...Object.entries(table.indexes ?? {})].map(([name, index]) => index ? [name, {
    table: tableName,
    columns: index.columns,
    customName: index.customName,
  }] : undefined).filter(e => e !== undefined) as [string, GlobalIndex][]);
}

function diffTables(tableName: string, from: ResolvedTable, to: ResolvedTable): TableDiff {
  const [addColumns, dropColumns, sameColumns] = mapDifference(from.columns, to.columns);

  const changes: ColumnDiff[] = [];
  for (const [columnName, fromColumn, toColumn] of sameColumns) {
    if (fromColumn === undefined) {
      addColumns.push([columnName, toColumn]);
      continue;
    }
    if (toColumn === undefined) {
      dropColumns.push([columnName, fromColumn]);
      continue;
    }
    // If the type is changed, then one column will be removed, and another added.
    if (fromColumn.type !== toColumn.type) {
      dropColumns.push([columnName, fromColumn]);
      addColumns.push([columnName, toColumn]);
      continue;
    }
    // Compute attributes to be changed
    const fromAttributes = new Set(fromColumn.is ?? []);
    const toAttributes = new Set(toColumn.is ?? []);
    const addAttributes = toAttributes.difference(fromAttributes);
    const dropAttributes = fromAttributes.difference(toAttributes);
    const columnDiff: ColumnDiff = {
      columnName,
      fromScheme: fromColumn,
      toScheme: toColumn,
      addAttributes: addAttributes.size ? addAttributes : undefined,
      dropAttributes: dropAttributes.size ? dropAttributes : undefined,
      changeDefault: fromColumn.default !== toColumn.default ? { from: fromColumn.default, to: toColumn.default } : undefined,
    };
    if (columnDiff.changeDefault || columnDiff.addAttributes || columnDiff.changeDefault)
      changes.push(columnDiff);
  }

  return {
    tableName,
    columns: {
      toAdd: addColumns.filter(([columnName, column]) => column !== undefined) as [string, SchemaColumn][],
      toDrop: dropColumns.filter(([columnName, column]) => column !== undefined) as [string, SchemaColumn][],
    },
    changes,
  };
}

function diffSchemes<OLD, NEW>(fromSchema: DBSchema<OLD>, toSchema: DBSchema<NEW>): SchemaDiff {
  const result: SchemaDiff = {
    changes: [],
    tables: {
      toAdd: [],
      toDrop: [],
    },
    fk: {
      toAdd: [],
      toDrop: [],
    },
    indexes: {
      toAdd: [],
      toDrop: [],
    },
    uniques: {
      toAdd: [],
      toDrop: [],
    },
  };

  const from = resolveScheme(fromSchema);
  const to = resolveScheme(toSchema);

  const [addTables, dropTables, sameTables] = mapDifference(from, to);
  // Add all tables that names we haven't seen before, along with their indexes/constrains/fk
  for (const [tableName, table] of addTables) {
    result.tables.toAdd.push([tableName, table]);
    result.fk.toAdd.push(...table.fk.values());
    result.indexes.toAdd.push(...table.indexes.values());
    result.uniques.toAdd.push(...table.uniques.values());
  }
  // Drop all tables that we no longer see, along with their indexes/constrains/fk
  for (const [tableName, table] of dropTables) {
    result.tables.toDrop.push([tableName, table]);
    result.fk.toDrop.push(...table.fk.values());
    result.indexes.toDrop.push(...table.indexes.values());
    result.uniques.toDrop.push(...table.uniques.values());
  }

  for (const [tableName, fromTable, toTable] of sameTables) {
    const tableDiff = diffTables(tableName, fromTable, toTable);
    result.changes.push(tableDiff);

    result.fk.toDrop.push(...fromTable.fk.values());
    result.fk.toAdd.push(...toTable.fk.values());
    result.indexes.toDrop.push(...fromTable.indexes.values());
    result.indexes.toAdd.push(...toTable.indexes.values());
    result.uniques.toDrop.push(...fromTable.uniques.values());
    result.uniques.toAdd.push(...toTable.uniques.values());
  }

  return result;
}

async function transformScheme<OLD, NEW>(db: Kysely<OLD>, from: DBSchema<OLD>, to: DBSchema<NEW>, beforeConstrainsCallback?: (db: Kysely<NEW>) => Promise<void>): Promise<void> {
  const diff = diffSchemes(from, to);
  // First, drop all old constraints
  await dropForeignKeys(db, diff.fk.toDrop);
  await dropIndexes(db, diff.indexes.toDrop);
  await dropUniques(db, diff.uniques.toDrop);

  const addNotNullConstraints: {
    tableName: string,
    columnName: string,
    type: ColumnDataType,
    defaultTo: any,
  }[] = [];

  const applyColumnAttributes = (b: ColumnDefinitionBuilder, tableName: string, columnName: string, column: SchemaColumn): ColumnDefinitionBuilder => {
    if (column.is?.includes('primary'))
      b = b.primaryKey();
    if (column.is?.includes('auto-inc'))
      b = b.generatedByDefaultAsIdentity();
    if (column.default !== undefined)
      b = b.defaultTo(column.default);
    // Postpone setting "not-null" bit: this will allow us to run data-modification callback
    // before making sure that all columns are not-null.
    if (column.is?.includes('not-null')) {
      addNotNullConstraints.push({
        columnName,
        tableName,
        type: column.type,
        defaultTo: column.default,
      });
    }
    return b;
  }

  // First, drop all tables.
  for (const [tableName] of diff.tables.toDrop)
    await db.schema.dropTable(tableName).execute();

  // Next, create all new tables.
  for (const [tableName, schema] of diff.tables.toAdd) {
    let b = db.schema.createTable(tableName);
    for (const [columnName, column] of schema.columns) {
      if (column)
        b = b.addColumn(columnName, column.type, b => applyColumnAttributes(b, tableName, columnName, column))
    }
    await b.execute();
  }
  // Now, proceed to table changes.
  for (const tableDiff of diff.changes) {
    // First, let's drop all columns.
    for (const [columnName] of tableDiff.columns.toDrop) {
      await db.schema.alterTable(tableDiff.tableName)
        .dropColumn(columnName)
        .execute();
    }
    // Next, let's add all new columns.
    for (const [columnName, column] of tableDiff.columns.toAdd) {
      await db.schema.alterTable(tableDiff.tableName)
        .addColumn(columnName, column.type, b => applyColumnAttributes(b, tableDiff.tableName, columnName, column))
        .execute();
    }
    // Next, let's apply column changes.
    for (const columnDiff of tableDiff.changes) {
      // Drop attributes first.
      for (const attr of columnDiff.dropAttributes ?? [])
        throw new Error(`Cannot drop attribute "${attr}"`);
      // Add new attributes.
      for (const attr of columnDiff.addAttributes ?? []) {
        if (attr === 'not-null') {
          addNotNullConstraints.push({
            tableName: tableDiff.tableName,
            columnName: columnDiff.columnName,
            type: columnDiff.toScheme.type,
            defaultTo: columnDiff.toScheme.default,
          });
        } else {
          throw new Error(`Cannot add attribute "${attr}`)
        }
      }
      // Change default.
      if (columnDiff.changeDefault)
        throw new Error(`Cannot change column default`);
    }
  }

  await beforeConstrainsCallback?.call(null, db as any as Kysely<NEW>);

  // Add not-null constraints
  for (const { tableName, columnName, type, defaultTo } of addNotNullConstraints) {
    await db.schema
      .alterTable(tableName)
      .$call(b =>
        b.alterColumn(columnName, b => b.setNotNull())
      )
      .execute();
  }
  // Finally, add all constraints
  // First, drop all old constraints
  await addIndexes(db, diff.indexes.toAdd);
  await addUniques(db, diff.uniques.toAdd);
  await addForeignKeys(db, diff.fk.toAdd);
}

function prettyPrint<DB>(schema: mu.Schema<DB>) {
  const lines: string[] = [];
  const log = (indent: number, text: string) => lines.push(' '.repeat(indent) + text);

  for (const [tableName, table] of Object.entries(schema) as [keyof DB, SchemaTable|undefined][]) {
    if (!table)
      continue;
    log(0, `${String(tableName)}:`);
    log(2, 'columns:');
    for (const [columnName, column] of Object.entries(table.columns)) {
      if (!column)
        continue;
      const nullable = column.is?.includes('not-null') || column.is?.includes('primary') ? '' : '?';
      const defaultTo = 'default' in column && column.default !== undefined ? ` = ${JSON.stringify(column.default)}` : '';
      const PK = column.is?.includes('primary') ? ' PK' : '';
      const typeOrReference = 'ref' in column ? ` → ${column.ref} (delete=${column.onDelete})` : `: ${column.type}`;
      log(4, `${columnName}${nullable}${typeOrReference}${defaultTo}${PK}`);
    }
    const uniques = Object.entries(table.uniques ?? {}).filter(([uniqueName, unique]) => !!unique) as [string, SchemaUniqueConstraint][];
    if (uniques.length) {
      log(2, 'uniques:');
      for (const [uniqueName] of uniques)
        log(4, `${uniqueName}`);
    }

    const indexes = Object.entries(table.indexes ?? {}).filter(([indexName, index]) => !!index) as [string, SchemaIndex][];
    if (indexes.length) {
      log(2, 'indexes:');
      for (const [indexName, index] of indexes)
        log(4, `${indexName}`);
    }
    log(0, '');
  }
  return lines.join('\n');
}

function topsort(schema: DBSchema<any>): string[] {
  const resolved = resolveScheme(schema);

  const visited = new Set<string>();
  const topsortTables: string[] = [];

  const dfs = (tableName: string) => {
    if (visited.has(tableName))
      return;
    visited.add(tableName);
    const table = resolved.get(tableName)!;
    for (const [fkName, fk] of table.fk)
      dfs(fk.pk.table);
    topsortTables.push(tableName);
  }
  for (const [tableName] of resolved)
    dfs(tableName);
  return topsortTables;
}

export namespace mu {
  // The easiest way to support UUIDv4 in both PgSql and MySql
  // is to store them as text representation.
  export const UUID_V4_TEXT_TYPE = 'varchar(36)';

  export type Schema<DB> = DBSchema<DB>;

  export const transform = transformScheme;
  export const print = prettyPrint;

  export function edit<FROM, TO>(base: Schema<FROM>, edit: Partial<Schema<TO>>): Schema<TO> {
    const result: any = { ...base, ...edit };
    for (const [tableName, table] of Object.entries(edit) as [string, SchemaTable|undefined][]) {
      // add/remove table
      if ((base as any)[tableName] === undefined || !table)
        continue;
      result[tableName] = {
        columns: {
          ...(base as any)[tableName].columns,
          ...table.columns,
        },
        indexes: {
          ...(base as any)[tableName].indexes,
          ...table.indexes,
        },
        uniques: {
          ...(base as any)[tableName].uniques,
          ...table.uniques,
        },
      }
    }
    return result;
  }

  export type PullData<DB> = {
    [K in keyof DB]: Selectable<DB[K]>[];
  };

  export async function pullAndClear<DB>(db: Kysely<DB>, schema: DBSchema<DB>): Promise<PullData<DB>> {
    const result: any = {};
    for (const tableName of topsort(schema).reverse()) {
      result[tableName] = await db.selectFrom(tableName as any).selectAll().execute();
      await db.deleteFrom(tableName as any).execute();
    }
    return result;
  }

  export async function pull<DB>(db: Kysely<DB>, schema: DBSchema<DB>): Promise<PullData<DB>> {
    const result: any = {};
    for (const tableName of topsort(schema).reverse())
      result[tableName] = await db.selectFrom(tableName as any).selectAll().execute();
    return result;
  }

  export async function push<DB>(
    db: Kysely<DB>,
    schema: DBSchema<DB>,
    data: PullData<DB>,
  ): Promise<void> {
    await db.transaction().execute(async trx => {
      const resolved = resolveScheme(schema);
      for (const tableName of topsort(schema)) {
        const table = resolved.get(tableName);
        assert(table);
        const entries = (data as any)[tableName] as Array<any>;
        if (!entries.length)
          continue;
  
        const CHUNK_SIZE = 1000;
        for (let i = 0; i < entries.length; i += CHUNK_SIZE) {
          await trx
            .insertInto(tableName as any)
            .values(entries.slice(i, i + CHUNK_SIZE))
            .execute();
        }
      }
    });
}

  type mapCallback<BASE, NEW, K extends keyof BASE & keyof NEW> = (
    element: Selectable<BASE[K]>,
    pushed: <E extends keyof NEW>(tableName: E, query: SMapSelector<Selectable<NEW[E]>>) => Selectable<NEW[E]>,
    old: <E extends keyof BASE>(tableName: E, query: SMapSelector<Selectable<BASE[E]>>) => Selectable<BASE[E]>,
  ) => Insertable<NEW[K]>;

  type Mappers<BASE, NEW> = {
    [K in keyof BASE & keyof NEW]?: mapCallback<BASE, NEW, K>
  } & {
    [K in keyof BASE & keyof NEW as BASE[K] extends NEW[K] ? never : K]-?: mapCallback<BASE, NEW, K>
  };

  export async function pushmapped<BASE, NEW>(
      db: Kysely<NEW>,
      schema: DBSchema<NEW>,
      data: PullData<BASE>,
      map: Mappers<BASE,NEW>
    ): Promise<void> {
    const oldMapping = new Map<string, SMap<any>>();
    for (const [tableName, entries] of Object.entries(data))
      oldMapping.set(tableName, new SMap(entries as any));
    const old = (tableName: string, query: SMapSelector<any>): any => {
      const elements = oldMapping.get(tableName)?.getAll(query);
      assert(elements && elements.length === 1);
      return elements[0];
    }

    const pushedMapping = new Map<string, SMap<any>>();
    const pushed = (tableName: string, query: SMapSelector<any>): any => {
      const elements = pushedMapping.get(tableName)?.getAll(query);
      assert(elements && elements.length === 1);
      return elements[0];
    }

    const resolved = resolveScheme(schema);
    for (const tableName of topsort(schema)) {
      const table = resolved.get(tableName);
      assert(table);
      const entries = (data as any)[tableName] as Array<any>;
      if (!entries.length)
        continue;
      const mapper = (map as any)[tableName];
      let toInsert = mapper ? entries.map(entry => mapper(entry, pushed, old)) : entries;
      const CHUNK_SIZE = 1000;
      for (let i = 0; i < toInsert.length; i += CHUNK_SIZE) {
        await db
          .insertInto(tableName as any)
          .values(toInsert.slice(i, i + CHUNK_SIZE))
          .execute();
      }
      const newElements = await db.selectFrom(tableName as any).selectAll().execute();
      pushedMapping.set(tableName, new SMap(newElements));
    }
  }

  export function migration<FROM, TO>(db: Kysely<FROM>, from: DBSchema<FROM>, to: DBSchema<TO>) {
    return new Migration<FROM, TO>(db, from, to);
  }

  export class Migration<FROM, TO> {
    constructor(
      private _db: Kysely<FROM>,
      private _from: DBSchema<FROM>,
      private _to: DBSchema<TO>
    ) {}

    async run(mappers?: Mappers<FROM, TO>) {
      const data = mappers ? await mu.pullAndClear(this._db, this._from) : undefined;
      await mu.transform(this._db, this._from, this._to, async db => {
        if (data && mappers)
          await mu.pushmapped(db, this._to, data, mappers);
      });
    }
  }
}
