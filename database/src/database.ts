import { Brand } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { promises as fs } from 'fs';
import {
  Dialect,
  FileMigrationProvider,
  Insertable,
  Kysely,
  Migrator,
  PostgresDialect,
  Selectable,
  sql,
  Transaction,
  Updateable
} from 'kysely';
import * as path from 'path';
import pg from 'pg';
import url from "url";
import { aes } from './aes.js';
import * as dbtypes from './generated/latest_types.js';
import { ActiveJob, ArchivedJob, Queue, QueuedJob } from './queue.js';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


export type ProjectPublicId = Brand<string, 'ProjectPublicId'>;
export type UserPublicId = Brand<string, 'UserPublicId'>;
export type OrgPublicId = Brand<string, 'OrgPublicId'>;
export type ProductPlanPublicId = Brand<string, 'ProductPlanPublicId'>;
export type UserId = Brand<number, 'UserId'>;
export type ProjectId = Brand<number, 'ProjectId'>;
export type OrgId = Brand<number, 'OrgId'>;
export type ProductPlanId = Brand<number, 'ProductPlanId'>;
export type SourceAuthType = Brand<number, 'SourceAuthType'>;
export type ProjectAccessRole = Brand<number, 'ProjectAccessRole'>;
export type OrgAccessRole = Brand<number, 'OrgAccessRole'>;
export type ProjectVisibility = Brand<number, 'ProjectVisibility'>;

export type User = Selectable<dbtypes.Users>;
export type NewUser = Insertable<dbtypes.Users>
export type UserUpdate = Updateable<dbtypes.Users>

export type Project = Selectable<dbtypes.Projects>;
export type NewProject = Insertable<dbtypes.Projects>;
export type ProjectUpdate = Updateable<dbtypes.Projects>;

export type Organization = Selectable<dbtypes.Organizations>;
export type NewOrganization = Insertable<dbtypes.Organizations>;
export type OrganizationUpdate = Updateable<dbtypes.Organizations>;

export type GithubOAuthToken = {
  accessToken: string,
  accessExpirationMs: number,
  refreshToken: string,
  refreshExpirationMs: number,
}

export type ProductPlan = Selectable<dbtypes.ProductPlans>;
export type NewProductPlan = Insertable<dbtypes.ProductPlans>;
export type ProductPlanUpdate = Updateable<dbtypes.ProductPlans>;

export type DatabaseConfig = {
  host: string,
  port: number,
  user: string,
  password: string,
  database: string,
  encryptionKey: string,
}

export class Database {
  static sourceAuthType = {
    githubPat: 1 as SourceAuthType,
    githubApp: 2 as SourceAuthType,
  };

  static orgAccessRole = {
    member: 100 as OrgAccessRole,
    admin: 200 as OrgAccessRole,
  };

  static projectAccessRole = {
    viewer: 1 as ProjectAccessRole,
    editor: 2 as ProjectAccessRole,
  };

  static projectVisibility = {
    private: 666 as ProjectVisibility,
    public: 777 as ProjectVisibility,
  };

  static createEncryptionKey() {
    return aes.createKey();
  }

  static async initializeOrDie(options: DatabaseConfig) {
    try {
      const db = await Database.initialize(options);
      console.log(`Connected to Postgres database at ${options.host}:${options.port}/${options.database}`);
      return db;
    } catch (e) {
      console.error(e);
      process.exit(1);
    }
  }

  static createEncryptionKeyForTest() {
    return aes.createKey();
  }

  static createKyselyForTest(options: DatabaseConfig) {
    return connectToPostgres(options, false);
  }

  static async initialize(options: DatabaseConfig) {
    const { db, migrator } = connectToPostgres(options, true);
    const migrations = await migrator.getMigrations();
    const unappliedMigrations = migrations.filter(migration => !migration.executedAt);
    if (unappliedMigrations.length) {
      const text = [
        `Cannot connect to the database!`,
        `The server is expecting a newer version of the database; the following migrations are not applied:`,
        ...unappliedMigrations.map(migration => `- ` + migration.name),
      ];
      throw new Error(text.join('\n'));
    }
    return new Database(db, options.encryptionKey);
  }

  static async migrate<T>(options: DatabaseConfig, migratorCallback: (migrator: Migrator, db: Kysely<any>) => Promise<T>): Promise<T> {
    const { db, migrator } = connectToPostgres(options, true);
    const oldEnvValue = process.env.DB_ENCRYPTION_KEY;
    try {
      // We use environment variables to propagate information down to migartors. (sigh)
      process.env.DB_ENCRYPTION_KEY = options.encryptionKey;
      return await migratorCallback(migrator, db);
    } finally {
      process.env.DB_ENCRYPTION_KEY = oldEnvValue;
      await db.destroy();
    }
  }

  private _db: Kysely<dbtypes.DB>;

  readonly users: Users;
  readonly orgs: Organizations;
  readonly projects: Projects;
  readonly projectSharing: ProjectSharing;
  readonly projectSource: ProjectSource;
  readonly githubOauthUserTokens: GithubOauthUserTokens;
  readonly orgSharing: OrganizationSharing;
  readonly queues: Queues;
  readonly productPlans: ProductPlans;

  constructor(
    db: Kysely<dbtypes.DB>,
    encryptionKey: string,
  ) {
    this._db = db;
    this.queues = new Queues(db, encryptionKey);
    this.users = new Users(db, encryptionKey);
    this.orgs = new Organizations(db, encryptionKey);
    this.projects = new Projects(db, encryptionKey);
    this.projectSharing = new ProjectSharing(db, encryptionKey);
    this.projectSource = new ProjectSource(db, encryptionKey);
    this.githubOauthUserTokens = new GithubOauthUserTokens(db, encryptionKey);
    this.orgSharing = new OrganizationSharing(db, encryptionKey);
    this.productPlans = new ProductPlans(db, encryptionKey);
  }

  async close() {
    await this._db.destroy();
  }
}

class Users {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async get(userId: UserId): Promise<User|undefined> {
    return await this._db.selectFrom('users')
      .selectAll('users')
      .where('user_id', '=', userId)
      .executeTakeFirst();
  }

  async getOrganizations(userId: UserId): Promise<OrgId[]> {
    const orgs = await this._db
      .selectFrom('organizations')
      .where('organizations.owner_id', '=', userId)
      .select('org_id')
      .execute();
    return orgs.map(org => org.org_id);
  }

  async getByPublicId(userPublicId: UserPublicId): Promise<User|undefined> {
    return await this._db.selectFrom('users')
      .selectAll()
      .where('users.user_public_id', '=', userPublicId)
      .executeTakeFirst();
  }

  async getByGithubId(githubId: number): Promise<User|undefined> {
    return await this._db.selectFrom('users')
      .selectAll('users')
      .where('github_id', '=', githubId)
      .executeTakeFirst();
  }

  async findUsers(query: string): Promise<User[]> {
    const users = await this._db.selectFrom('users')
      .selectAll('users')
      .where(eb => eb.or([
        eb(sql`LOWER(user_name)`, 'like', query.toLowerCase() + '%'),
        eb(sql`LOWER(user_login)`, 'like', query.toLowerCase() + '%'),
      ]))
      .limit(10)
      .execute();
    return users;
  }

  async create(user: NewUser): Promise<User> {
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('users')
        .values(user)
        .execute();
      const result = await trx.selectFrom('users').where('user_public_id', '=', user.user_public_id).selectAll().executeTakeFirst();
      assert(result);
      return result;
    })
  }
}

class Organizations {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async all(): Promise<Organization[]> {
    return await this._db.selectFrom('organizations')
      .selectAll('organizations')
      .execute();
  }

  async getProjects(orgId: OrgId): Promise<ProjectId[]> {
    const result = await this._db.selectFrom('projects')
      .where('org_id', '=', orgId)
      .select('project_id')
      .execute();
    return result.map(entry => entry.project_id);
  }

  async getProjectPublicIds(orgId: OrgId): Promise<ProjectPublicId[]> {
    const result = await this._db.selectFrom('projects')
      .where('org_id', '=', orgId)
      .select('project_public_id')
      .execute();
    return result.map(entry => entry.project_public_id);
  }

  async update(orgId: OrgId, update: Partial<OrganizationUpdate>): Promise<Organization|undefined> {
    return await this._db.transaction().execute(async trx => {
      const old = trx.selectFrom('organizations')
        .where('org_id', '=', orgId)
        .selectAll()
        .executeTakeFirst();
      if (!old)
        return old;

      await trx
        .updateTable('organizations')
        .set(update)
        .where('org_id', '=', orgId)
        .execute();
      return old;
    });
  }

  async create(org: NewOrganization): Promise<Organization> {
    return await this._db.transaction().execute(async trx => {
      await trx.insertInto('organizations').values(org).execute();
      const result = await trx.selectFrom('organizations').where('org_slug', '=', org.org_slug).selectAll().executeTakeFirst();
      assert(result);
      return result;
    })
  }

  async get(orgId: OrgId): Promise<Organization|undefined> {
    return await this._db.selectFrom('organizations')
      .where('org_id', '=', orgId)
      .selectAll()
      .executeTakeFirst();
  }

  async getBySlug(slug: string): Promise<Organization|undefined> {
    return await this._db.selectFrom('organizations')
      .selectAll()
      .where('org_slug', '=', slug)
      .executeTakeFirst();
  }

  async getByPublicId(publicId: OrgPublicId): Promise<Organization|undefined> {
    return await this._db.selectFrom('organizations')
      .selectAll()
      .where('org_public_id', '=', publicId)
      .executeTakeFirst();
  }

  async delete(orgId: OrgId): Promise<Organization|undefined> {
    return await this._db.transaction().execute(async trx => {
      const result = await trx.selectFrom('organizations')
        .where('org_id', '=', orgId)
        .selectAll()
        .forUpdate()
        .executeTakeFirst();
      if (!result)
        return undefined;
      await trx.deleteFrom('organizations')
        .where('org_id', '=', orgId)
        .execute();
      return result;
    });
  }
}

class Projects {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async all(): Promise<Project[]> {
    return await this._db.selectFrom('projects')
      .selectAll('projects')
      .execute();
  }

  async get(projectId: ProjectId): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll('projects')
      .where('projects.project_id', '=', projectId)
      .executeTakeFirst();
  }

  async getBySlug(orgId: OrgId, projectSlug: string): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll()
      .where('org_id', '=', orgId)
      .where(`project_slug`, '=', projectSlug)
      .executeTakeFirst();
  }

  async getByAccessToken(accessToken: string): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll()
      .where('flakiness_access_token', '=', accessToken)
      .executeTakeFirst();
  }

  async getByPublicId(projectPublicId: ProjectPublicId): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll()
      .where('projects.project_public_id', '=', projectPublicId)
      .executeTakeFirst();
  }

  async delete(projectId: ProjectId): Promise<Project|undefined> {
    return await this._db.transaction().execute(async trx => {
      const result = await trx.selectFrom('projects')
        .where('project_id', '=', projectId)
        .selectAll()
        .forUpdate()
        .executeTakeFirst();
      if (!result)
        return undefined;
      await trx.deleteFrom('projects')
        .where('project_id', '=', projectId)
        .execute();
      return result;
    });
  }

  async incReportCount(projectId: ProjectId): Promise<number> {
    const result = await this._db.transaction().execute(async trx => {
      const updated = await trx
        .updateTable('projects')
        .set('reports_count', sql`reports_count + 1`)
        .where('project_id', '=', projectId)
        .returning('reports_count')
        .executeTakeFirstOrThrow();
      return updated.reports_count;
    });
    return result;
  }

  async update(projectId: ProjectId, update: Partial<ProjectUpdate>): Promise<Project|undefined> {
    return await this._db.transaction().execute(async trx => {
      const old = await trx.selectFrom('projects')
        .where('project_id', '=', projectId)
        .selectAll()
        .executeTakeFirst();
      if (!old)
        return old;

      await trx
        .updateTable('projects')
        .set(update)
        .where('project_id', '=', projectId)
        .execute();
      return old;
    });
  }

  async create(proj: NewProject, auth: { pat?: string, installationId?: string }): Promise<Project> {
    assert(auth.installationId || auth.pat, 'Either auth or pat must be defined');
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('projects')
        .values({
          ...proj,
          source_auth_type: auth.pat ? Database.sourceAuthType.githubPat : Database.sourceAuthType.githubApp,
        })
        .execute();
      const project = await trx.selectFrom('projects')
        .selectAll()
        .where('org_id', '=', proj.org_id)
        .where('project_slug', '=', proj.project_slug)
        .executeTakeFirst();
      assert(project);

      if (auth.installationId) {
        await trx
          .insertInto('github_app_installations')
          .values({
            project_id: project.project_id,
            installation_id_encoded: aes.encrypt(this._encryptionKey, auth.installationId),
          })
          .execute();
      } else if (auth.pat) {
        await trx
          .insertInto('personal_access_tokens')
          .values({
            project_id: project.project_id,
            pat_encoded: aes.encrypt(this._encryptionKey, auth.pat),
          })
          .execute();
      } else {
        throw new Error('Either auth or pat must be defined');
      }
      return project;
    });
  }
}

class GithubOauthUserTokens {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async set(userId: UserId, token: GithubOAuthToken) {
    const values = fromGithubOAuthToken({
      key: this._encryptionKey, userId, token
    });
    await this._db.insertInto('github_oauth_user_tokens')
      .values(values)
      .$call(b => b.onConflict(cb => cb.column('user_id').doUpdateSet(values)))
      .execute();
  }

  async refresh(userId: UserId, refreshCallback: (refreshToken: string) => Promise<GithubOAuthToken>): Promise<GithubOAuthToken|undefined> {
    return await this._db.transaction().execute(async trx => {
      const token = await trx
        .selectFrom('github_oauth_user_tokens')
        .where('user_id', '=', userId)
        .selectAll()
        .forUpdate()
        .skipLocked()
        .limit(1)
        .executeTakeFirst();
      if (!token)
        return undefined;

      const newToken = await refreshCallback(aes.decrypt(this._encryptionKey, token.refresh_token_encoded)).catch(e => {
        console.error(`FAILED TO REFRESH ACCESS TOKEN for userId = ${userId}`);
        console.error(e);
        return undefined;
      });
      if (newToken) {
        const values = fromGithubOAuthToken({ userId, key: this._encryptionKey, token: newToken });
        await trx.insertInto('github_oauth_user_tokens')
          .values(values)
          .$call(b => b.onConflict(cb => cb.column('user_id').doUpdateSet(values)))
          .execute();
      }
      return newToken;
    });
  }

  async get(userId: UserId): Promise<GithubOAuthToken|undefined> {
    const result = await this._db.selectFrom('github_oauth_user_tokens')
      .selectAll()
      .where('user_id', '=', userId)
      .executeTakeFirst();
    return result ? toGithubOAuthToken({ key: this._encryptionKey, token: result }) : undefined;
  }
}

class OrganizationSharing {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async getOrganizations(userId: UserId): Promise< { orgId: OrgId, accessRole: OrgAccessRole }[]> {
    const result = await this._db
      .selectFrom('organization_members')
      .where('organization_members.user_id', '=', userId)
      .select(['organization_members.access_role', 'organization_members.org_id'])
      .execute();
    return result.map(entry => ({
      accessRole: entry.access_role,
      orgId: entry.org_id,
    }));
  }

  async getUsers(orgId: OrgId): Promise<{ userId: UserId, accessRole: OrgAccessRole }[]> {
    const result = await this._db
      .selectFrom('organization_members')
      .where('organization_members.org_id', '=', orgId)
      .select(['organization_members.access_role', 'organization_members.user_id'])
      .execute();
    return result.map(entry => ({
      accessRole: entry.access_role,
      userId: entry.user_id,
    }));
  }

  async setAccess(orgId: OrgId, userId: UserId, access: OrgAccessRole | undefined) {
    if (!access) {
      await this._db
        .deleteFrom('organization_members')
        .where('org_id', '=', orgId)
        .where('user_id', '=', userId)
        .execute();
    } else {
      await this._db
        .insertInto('organization_members')
        .values({
          org_id: orgId,
          user_id: userId,
          access_role: access,
        })
        .$call((ib) => ib.onConflict(oc =>
          oc.columns(['org_id', 'user_id'])
          .doUpdateSet({ access_role: access })
        ))
        .execute();
    }
  }
}

class ProjectSharing {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  /**
   * Return all the projects the user has explicit role at.
   * @param userId 
   * @returns 
   */
  async getProjects(userId: UserId): Promise<{ projectId: ProjectId, accessRole: ProjectAccessRole }[]> {
    const projectsAndRoles = await this._db
      .selectFrom('project_collaborators')
      .where('project_collaborators.user_id', '=', userId)
      .select(['project_collaborators.access_role', 'project_collaborators.project_id'])
      .execute();
    return projectsAndRoles.map(entry => ({
      projectId: entry.project_id,
      accessRole: entry.access_role,
    }));
  }

  async getUsers(projectId: ProjectId): Promise<{ userId: UserId, accessRole: ProjectAccessRole }[]> {
    const projectsAndRoles = await this._db
      .selectFrom('project_collaborators')
      .where('project_collaborators.project_id', '=', projectId)
      .select(['project_collaborators.access_role', 'project_collaborators.user_id'])
      .execute();
    return projectsAndRoles.map(entry => ({
      userId: entry.user_id,
      accessRole: entry.access_role,
    }));
  }

  async setAccess(projectId: ProjectId, userId: UserId, access: ProjectAccessRole | undefined) {
    if (!access) {
      await this._db
        .deleteFrom('project_collaborators')
        .where('project_id', '=', projectId)
        .where('user_id', '=', userId)
        .execute();
    } else {
      await this._db
        .insertInto('project_collaborators')
        .values({
          project_id: projectId,
          user_id: userId,
          access_role: access,
        })
        .$call((ib) => ib.onConflict(oc =>
          oc.columns(['project_id', 'user_id'])
          .doUpdateSet({ access_role: access })
        ))
        .execute();
    }
  }
}


class ProjectSource {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async getPersonalAccessToken(projectId: ProjectId): Promise<string|undefined> {
    const result = await this._db.selectFrom('personal_access_tokens')
      .selectAll('personal_access_tokens')
      .where('project_id', '=', projectId)
      .executeTakeFirst();
    return result?.pat_encoded ? aes.decrypt(this._encryptionKey, result.pat_encoded) : undefined;
  }

  async getInstallationId(projectId: ProjectId): Promise<string|undefined> {
    const result = await this._db.selectFrom('github_app_installations')
      .selectAll('github_app_installations')
      .where('project_id', '=', projectId)
      .executeTakeFirst();
    return result?.installation_id_encoded ? aes.decrypt(this._encryptionKey, result.installation_id_encoded) : undefined;
  }

  async setProjectSource(options: { projectId: ProjectId, sourceOwnerName: string, sourceRepo: string, personalAccessToken?: string, installationId?: string }) {
    assert(options.personalAccessToken || options.installationId, 'Either PAT or installation id must be present')
    await this._db.transaction().execute(async trx => {
      await trx
        .deleteFrom('personal_access_tokens')
        .where('project_id', '=', options.projectId)
        .execute();
      await trx
        .deleteFrom('github_app_installations')
        .where('project_id', '=', options.projectId)
        .execute();
      
      if (options.installationId) {
        await trx
          .insertInto('github_app_installations')
          .values({
            project_id: options.projectId,
            installation_id_encoded: aes.encrypt(this._encryptionKey, options.installationId),
          })
          .execute();
        await trx
          .updateTable('projects')
          .where('project_id', '=', options.projectId)
          .set({
            source_owner_name: options.sourceOwnerName,
            source_repo_name: options.sourceRepo,
            source_auth_type: Database.sourceAuthType.githubApp,
          })
          .execute();
      } else if (options.personalAccessToken) {
        await trx
          .insertInto('personal_access_tokens')
          .values({
            project_id: options.projectId,
            pat_encoded: aes.encrypt(this._encryptionKey, options.personalAccessToken),
          })
          .execute();
        await trx
          .updateTable('projects')
          .where('project_id', '=', options.projectId)
          .set({
            source_owner_name: options.sourceOwnerName,
            source_repo_name: options.sourceRepo,
            source_auth_type: Database.sourceAuthType.githubPat,
          })
          .execute();
      } else {
        throw new Error('either PAT or installation id must be present')
      }
    });
  }
}

class Queues {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}


  createQueue<T extends {}>(name: string): Queue<T> {
    return new Queue<T>({
      db: this._db,
      name,
    });
  }

  private async _listJobs<T>(trx: Transaction<dbtypes.DB>, table: 'archived_jobs'|'queued_jobs'|'active_jobs', offset: number, pageSize: number) {
    const countResult = await trx
        .selectFrom(table)
        .select(this._db.fn.countAll().as("count"))
        .executeTakeFirst();
      if (!countResult)
        return { count: 0, jobs: [] };
      const jobs = await trx
        .selectFrom(table)
        .selectAll()
        .orderBy(table === 'archived_jobs' ? sql`execution_duration_seconds + execution_timestamp_seconds` : 'id', 'desc')
        .limit(pageSize)
        .offset(offset)
        .execute();
      const count = typeof countResult.count === 'string' ? parseInt(countResult.count, 10) :
          typeof countResult.count === 'number' ? countResult.count :
          Number(countResult.count);
      return { count, jobs: jobs as T[] };
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listJobs(pageSize: number, offsetQueued: number, offsetActive: number, offsetArchived: number) {
    return await this._db.transaction().execute(async trx => {
      return {
        queued: await this._listJobs<QueuedJob>(trx, 'queued_jobs', offsetQueued, pageSize),
        active: await this._listJobs<ActiveJob>(trx, 'active_jobs', offsetActive, pageSize),
        archived: await this._listJobs<ArchivedJob>(trx, 'archived_jobs', offsetArchived, pageSize),
      }
    });
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listArchivedJobs(offset: number, pageSize: number): Promise<{ count: number, jobs: ArchivedJob[] }> {
    return await this._db.transaction().execute(async trx => this._listJobs(trx, 'archived_jobs', offset, pageSize));
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listQueuedJobs(offset: number, pageSize: number): Promise<{ count: number, jobs: QueuedJob[] }> {
    return await this._db.transaction().execute(async trx => this._listJobs(trx, 'queued_jobs', offset, pageSize));
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listActiveJobs(offset: number, pageSize: number): Promise<{ count: number, jobs: ActiveJob[] }> {
    return await this._db.transaction().execute(async trx => this._listJobs(trx, 'active_jobs', offset, pageSize));
  }
}

class ProductPlans {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async list(): Promise<ProductPlan[]> {
    return await this._db.selectFrom('product_plans')
      .selectAll()
      .execute();
  }

  async get(planId: ProductPlanId): Promise<ProductPlan|undefined> {
    return await this._db.selectFrom('product_plans')
      .selectAll()
      .where('plan_id', '=', planId)
      .executeTakeFirst();
  }

  async getByPublicId(planPublicId: ProductPlanPublicId): Promise<ProductPlan|undefined> {
    return await this._db.selectFrom('product_plans')
      .selectAll()
      .where('plan_public_id', '=', planPublicId)
      .executeTakeFirst();
  }

  async update(planId: ProductPlanId, update: Partial<ProductPlanUpdate>): Promise<ProductPlan|undefined> {
    return await this._db.transaction().execute(async trx => {
      const old = await trx.selectFrom('product_plans')
        .where('plan_id', '=', planId)
        .selectAll()
        .executeTakeFirst();
      if (!old)
        return old;

      await trx
        .updateTable('product_plans')
        .set(update)
        .where('plan_id', '=', planId)
        .execute();
      return old;
    });
  }

  async create(plan: NewProductPlan): Promise<ProductPlan> {
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('product_plans')
        .values(plan)
        .execute();
      
      const result = await trx.selectFrom('product_plans')
        .selectAll()
        .where('plan_public_id', '=', plan.plan_public_id)
        .executeTakeFirst();
      
      assert(result);
      return result;
    });
  }

  async delete(planId: ProductPlanId): Promise<ProductPlan|undefined> {
    return await this._db.transaction().execute(async trx => {
      const result = await trx.selectFrom('product_plans')
        .where('plan_id', '=', planId)
        .selectAll()
        .forUpdate()
        .executeTakeFirst();

      if (!result)
        return undefined;
      
      await trx.deleteFrom('product_plans')
        .where('plan_id', '=', planId)
        .execute();
      
      return result;
    });
  }
}

function connectToPostgres(options: DatabaseConfig, logError: boolean) {
  const int8TypeId = 20;
  // Map int8 to number.
  pg.types.setTypeParser(int8TypeId, (val) => {
    return parseInt(val, 10)
  });

  const pool = new pg.Pool({
    host: options.host,
    port: options.port,
    database: options.database,
    user: options.user,
    password: options.password,
    // These are important so that PGClient re-connects after
    // the connection is dropped.
    connectionTimeoutMillis: 2000,
    idleTimeoutMillis: 30000,
  });
  // Automatically reconnect on errors
  pool.on('error', (err) => {
    console.error('Unexpected pool error:', err);
  });

  return connect(new PostgresDialect({ pool, }), logError);
}

function connect(dialect: Dialect, logError: boolean) {
  const db = new Kysely<dbtypes.DB>({
    dialect,
    log: logError ? ['error'] : [],
  });
  const migrator = new Migrator({
    db,
    provider: new FileMigrationProvider({
      fs,
      path,
      // This needs to be an absolute path.
      migrationFolder: path.join(__dirname, './migrations'),
    }),
  });
  return { db, migrator };
}

function fromGithubOAuthToken(options: { key: string, userId: UserId, token: GithubOAuthToken }): Selectable<dbtypes.GithubOauthUserTokens> {
  return {
    user_id: options.userId,
    access_expiration_timestamp_seconds: Math.round(options.token.accessExpirationMs / 1000),
    access_token_encoded: aes.encrypt(options.key, options.token.accessToken),
    refresh_expiration_timestamp_seconds: Math.round(options.token.refreshExpirationMs / 1000),
    refresh_token_encoded: aes.encrypt(options.key, options.token.refreshToken),
  };
}

function toGithubOAuthToken(options: { key: string, token: Selectable<dbtypes.GithubOauthUserTokens> }): GithubOAuthToken {
  return {
    accessExpirationMs: options.token.access_expiration_timestamp_seconds * 1000,
    accessToken: aes.decrypt(options.key, options.token.access_token_encoded),
    refreshExpirationMs: options.token.refresh_expiration_timestamp_seconds * 1000,
    refreshToken: aes.decrypt(options.key, options.token.refresh_token_encoded),
  }
}
