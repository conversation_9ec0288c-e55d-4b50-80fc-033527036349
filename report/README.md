# Flakiness Report

This folder contains the source for the `@flakiness/report` package. This
package is published automatically when a new version of the service is
deployed.

The package consists of:
- A CLI to interact with flakiness.io service
- A set of reporters for popular test runners to generate & upload Flakiness
  report.
  Supported test runners:
    * [playwright/test](https://github.com/microsoft/playwright)
- Typescript definition of the Flakiness Report JSON
- Validation utilities for the Flakiness Report JSON (TBD)

## Getting Started

To start using reporter with Playwright Test:

1. Install this package:
    ```bash
    npm i @flakiness/report@latest
    ```
2. Add flakiness.io  to the `playwright.config.ts` file:
    ```ts
    import { defineConfig } from '@playwright/test';

    export default defineConfig({
      reporter: [
        ['list'],
        ['@flakiness/report/playwright-test', {
          endpoint: 'https://flakiness.io', // custom endpoint
          token: '...', // Flakiness access token
          collectBrowserVersion: true, // collect browser versions
        }]
      ],
    });
    ```

