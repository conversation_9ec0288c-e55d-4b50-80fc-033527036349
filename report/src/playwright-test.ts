import { Multimap } from '@flakiness/shared/common/multimap.js';
import { BrowserType } from '@playwright/test';
import type {
  FullConfig,
  FullProject,
  FullResult,
  Location,
  Reporter,
  Suite, TestCase, TestError, TestResult,
  TestStep
} from '@playwright/test/reporter';
import fs from 'fs';
import path from 'path';
import { FlakinessReport as FK, FlakinessReport } from './common/flakinessReport.js';
import { createTestStepSnippets } from './createTestStepSnippets.js';
import { ReportUploader } from './reportUploader.js';
import { computeGitRoot, createEnvironments, existsAsync, gitCommitInfo, gitFilePath, inferRunUrl, NormalizedPath, normalizePath, parseDurationMS, sha256Buffer, sha256File, stripAnsi } from './utils.js';

type ProcessingContext = {
  project2environmentIdx: Map<FullProject, number>,
  testBaseDir: NormalizedPath,
  gitRoot: NormalizedPath,
  attachments: Map<string, FK.ExternalAttachment>,
  unaccessibleAttachmentPaths: string[],
}

export default class FlakinessReporter implements Reporter {
  private _config?: FullConfig;
  private _rootSuite?: Suite;
  private _results = new Multimap<TestCase, TestResult>();
  private _unattributedErrors: TestError[] = [];

  private _filepathToSteps = new Multimap<string, FK.TestStep>();

  constructor(private _options: {
    endpoint?: string,
    token?: string,
    collectBrowserVersions?: boolean,
    relatedCommitIds?: string[],
  } = {}) {
  }

  printsToStdio(): boolean {
    return false;
  }

  onBegin(config: FullConfig, suite: Suite) {
    this._config = config;
    this._rootSuite = suite;
  }

  onError(error: TestError): void {
    this._unattributedErrors.push(error);
  }

  onTestBegin(test: TestCase) {
  }

  onTestEnd(test: TestCase, result: TestResult) {
    this._results.set(test, result);
  }

  private async _toFKSuites(context: ProcessingContext, pwSuite: Suite): Promise<FK.Suite[]> {
    const location = pwSuite.location;
    // Location should be missing only for root and project suites. Either way, we skip
    // the suite if there's no location.
    if (pwSuite.type === 'root' || pwSuite.type === 'project' || !location)
      return (await Promise.all(pwSuite.suites.map(suite => this._toFKSuites(context, suite)))).flat();

    let type: FK.SuiteType = 'suite';
    if (pwSuite.type === 'file')
      type = 'file';
    else if (pwSuite.type === 'describe' && !pwSuite.title)
      type = 'anonymous suite';
  
    return [{
      type,
      title: pwSuite.title,
      location: this._createLocation(context, location),
      suites: (await Promise.all(pwSuite.suites.map(suite => this._toFKSuites(context, suite)))).flat(),
      tests: await Promise.all(pwSuite.tests.map(test => this._toFKTest(context, test))),
    } as FK.Suite];
  }

  private async _toFKTest(context: ProcessingContext, pwTest: TestCase): Promise<FK.Test> {
    return {
      title: pwTest.title,
      // Playwright Test tags must start with '@' so we cut it off.
      tags: pwTest.tags.map(tag => tag.startsWith('@') ? tag.substring(1) : tag),
      location: this._createLocation(context, pwTest.location),
      // de-duplication of tests will happen later, so here we will have a single test run with attempts.
      runs: [await this._toFKTestRun(context, pwTest)],
    } as FK.Test;
  }

  private async _toFKTestRun(context: ProcessingContext, pwTest: TestCase): Promise<FK.TestRun> {
    const testRun: FK.TestRun = {
      timeout: parseDurationMS(pwTest.timeout),
      annotations: pwTest.annotations,
      environmentIdx: context.project2environmentIdx.get(pwTest.parent.project()!)!,
      expectedStatus: pwTest.expectedStatus,
      outcome: pwTest.outcome(),
      attempts: await Promise.all(this._results.getAll(pwTest).map(result => this._toFKRunAttempt(context, pwTest, result))),
    };
    return testRun;
  }

  private async _toFKRunAttempt(context: ProcessingContext, pwTest: TestCase, result: TestResult): Promise<FK.RunAttempt> {
    const attachments: FK.Attachment[] = [];
    const attempt: FK.RunAttempt = {
      parallelIndex: result.parallelIndex,
      retry: result.retry,
  
      status: result.status as FK.TestStatus,
      errors: result.errors && result.errors.length ? result.errors.map(error => this._toFKTestError(context, error)) : undefined,
  
      stdout: result.stdout ? result.stdout.map(toSTDIOEntry) : undefined,
      stderr: result.stderr ? result.stderr.map(toSTDIOEntry) : undefined,
  
      steps: result.steps ? result.steps.map(jsonTestStep => this._toFKTestStep(context, jsonTestStep)) : undefined,
  
      startTimestamp: +result.startTime as FK.UnixTimestampMS,
      duration: +result.duration as FK.DurationMS,
  
      attachments,
    };

    await Promise.all((result.attachments ?? []).map(async jsonAttachment => {
      // If we cannot access attachment path, then we should skip this attachment, and add it to the "unaccessible" array.
      if (jsonAttachment.path && !(await existsAsync(jsonAttachment.path))) {
        context.unaccessibleAttachmentPaths.push(jsonAttachment.path);
        return;
      }
      const id = (jsonAttachment.path ? await sha256File(jsonAttachment.path) : sha256Buffer(jsonAttachment.body ?? '')) as FK.AttachmentId;
      context.attachments.set(id, {
        contentType: jsonAttachment.contentType,
        id,
        body: jsonAttachment.body,
        path: jsonAttachment.path,
      });
      attachments.push({
        id,
        name: jsonAttachment.name,
        contentType: jsonAttachment.contentType,
      });
    }));

    return attempt;
  }

  private _toFKTestStep(context: ProcessingContext, pwStep: TestStep): FK.TestStep {
    const step: FK.TestStep = {
      // NOTE: jsonStep.duration was -1 in some playwright versions
      duration: parseDurationMS(Math.max(pwStep.duration, 0)),
      title: pwStep.title,
      location: pwStep.location ? this._createLocation(context, pwStep.location) : undefined,
    };

    if (pwStep.location) {
      const resolvedPath = path.resolve(pwStep.location.file);
      this._filepathToSteps.set(resolvedPath, step);
    }

    if (pwStep.error)
      step.error = this._toFKTestError(context, pwStep.error);
    if (pwStep.steps)
      step.steps = pwStep.steps.map(childJSONStep => this._toFKTestStep(context, childJSONStep));
    return step;
  }

  private _createLocation(
    context: ProcessingContext,
    pwLocation: Location,
  ): FK.Location {
    return {
      file: gitFilePath(context.gitRoot, normalizePath(pwLocation.file)),
      line: pwLocation.line as FK.Number1Based,
      column: pwLocation.column as FK.Number1Based,
    };
  }

  private _toFKTestError(context: ProcessingContext, pwError: TestError) {
    return {
      location: pwError.location ? this._createLocation(context, pwError.location) : undefined,
      message: stripAnsi(pwError.message ?? '').split('\n')[0],
      snippet: pwError.snippet,
      stack: pwError.stack,
      value: pwError.value,
    }
  }

  async onEnd(result: FullResult) {
    if (!this._config || !this._rootSuite)
      throw new Error('ERROR: failed to resolve config');
    const flakinessAccessToken = this._options.token ?? process.env['FLAKINESS_ACCESS_TOKEN'];
    if (!flakinessAccessToken) {
      console.log(`[flakiness.io] Uploading skipped since no FLAKINESS_ACCESS_TOKEN is specified`);
      return;
    }
    let commitId: FlakinessReport.CommitId;
    try {
      commitId = gitCommitInfo(this._config.rootDir);
    } catch (e) {
      console.log(`[flakiness.io] Uploading skipped since failed to get commit info - is this a git repo?`);
      return;
    }

    const gitRoot = normalizePath(computeGitRoot(this._config.rootDir));
    const configPath = this._config.configFile ? gitFilePath(gitRoot, normalizePath(this._config.configFile)) : undefined;

    const context: ProcessingContext = {
      project2environmentIdx: new Map(),
      testBaseDir: normalizePath(this._config.rootDir),
      gitRoot,
      attachments: new Map(),
      unaccessibleAttachmentPaths: [],
    };

    const environmentsMap = createEnvironments(this._config.projects);
    if (this._options.collectBrowserVersions) {
      try {
        // The process.argv[1] is the absolute path of the playwright executable than runs this custom
        // reporter. It also runs tests.
        // Unfortunately, we're not given the Playwright instance in the playwright api;
        // as a result, jump through a few hoops to get the instance.
        // 
        // 1. Resolve process.argv[1] to absolute path. This is a symlink that points to some file
        //    inside the @playwright/test node module.
        // 2. Go up until we reach the "test" directory.
        // 3. Playwright's main import is the 'index.js' file.
        let playwrightPath = fs.realpathSync(process.argv[1]);
        while (path.basename(playwrightPath) !== 'test')
          playwrightPath = path.dirname(playwrightPath);
        const module = await import(path.join(playwrightPath, 'index.js'));

        for (const [project, env] of environmentsMap) {
          const { browserName = 'chromium', channel, headless } = project.use;
  
          let browserType: BrowserType;
          switch (browserName) {
            case 'chromium': browserType = module.default.chromium; break;
            case 'firefox': browserType = module.default.firefox; break;
            case 'webkit': browserType = module.default.webkit; break;
            default: throw new Error(`Unsupported browser: ${browserName}`);
          }
  
          const browser = await browserType.launch({ channel, headless });
          const version = browser.version();
          await browser.close();
          env.userSuppliedData['browser'] = (channel ?? browserName).toLowerCase().trim() + ' ' + version;
        }
      } catch (e) {
        console.error('[flakiness.io] failed to resolve browser version', e);
      }
    }
    const environments = [...environmentsMap.values()];
    for (let envIdx = 0; envIdx < environments.length; ++envIdx)
      context.project2environmentIdx.set(this._config.projects[envIdx], envIdx);

    const relatedCommitIds = this._options.relatedCommitIds as FlakinessReport.CommitId[];
    const report = FK.dedupeSuitesTestsEnvironments({
      commitId: commitId,
      relatedCommitIds,
      configPath,
      url: inferRunUrl(),
      environments,
      suites: await this._toFKSuites(context, this._rootSuite),
      opaqueData: this._config,
      unattributedErrors: this._unattributedErrors.map(e => this._toFKTestError(context, e)),
      duration: parseDurationMS(result.duration),
      startTimestamp: +result.startTime as FK.UnixTimestampMS,
    });
    createTestStepSnippets(this._filepathToSteps);

    for (const unaccessibleAttachment of context.unaccessibleAttachmentPaths)
      console.warn(`[flakiness.io] WARN: cannot access attachment ${unaccessibleAttachment}`);

    const endpoint = this._options.endpoint ?? process.env['FLAKINESS_ENDPOINT'] ?? 'https://flakiness.io';
    const uploader = new ReportUploader({
      flakinessAccessToken,
      flakinessEndpoint: endpoint,
    });
    const upload = uploader.createUpload(report, [...context.attachments.values()]);
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      console.log(`[flakiness.io] X Failed to upload to ${endpoint}: ${uploadResult.message}`);
      return { status: 'failed' as const };
    }
    console.log(`[flakiness.io] ✓ Report uploaded ${uploadResult.message ?? ''}`);
    if (uploadResult.reportUrl)
      console.log(`[flakiness.io] ${uploadResult.reportUrl}`)
  }
}



function toSTDIOEntry(data: Buffer | string): FK.STDIOEntry {
  if (Buffer.isBuffer(data))
    return { buffer: data.toString('base64') };
  return { text: data };
}
