#!/usr/bin/env node
import { Command, Option } from 'commander';
import fs from 'fs/promises';
import path from 'path';
import { FlakinessReport } from '../common/flakinessReport.js';
import { PlaywrightJSONReport } from '../playwrightJSONReport.js';
import { ReportUploader } from '../reportUploader.js';

const program = new Command();

program
  .name('flakiness')
  .description('Flakiness CLI tool')
  .version('0.21.0'); // Using version from package.json

// Add the upload command
type OptAccessToken = { accessToken: string };
const optAccessToken = new Option('-t, --access-token <token>', 'A read-write flakiness.io access token')
  .makeOptionMandatory()
  .env('FLAKINESS_ACCESS_TOKEN');

type OptEndpoint = { endpoint: string };
const optEndpoint = new Option('-e, --endpoint <url>', 'An endpoint where the service is deployed')
  .default('https://flakiness.io')
  .env('FLAKINESS_ENDPOINT');

program.command('upload-playwright-json', { hidden: true })
  .description('Upload Playwright Test JSON report to the flakiness.io service')
  .argument('<relative-path-to-json>', 'Path to the Playwright JSON report file')
  .addOption(optAccessToken)
  .addOption(optEndpoint)
  .action(async (relativePath, options: OptAccessToken & OptEndpoint) => {
    const fullPath = path.resolve(relativePath);
    if (!(await fs.access(fullPath, fs.constants.F_OK).then(() => true).catch(() => false))) {
      console.error(`Error: path ${fullPath} is not accessible`);
      process.exit(1);
    }

    const text = await fs.readFile(fullPath, 'utf-8');
    const playwrightJson = JSON.parse(text);
    const { attachments, report, unaccessibleAttachmentPaths } = await PlaywrightJSONReport.parse(PlaywrightJSONReport.collectMetadata(), playwrightJson, {
      extractAttachments: true,
    });

    for (const unaccessibleAttachment of unaccessibleAttachmentPaths)
      console.warn(`WARN: cannot access attachment ${unaccessibleAttachment}`);

    const uploader = new ReportUploader({
      flakinessAccessToken: options.accessToken,
      flakinessEndpoint: options.endpoint,
    });

    const upload = uploader.createUpload(report, attachments);
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      console.log(`[flakiness.io] X Failed to upload to ${options.endpoint}: ${uploadResult.message}`);
    } else {
      console.log(`[flakiness.io] ✓ Report uploaded ${uploadResult.reportUrl ?? uploadResult.message ?? ''}`);
    }
  });

type OptAttachmentsDir = { attachmentsDir: string };
const optAttachmentsDir = new Option('--attachments-dir <dir>', 'Directory containing attachments to upload')
  .default('', 'Same directory as the report file');

type OptIgnoreMissingAttachments = { ignoreMissingAttachments: boolean };
const optIgnoreMissingAttachments = new Option('--ignore-missing-attachments', 'Upload report even if some attachments are missing.')
  .default('', 'Same directory as the report file');
  

program.command('upload')
  .description('Upload Flakiness report to the flakiness.io service')
  .argument('<relative-path>', 'Path to the Flakiness report file')
  .addOption(optAccessToken)
  .addOption(optEndpoint)
  .addOption(optAttachmentsDir)
  .addOption(optIgnoreMissingAttachments)
  .action(async (relativePath, options: OptAccessToken & OptEndpoint & OptAttachmentsDir & OptIgnoreMissingAttachments) => {
    const fullPath = path.resolve(relativePath);
    if (!(await fs.access(fullPath, fs.constants.F_OK).then(() => true).catch(() => false))) {
      console.error(`Error: path ${fullPath} is not accessible`);
      process.exit(1);
    }

    // Read all files from attachments directory.
    const attachmentsDir = options.attachmentsDir ?? path.dirname(fullPath);    
    const attachmentFiles = await listFilesRecursively(attachmentsDir);
    const attachmentIdToPath = new Map(attachmentFiles.map(file => [path.basename(file), file]));

    const text = await fs.readFile(fullPath, 'utf-8');
    const report = JSON.parse(text);
    const attachments: FlakinessReport.ExternalAttachment[] = [];
    const missingAttachments: FlakinessReport.Attachment[] = [];
    FlakinessReport.visitTests(report, test => {
      for (const run of test.runs) {
        for (const attempt of run.attempts) {
          for (const attachment of attempt.attachments ?? []) {
            const file = attachmentIdToPath.get(attachment.id);
            if (!file) {
              missingAttachments.push(attachment);
              continue;
            }
            attachments.push({
              contentType: attachment.contentType,
              id: attachment.id,
              path: file,
            });
          }
        }
      }
    });
    if (missingAttachments.length && !options.ignoreMissingAttachments) {
      console.log(`Missing ${missingAttachments.length} attachments - exiting. Use --ignore-missing-attachments to force upload.`);
      process.exit(1);
    }

    const uploader = new ReportUploader({
      flakinessAccessToken: options.accessToken,
      flakinessEndpoint: options.endpoint,
    });
    const upload = uploader.createUpload(report, attachments);
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      console.log(`[flakiness.io] X Failed to upload to ${options.endpoint}: ${uploadResult.message}`);
    } else {
      console.log(`[flakiness.io] ✓ Report uploaded ${uploadResult.reportUrl ?? uploadResult.message ?? ''}`);
    }
  });

// Parse arguments and execute the matching command
await program.parseAsync();

async function listFilesRecursively(dir: string, result: string[] = []): Promise<string[]> {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory())
      await listFilesRecursively(fullPath, result);
    else
      result.push(fullPath);
  }
  return result;
}
