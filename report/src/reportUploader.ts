import fs from 'fs';
import { URL } from 'url';
import { brotliCompressSync } from 'zlib';
import { FlakinessReport } from './common/flakinessReport.js';
import { brotliCompressAsync, httpUtils, retryWithBackoff } from './utils.js';

type ReportUploaderOptions = {
  flakinessEndpoint: string;
  flakinessAccessToken: string;
}

export class ReportUploader {
  private _options: ReportUploaderOptions;
  constructor(options: ReportUploaderOptions) {
    this._options = options;
  }

  createUpload(report: FlakinessReport.Report, attachments: FlakinessReport.ExternalAttachment[]) {
    const upload = new ReportUpload(this._options, report, attachments);
    return upload;
  }
}

const HTTP_BACKOFF = [100, 500, 1000, 1000, 1000, 1000];

type UploadOptions = {
  syncCompression?: boolean;
}

class ReportUpload {
  private _report: FlakinessReport.Report;
  private _attachments: FlakinessReport.ExternalAttachment[];
  private _options: ReportUploaderOptions;

  constructor(options: ReportUploaderOptions, report: FlakinessReport.Report, attachments: FlakinessReport.ExternalAttachment[]) {
    this._options = options;
    this._report = report;
    this._attachments = attachments;
  }

  async upload(options?: UploadOptions): Promise<{ success: boolean, message?: string, reportUrl?: string }> {
    const response = await httpUtils.postJSON(new URL('/api/run.startUpload', this._options.flakinessEndpoint).toString(), {
      flakinessAccessToken: this._options.flakinessAccessToken,
      attachmentIds: this._attachments.map(attachment => attachment.id),
    }, HTTP_BACKOFF).catch(e => ({ error: e }));
    if (response?.error)
      return { success: false, message: `flakiness.io returned error: ${response.error.message}`}

    await Promise.all([
      this._uploadReport(JSON.stringify(this._report), response.result.data.report_upload_url, options?.syncCompression ?? false),
      ...this._attachments.map(attachment => {
        const uploadURL = response.result.data.attachment_upload_urls[attachment.id];
        if (!uploadURL)
          throw new Error('Internal error: missing upload URL for attachment!');
        return this._uploadAttachment(attachment, uploadURL);
      }),
    ]);
  
    const response2 = await httpUtils.postJSON(new URL('/api/run.completeUpload', this._options.flakinessEndpoint).toString(), {
      upload_token: response.result.data.upload_token,
    }, HTTP_BACKOFF);

    const url = response2?.result.data.report_url ? new URL(response2?.result.data.report_url, this._options.flakinessEndpoint).toString() : undefined;
    return { success: true, reportUrl: url };
  }

  private async _uploadReport(data: Buffer|string, uploadUrl: string, syncCompression: boolean) {
    const compressed = syncCompression ? brotliCompressSync(data) : await brotliCompressAsync(data);
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(compressed) + '',
      'Content-Encoding': 'br',
    };
    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(compressed);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }

  private async _uploadAttachment(attachment: FlakinessReport.ExternalAttachment, uploadUrl: string) {
    const bytesLength = attachment.path ? (await fs.promises.stat(attachment.path)).size :
                        attachment.body ? Buffer.byteLength(attachment.body) : 0;
    const headers = {
      'Content-Type': attachment.contentType,
      'Content-Length': bytesLength + '',
    };

    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      if (attachment.path) {
        fs.createReadStream(attachment.path)
          .pipe(request);
      } else {
        if (attachment.body)
          request.write(attachment.body);
        request.end();
      }
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }
}
