/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

import { codeFrameColumns } from '@babel/code-frame';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import fs from 'fs';
import { FlakinessReport } from './common/flakinessReport.js';

export function createTestStepSnippets(filepathToSteps: Multimap<string, FlakinessReport.TestStep>) {
  for (const [filepath, steps] of filepathToSteps) {
    let source: string;
    try {
      source = fs.readFileSync(filepath, 'utf-8');
    } catch (e) {
      continue;
    }
    const lines = source.split('\n').length;
    const highlighted = codeFrameColumns(source, { start: { line: lines, column: 1 } }, { highlightCode: true, linesAbove: lines, linesBelow: 0 });
    const highlightedLines = highlighted.split('\n');
    const lineWithArrow = highlightedLines[highlightedLines.length - 1];
    for (const step of steps) {
      if (!step.location)
        continue;
      // Don't bother with snippets that have less than 3 lines.
      if (step.location.line < 2 || step.location.line >= lines)
        continue;
      // Cut out snippet.
      const snippetLines = highlightedLines.slice(step.location.line - 2, step.location.line + 1);
      // Relocate arrow.
      const index = lineWithArrow.indexOf('^');
      const shiftedArrow = lineWithArrow.slice(0, index) + ' '.repeat(step.location.column - 1) + lineWithArrow.slice(index);
      // Insert arrow line.
      snippetLines.splice(2, 0, shiftedArrow);
      step.snippet = snippetLines.join('\n');
    }
  }
}
